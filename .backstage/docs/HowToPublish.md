### Merge Request creation

On a merge request creation a series of CI jobs starts.
When the jobs are over you can trigger an additional job manually to create a review-app link, useful if you need to have a link to share pointing to a specific merge request or you can trigger the `pre-release` manual job to release an alpha version of a package to install and test in your environment.

### Changes to documentation

After you merge your changes in develop branch and the CI jobs ends successfully, every changes to documentation will be automatically reflected on the production link: https://getrix.pages.pepita.io/gx-design/

### Publish

To release an official tag of single or multiple packages in production you need to:

- Create a Merge Request from develop to master (usualy called: chore(publish): [changes details])
- Manually merge the Merge Request created
- Wait for pipeline to finish
- Manually trigger the `release` job on master
