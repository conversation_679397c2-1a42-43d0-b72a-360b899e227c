Here's a list of instructions to learn how to work on components, stories or documentation.

### Packages source code

The component source codes are stored `/packages/[packageName]` folder. Inside `/src` you can find the code to work on.
Remember to adjust tests accordingly (`/packages/[packageName]/[packageName.test.tsx]`) if you change component logic.

> :warning: The comments inside the component file are used by Storybook to render the documentation.

### Documentation

The stories and documentation about a single component and custom pages can be found inside the `/documentation` folder.
