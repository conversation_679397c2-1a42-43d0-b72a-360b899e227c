{"name": "gx-design", "description": "Collection of components used in Gx Design", "main": "index.js", "repository": "********************:getrix/gx-design.git", "license": "MIT", "workspaces": ["packages/*"], "dependencies": {"@gx-design/core": "workspace:^", "@gx-design/layout": "workspace:^", "@gx-design/typography": "workspace:^", "@gx-design/utilities": "workspace:^", "clsx": "1.1.1", "react-markdown": "^8.0.0"}, "devDependencies": {"@commitlint/cli": "^16.0.3", "@commitlint/config-conventional": "^16.0.0", "@pepita-tools/vitest-gitlab-reporter": "^2.0.4", "@storybook/addon-a11y": "8.6.12", "@storybook/addon-actions": "^8.6.12", "@storybook/addon-designs": "^8.2.1", "@storybook/addon-essentials": "^8.6.12", "@storybook/addon-links": "^8.6.12", "@storybook/addon-mdx-gfm": "^8.6.12", "@storybook/manager-api": "^8.6.12", "@storybook/react": "^8.6.12", "@storybook/react-vite": "^8.6.12", "@storybook/theming": "^8.6.12", "@testing-library/dom": "^10.4.0", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.2.0", "@testing-library/user-event": "^14.5.1", "@types/mdx": "^2.0.2", "@types/react": "^19.1.6", "@types/react-dom": "^19.1.6", "@typescript-eslint/parser": "^5.28.0", "@vitejs/plugin-react": "^4.3.4", "@vitest/coverage-istanbul": "^3.0.2", "@vitest/coverage-v8": "^3.0.2", "@vitest/ui": "^3.0.2", "babel-loader": "^8.2.3", "eslint": "^8.17.0", "eslint-config-prettier": "^8.5.0", "eslint-plugin-jsx-a11y": "^6.5.1", "eslint-plugin-prettier": "^4.0.0", "eslint-plugin-promise": "^6.0.0", "eslint-plugin-react": "^7.30.0", "eslint-plugin-react-hooks": "^4.5.0", "eslint-plugin-storybook": "^0.12.0", "eslint-plugin-testing-library": "^6.1.0", "eslint-plugin-vitest": "^0.3.2", "husky": "^7.0.0", "jsdom": "^26.0.0", "lerna": "^7.2.0", "lint-staged": "^11.0.0", "nx": "^15.1.0", "postcss-loader": "^7.3.3", "prettier": "^2.6.2", "react": "^19.1.0", "react-dom": "^19.1.0", "sass": "1.32.8", "sass-loader": "10.1.x", "storybook": "^8.6.12", "tsup": "^7.2.0", "typescript": "^5.6.2", "vite": "^5.4.14", "vitest": "^3.0.2"}, "volta": {"node": "20.11.0", "yarn": "3.8.0"}, "scripts": {"storybook": "storybook dev -p 6006", "lint": "eslint --fix ./documentation/components/**/*.{tsx,ts,js} ./packages/**/src/*.{tsx,ts,js}", "build-storybook": "storybook build", "build": "lerna run build", "build-publish": "lerna run build --since --include-dependencies", "release": "lerna publish --conventional-graduate --yes", "next": "lerna publish --conventional-prerelease --yes", "test": "vitest"}, "packageManager": "yarn@3.8.0"}