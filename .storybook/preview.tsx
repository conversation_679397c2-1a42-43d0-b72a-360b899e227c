import React from "react";
import DocumentationTemplate from "./DocumentationTemplate.mdx";
import "./scss/getrix.scss";
// import getrix from '!!style-loader?injectType=lazyStyleTag!css-loader?url=false!sass-loader!./scss/getrix.scss';
// import immoPro from '!!style-loader?injectType=lazyStyleTag!css-loader?url=false!sass-loader!./scss/immobiliare-pro.scss';

export const parameters = {
  // product: {
  //   themes: {
  //     getrix,
  //     immoPro
  //   }
  // },
  // theme: 'getrix',
  docs: {
    page: DocumentationTemplate,
    source: { type: "dynamic" },
  },
  options: {
    storySort: {
      order: [
        "Welcome",
        "Changelog",
        "Foundation",
        ["Tokens", "Typography", "Shadows", "Icons"],
        "components",
      ],
    },
  },
};
export const tags = ["autodocs"];
