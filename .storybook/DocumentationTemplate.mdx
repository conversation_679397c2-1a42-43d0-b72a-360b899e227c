import {
  Meta,
  Title,
  Subtitle,
  Description,
  Primary,
  ArgTypes,
  Stories,
  Unstyled,
} from "@storybook/blocks";
import { ComponentHeader } from "./blocks/header/ComponentHeader";
import { ComponentStart } from "./blocks/start/ComponentStart";
import { ComponentAnatomy } from "./blocks/anatomy/ComponentAnatomy";
import { ComponentExternalLinks } from "./blocks/external-links/ComponentExternalLinks";

<Meta isTemplate />
<Unstyled>
  <div className="source-styled">
    <ComponentHeader />
    <ComponentExternalLinks />
    <ComponentAnatomy />
    <ComponentStart />
    <h2 className="gx-title-1 component-doc-heading">Example</h2>
    <Primary />
    <h2 className="gx-title-1 component-doc-heading">Props</h2>
    <ArgTypes />
    <h2 className="gx-title-1 component-doc-heading">Variations</h2>
    <div className="doc-stories-wrapper">
      <Stories title="" />
    </div>
  </div>
</Unstyled>
