import React from "react";
import { Icon } from "@gx-design/icon/src/Icon";
import { Tag } from "@gx-design/icon/src/Icon";
import { Gap } from "./components/Gap.jsx";
import { useStorybookApi } from "@storybook/manager-api";

const renderIcon = (item) => {
  const { type, name, parent, depth } = item;
  switch (type) {
    case "docs": {
      if (item.name === "Welcome") {
        return <Icon name="home" />;
      }

      return (
        <>
          {depth > 1 && <Gap size={depth * 16} />}
          <Icon name="doc-word" />
        </>
      );
    }

    case "component": {
      if (parent && parent.includes("components-")) {
        return (
          <>
            <Gap size={depth * 16} />
            <Icon name="layers" />
          </>
        );
      }

      return <Icon name="layers" />;
    }

    case "story": {
      return (
        <>
          {depth > 1 && <Gap size={depth * 16} />}
          {depth > 1 ? <Icon name="layers" /> : <Icon name="layers" />}
        </>
      );
    }

    case "group": {
      return (
        <>
          {depth > 1 && <Gap size={depth * 16} />}
          {depth > 1 ? <Icon name="list" /> : <Icon name="list" />}
        </>
      );
    }

    default:
      return null;
  }
};

export const renderLabel = (item) => {
  return (
    <span className="item-label">
      {renderIcon(item)}
      <span>{item.name}</span>
    </span>
  );
};
