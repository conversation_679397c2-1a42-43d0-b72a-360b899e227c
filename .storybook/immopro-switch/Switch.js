// OutlineSelector.js 

import React, { useState, useCallback, useEffect } from 'react';
import { useChannel } from "@storybook/api";
import { styled } from "@storybook/theming";

const StyledSwitch = styled.div({
  padding: '10px',
  display: "flex",
});

const Divider = styled.span({
  width: "1px",
  height: "20px",
  background: "rgba(0,0,0,.1)",
  marginTop: "10px",
  marginLeft: "6px",
  marginRight: "2px",
})

const GetrixLabel = styled.div(props => ({
  padding: '8px',
  backgroundColor: props.active ? '#428CC6' : '#dfdfdf',
  fontSize: '12px',
  color: props.active ? '#ffffff' : '#505050',
  display: 'flex',
  alignItems: 'center',
  justifyContent: 'center',
  borderRadius: '3px',
  fontWeight: props.active ? '700' : '400',
  cursor: 'pointer',
  borderTopRightRadius: '0',
  borderBottomRightRadius: '0',
}))


const ImmobiliareProLabel = styled.div(props => ({
  padding: '8px',
  backgroundColor: props.active ? '#C12400' : '#dfdfdf',
  fontSize: '12px',
  color: props.active ? '#ffffff' : '#505050',
  display: 'flex',
  alignItems: 'center',
  justifyContent: 'center',
  borderRadius: '3px',
  fontWeight: props.active ? '700' : '400',
  cursor: 'pointer',
  borderTopLeftRadius: '0',
  borderBottomLeftRadius: '0',
}))


export const Switch = () => {
  const [currentTheme, setCurrentTheme] = useState('getrix');
  const currentThemeStorage = sessionStorage.getItem('productTheme');
  const emit = useChannel({});

  useEffect(() => {
    if (currentThemeStorage) {
      setCurrentTheme(currentThemeStorage);
    }
  }, [])

  const toggleTheme = (theme) => {
    setCurrentTheme(theme);
    emit('themeChange', theme)
  }

  return (
    <>
      <Divider />
      <StyledSwitch>
        <GetrixLabel active={currentTheme === 'getrix'} onClick={() => toggleTheme('getrix')}>Getrix</GetrixLabel>
        <ImmobiliareProLabel active={currentTheme === 'immoPro'} onClick={() => toggleTheme('immoPro')}>Immobiliare Pro</ImmobiliareProLabel>
      </StyledSwitch>
    </>
  );
};