import addons, { makeDecorator } from '@storybook/addons';

let currentCSS = null;

async function addBrandStyles(id, cssFiles) {
  const file = cssFiles[id];
  if (file) {
    file.use();

    // If we've got a CSS file in use, turn it off
    if (currentCSS) {
      currentCSS.unuse();
    }

    currentCSS = file;
  }
}

export var withGlobals = makeDecorator({
  name: 'immoTheme',
  parameterName: 'product',
  wrapper: function wrapper(getStory, context, { parameters }) {
    const { themes } = parameters;
    const { theme } = context.parameters;
    const channel = addons.getChannel();
    const currentTheme = sessionStorage.getItem('productTheme');

    if (currentTheme) {
      addBrandStyles(currentTheme, themes);
    } else {
      addBrandStyles(theme, themes);
    }
    
    channel.on('themeChange', (id) => {
      addBrandStyles(id, themes)
      sessionStorage.setItem('productTheme', id);
    });
    return getStory(context);
  }
});
