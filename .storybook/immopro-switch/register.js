import React from 'react';
import { addons, types } from '@storybook/addons';
import { Switch } from './Switch';

const ADDON_NAME = 'ImmoProSwitch'

const EXCLUDED_PAGES_ID = [
  'welcome--page',
  'change-log--page',
  'foundation-shadows--page',
  'foundation-shadows--page',
  'foundation-icons--page',
  'foundation-typography--page'
];

// Register the addon
addons.register(ADDON_NAME, () => {
  // Register the tool
  addons.add(ADDON_NAME, {
    title: 'ImmoPro Switch',
    type: types.TOOL,
    match: (props) => {
      // Don't render the switch on a set of pages defined before
      const show = EXCLUDED_PAGES_ID.every((item) => item !== props.storyId);
      return show;
    },
    render: () => <Switch />,
  });

});