import { dirname, join } from "path";

/**
 * This function is used to resolve the absolute path of a package.
 * It is needed in projects that use Yarn PnP or are set up within a monorepo.
 */
function getAbsolutePath(value) {
  return dirname(require.resolve(join(value, "package.json")));
}

/** @type { import('@storybook/react-vite').StorybookConfig } */
export default {
  core: {
    disableTelemetry: true, // 👈 Disables telemetry
  },
  stories: [
    "../documentation/**/*.stories.mdx",
    "../documentation/**/*.mdx",
    "../documentation/**/*.stories.@(js|jsx|ts|tsx)",
  ],
  addons: [
    getAbsolutePath("@storybook/addon-links"),
    getAbsolutePath("@storybook/addon-essentials"),
    getAbsolutePath("@storybook/addon-mdx-gfm"),
    getAbsolutePath("@storybook/addon-designs"),
    getAbsolutePath("@storybook/addon-a11y")
  ],
  typescript: {
    reactDocgen: "react-docgen-typescript",
    // skipBabel: true,
    // check: false,
  },
  framework: {
    name: getAbsolutePath("@storybook/react-vite"),
    options: {},
  },
  docs: {},
  staticDirs: ["../assets"],
};
