import React from "react";
import { useOf } from "@storybook/blocks";
import "./anatomy.scss";

export const ComponentAnatomy = ({ of }) => {
  const resolvedOf = useOf(of || "meta");
  if (
    resolvedOf.type !== "meta" ||
    !resolvedOf.preparedMeta.parameters.componentAnatomy
  )
    return null;
  return (
    <div className="sb-component-anatomy">
      <h2 className="gx-title-1 component-doc-heading">Anatomy</h2>
      <div className="sb-component-anatomy__image">
        <img
          srcSet={resolvedOf.preparedMeta.parameters.componentAnatomy.image + ' 2x'}
          alt="Anatomy"
        />
      </div>
      <div className="sb-component-anatomy__legend">
        {resolvedOf.preparedMeta.parameters.componentAnatomy.legend.map(
          (point, index) => {
            return (
              <div className="sb-component-anatomy__legendItem">
                <div className="sb-component-anatomy__legendItemNumber">
                  {index + 1}
                </div>
                <span>{point}</span>
              </div>
            );
          }
        )}
      </div>
    </div>
  );
};
