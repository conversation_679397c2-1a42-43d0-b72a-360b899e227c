@use "@gx-design/theme/styles" as *;

.sb-component-anatomy {
  &__image {
    margin-top: 2rem;
    background-color: #f2f2f2;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 8rem 1.6rem;
    border-radius: 4px;

    img {
      max-width: 100%;
    }
  }

  &__legend {
    display: flex;
    flex-wrap: wrap;
    margin: 4rem 0;

    &Item {
      display: flex;
      align-items: center;
      margin-right: 6rem;
      margin-bottom: space(md);

      &Number {
        margin-right: 0.8rem;
        background-color: #c54600;
        width: 2rem;
        height: 2rem;
        display: flex;
        align-items: center;
        justify-content: center;
        font-weight: 400;
        font-size: 1.2rem;
        color: color(content-notification);
        border-radius: 50%;
      }
    }
  }
}
