import { useOf, Source } from "@storybook/blocks";

/**
 * A block that displays the story name or title from the of prop
 * - if a story reference is passed, it renders the story name
 * - if a meta reference is passed, it renders the stories' title
 * - if nothing is s, it defaults to the primary story
 */
export const ComponentStart = ({ of }) => {
  const resolvedOf = useOf(of || "meta", ["story", "meta"]);
  switch (resolvedOf.type) {
    case "meta": {
      const subcomponents = resolvedOf.preparedMeta.subcomponents;
      return (
        <div className="storybook-getting-started">
          <h2 className="gx-title-1 component-doc-heading">Getting started</h2>
          <h4>Installing the package</h4>
          <Source
            language="bash"
            dark={true}
            code={`yarn add ${resolvedOf.preparedMeta.parameters.packageName}`}
          />
          <h4>Import the component</h4>
          <Source
            language="tsx"
            dark={true}
            code={`import { ${resolvedOf.preparedMeta.parameters.componentImport} } from '${resolvedOf.preparedMeta.parameters.packageName}'`}
          />
          <h4>Import the styles</h4>
          <Source
            language="css"
            dark={true}
            code={`@include meta.load-css('${
              resolvedOf.preparedMeta.parameters.styles ??
              resolvedOf.preparedMeta.parameters.packageName
            }/styles');`}
          />
        </div>
      );
    }
  }
  return null;
};
