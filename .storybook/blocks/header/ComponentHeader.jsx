import { useOf, Description } from "@storybook/blocks";
import { Tag } from "@gx-design/tag/src/Tag";
/**
 * A block that displays the story name or title from the of prop
 * - if a story reference is passed, it renders the story name
 * - if a meta reference is passed, it renders the stories' title
 * - if nothing is s, it defaults to the primary story
 */
export const ComponentHeader = ({ of }) => {
  const resolvedOf = useOf(of || "meta", ["story", "meta"]);
  switch (resolvedOf.type) {
    case "meta": {
      return (
        <>
          <Tag
            style="info"
            className="component-header-tag"
            text={resolvedOf.preparedMeta.parameters.componentSubtitle}
          />
          <h1 className="gx-display-2">
            {resolvedOf.preparedMeta.parameters.componentTitle}
            <Tag
              className="component-header-version"
              style="positive"
              text={resolvedOf.preparedMeta.parameters.version}
            />
          </h1>
          <Description />
        </>
      );
    }
  }
  return null;
};
