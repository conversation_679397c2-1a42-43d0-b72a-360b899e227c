import React from "react";
import { useOf } from "@storybook/blocks";
import { Button } from "@gx-design/button/src/Button";
import configFigmaLinks from "./configFigmaLinks.json";
import "./external-links.scss";

export const ComponentExternalLinks= ({ of }) => {
  const resolvedOf = useOf(of || "meta");
  if (
    resolvedOf.type !== "meta" ||
    !resolvedOf.preparedMeta.parameters.componentTitle
  )
    return null;

    const figmaLink = configFigmaLinks.components[resolvedOf.preparedMeta.parameters.componentTitle];

    const handleClick = (url) => {window.open(url, '_blank')}

  return (
    <div className="sb-component-externalLinks">
      <Button size="small" disabled={!figmaLink ? true : false} onClick={figmaLink && (() => handleClick(figmaLink))}>
        <img src="images/icons/figma.svg" />
        <span>Figma</span>
      </Button>
      <Button size="small" onClick={() => handleClick(`https://gitlab.pepita.io/getrix/gx-design/-/tree/develop/packages/${resolvedOf.preparedMeta.parameters.componentTitle}`)}>
      <img src="images/icons/gitlab.svg" />
        <span>GitLab</span>
      </Button>
    </div>
  );
};
