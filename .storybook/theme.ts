import { create } from "@storybook/theming/create";

export default create({
  base: "light",
  brandTitle: "GX Design System",
  brandImage: "images/gx-design.png",
  fontBase: '"Inter", sans-serif',

  // Base Colors
  colorPrimary: "#428CC6",
  colorSecondary: "#428CC6",
  // UI
  appBg: "#F5F5F5",
  appContentBg: "#FAFAF8",
  appBorderRadius: 3,

  // Text colors
  textColor: "#505050",

  // Toolbar default and active colors
  barTextColor: "#9E9E9E",
  barSelectedColor: "#585C6D",
  barBg: "#ffffff",

  // Form colors
  inputBg: "#FFFFFF",
  inputBorder: "#D5D5D5",
  inputTextColor: "#505050",
  inputBorderRadius: 3,
});
