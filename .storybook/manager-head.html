<link rel="preconnect" href="https://fonts.googleapis.com" />
<link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
<link
  href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap"
  rel="stylesheet"
/>
<style>
  html,
  body {
    font-family: "Inter", "Helvetica Neue", Helvetica, Arial, sans-serif !important;
    font-size: 62.5% !important;
  }

  img[alt="GX Design System"] {
    max-width: 150px;
  }

  #storybook-docs p {
    font-size: 1.6rem;
  }

  .sidebar-container .sidebar-item svg:not(.item-label svg) {
    display: none;
  }

  .sidebar-item > a {
    font-size: 14px;
    padding: 6px 4px;
    border-radius: 4px;
    cursor: pointer;
  }
  .sidebar-item > a:nth-child(2) {
    display: none;
  }

  .sidebar-item > a:hover,
  .sidebar-item[data-selected="true"] > a {
    background-color: #e6edf5;
    color: #428cc6;
  }

  .sidebar-item[data-selected="true"] > a .item-label svg {
    fill: #428cc6;
  }

  .sidebar-item > span:first-child {
    display: none;
  }

  .sidebar-container button.sidebar-item {
    width: 100%;
    padding: 6px 4px;
  }

  .item-label {
    display: flex;
    align-items: center;
  }

  .sidebar-subheading button > span:first-child {
    display: none;
  }

  .sidebar-container .sidebar-item {
    position: relative;
    padding: 0;
    color: #373c47;
    font-weight: 400;
    font-size: 14px;
    line-height: 20px;
    background: transparent !important;
    width: auto;
    align-items: center;
    overflow: hidden;
  }

  #storybook-explorer-tree div:first-child {
    margin-left: 0;
    margin-right: 0;
  }

  .sidebar-container .sidebar-item > div {
    display: none;
  }

  .item-label svg {
    width: 18px;
    height: 18px;
    margin-right: 0.8rem;
    fill: #919191;
  }
</style>
