@use "sass:meta";

// GX Design System

//Reset
@include meta.load-css("@gx-design/core/reset");

// General styles
@include meta.load-css("@gx-design/layout/styles");
@include meta.load-css("@gx-design/typography/styles");
@include meta.load-css("@gx-design/tools/styles");
@include meta.load-css("@gx-design/theme/custom-properties");
@include meta.load-css("@gx-design/utilities/styles");

// Gx design components
@include meta.load-css("@gx-design/icon/styles");
@include meta.load-css("@gx-design/accordion/styles");
@include meta.load-css("@gx-design/action-list/styles");
@include meta.load-css("@gx-design/alert/styles");
@include meta.load-css("@gx-design/avatar/styles");
@include meta.load-css("@gx-design/badge/styles");
@include meta.load-css("@gx-design/button/styles");
@include meta.load-css("@gx-design/dropdown/styles");
@include meta.load-css("@gx-design/empty-state/styles");
@include meta.load-css("@gx-design/energetic-tag/styles");
@include meta.load-css("@gx-design/helper-text/styles");
@include meta.load-css("@gx-design/list/styles");
@include meta.load-css("@gx-design/modal/styles");
@include meta.load-css("@gx-design/notification-badge/styles");
@include meta.load-css("@gx-design/pager/styles");
@include meta.load-css("@gx-design/pagination-bar/styles");
@include meta.load-css("@gx-design/popover/styles");
@include meta.load-css("@gx-design/summary-item/styles");
@include meta.load-css("@gx-design/tabs/styles");
@include meta.load-css("@gx-design/tag/styles");
@include meta.load-css("@gx-design/tooltip/styles");
@include meta.load-css("@gx-design/input/styles");
@include meta.load-css("@gx-design/snackbar/styles");
@include meta.load-css("@gx-design/checkbox/styles");
@include meta.load-css("@gx-design/toggle/styles");
@include meta.load-css("@gx-design/icon-input/styles");
@include meta.load-css("@gx-design/radio/styles");
@include meta.load-css("@gx-design/select/styles");
@include meta.load-css("@gx-design/textarea/styles");
@include meta.load-css("@gx-design/toolbar/styles");
@include meta.load-css("@gx-design/tooltip/styles");
@include meta.load-css("@gx-design/label/styles");
@include meta.load-css("@gx-design/character-counter/styles");
@include meta.load-css("@gx-design/loader/styles");
@include meta.load-css("@gx-design/table-data/styles");

// Storybook styles
@include meta.load-css("../../documentation/pages/intro");
@include meta.load-css("../../documentation/layout/Hidden/Hidden");

.storybook-getting-started {
  h4 {
    margin-bottom: 0;

    &:first-of-type {
      margin-top: 2rem;
    }
  }

  .docblock-source {
    margin: 0.5rem 0 2rem;
  }
}

.sidebar-item {
  & > div {
    display: none;
  }
}
