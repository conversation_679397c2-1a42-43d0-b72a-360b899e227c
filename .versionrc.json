{"tagPrefix": "v", "name": "conventionalcommits", "owner": "getrix", "repository": "gx-design", "repoUrl": "https://gitlab.pepita.io/getrix/gx-design", "types": [{"type": "feat", "section": "Features"}, {"type": "fix", "section": "Bug Fixes"}, {"type": "ci", "section": "CI/CD"}, {"type": "refactor", "section": "Refa<PERSON>"}, {"type": "perf", "section": "Performance"}, {"type": "docs", "section": "Documentation"}, {"type": "test", "section": "Test"}, {"type": "chore", "hidden": true}, {"type": "style", "hidden": true}], "commitUrlFormat": "https://gitlab.pepita.io/getrix/gx-design/commit/{{hash}}", "compareUrlFormat": "https://gitlab.pepita.io/getrix/gx-design/compare/{{previousTag}}...{{currentTag}}", "issueUrlFormat": "https://gitlab.pepita.io/getrix/gx-design/issues/{{id}}", "userUrlFormat": "https://gitlab.pepita.io/{{user}}"}