@use "@gx-design/theme/styles" as *;

.table-hidden {
  margin-bottom: space(xl) !important;
  border: 1px solid color(border-main);
  border-radius: 4px;

  td,
  th {
    text-align: center;
    width: 15%;
    max-width: 15.6rem;
    padding: 1rem;

    &:first-child {
      text-align: left;
      width: auto;
      max-width: inherit;
    }
  }

  tr:nth-of-type(2n) {
    background-color: color(background-alt);
  }

  tbody tr:hover {
    background-color: color(background-selected);

    .cell-hidden {
      background-color: color(content-error);
      color: color(content-reversed) !important;
    }

    .cell-visible {
      background-color: color(content-success);
      color: color(content-reversed) !important;
    }
  }

  small {
    display: block;
    font-weight: normal;
  }
}

.cell-hidden {
  background-color: color(background-error);
  color: color(content-error) !important;
}

.cell-visible {
  background-color: color(background-success);
  color: color(content-success) !important;
}

.table-hidden-classes {
  tr {
    + tr {
      border-top: 1px solid color(border-main);
    }
  }

  td {
    padding: space(md) space(sm);

    &:first-child {
      width: 50%;
    }
  }

  code {
    @include typography(body-small);
  }

  .gx-tag {
    width: 16rem !important;

    & + .gx-tag {
      margin-left: 0;
    }
  }
}

.hidden-resolution {
  justify-content: center;
  display: flex;
  padding: space(md);
  background-color: color(background-alt);
  @include typography(body);

  > div + div {
    margin-left: space(sm);
  }

  &__xs,
  &__sm,
  &__md,
  &__lg,
  &__xlg {
    display: none;
  }

  @media only screen and (max-width: 767px) {
    &__xs {
      display: inline-flex;
    }
  }
  @media only screen and (min-width: 768px) and (max-width: 1023px) {
    &__sm {
      display: inline-flex;
    }
  }
  @media only screen and (min-width: 1024px) and (max-width: 1279px) {
    &__md {
      display: inline-flex;
    }
  }
  @media only screen and (min-width: 1280px) and (max-width: 1439px) {
    &__lg {
      display: inline-flex;
    }
  }
  @media only screen and (min-width: 1440px) {
    &__xlg {
      display: inline-flex;
    }
  }
}

[class*="hidden-resolution__"] {
  align-items: center;
  background-color: color(background-reversed);
  padding: 0 space(sm);
  border-radius: radius(md);
  color: color(content-reversed);
  font-size: 1.2rem;
  font-family: Menlo, Monaco, Consolas, "Courier New", monospace;
}
