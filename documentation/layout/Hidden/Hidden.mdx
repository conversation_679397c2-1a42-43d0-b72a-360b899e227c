import { Meta, Unstyled } from "@storybook/blocks";
import { Input } from "@gx-design/input";
import { Tag } from "@gx-design/tag";

<Meta title="Layout/Hidden" />

<Unstyled>
<div className="sb-documentation">
<h1 className="gx-display-2">Hidden classes</h1>

Sometimes, on older sections, we need to hide elements only on certain resolutions.
We use some specific classes to do this.

<h2 className="component-doc-heading">Base hidden classes</h2>

Below there are the classes needed to hide an element on different resolutions.

<table className="table-hidden">
  <thead>
    <tr>
      <th>className</th>
      <th>
        <code>xsm</code>
        <small>&#60;768px</small>
      </th>
      <th>
        <code>sm</code>
        <small>≥768px &#60;1024px</small>
      </th>
      <th>
        <code>md</code>
        <small>≥1024px &#60;1280px</small>
      </th>
      <th>
        <code>lg</code>
        <small>≥1280px &#60;1440px</small>
      </th>
      <th>
        <code>xlg</code>
        <small>≥1440px</small>
      </th>
    </tr>
  </thead>
  <tbody>
    <tr>
      <td>
        <code>.gx-is-hidden</code>
      </td>
      <td className="cell-hidden">Hidden</td>
      <td className="cell-hidden">Hidden</td>
      <td className="cell-hidden">Hidden</td>
      <td className="cell-hidden">Hidden</td>
      <td className="cell-hidden">Hidden</td>
    </tr>
    <tr>
      <td>
        <code>.gx-is-hidden-sm-up</code>
      </td>
      <td className="cell-visible">Visible</td>
      <td className="cell-hidden">Hidden</td>
      <td className="cell-hidden">Hidden</td>
      <td className="cell-hidden">Hidden</td>
      <td className="cell-hidden">Hidden</td>
    </tr>
    <tr>
      <td>
        <code>.gx-is-hidden-md-up</code>
      </td>
      <td className="cell-visible">Visible</td>
      <td className="cell-visible">Visible</td>
      <td className="cell-hidden">Hidden</td>
      <td className="cell-hidden">Hidden</td>
      <td className="cell-hidden">Hidden</td>
    </tr>
    <tr>
      <td>
        <code>.gx-is-hidden-lg-up</code>
      </td>
      <td className="cell-visible">Visible</td>
      <td className="cell-visible">Visible</td>
      <td className="cell-visible">Visible</td>
      <td className="cell-hidden">Hidden</td>
      <td className="cell-hidden">Hidden</td>
    </tr>
    <tr>
      <td>
        <code>.gx-is-hidden-xlg-up</code>
      </td>
      <td className="cell-visible">Visible</td>
      <td className="cell-visible">Visible</td>
      <td className="cell-visible">Visible</td>
      <td className="cell-visible">Visible</td>
      <td className="cell-hidden">Hidden</td>
    </tr>
    <tr>
      <td>
        <code>.gx-is-hidden-xsm-down</code>
      </td>
      <td className="cell-hidden">Hidden</td>
      <td className="cell-visible">Visible</td>
      <td className="cell-visible">Visible</td>
      <td className="cell-visible">Visible</td>
      <td className="cell-visible">Visible</td>
    </tr>
    <tr>
      <td>
        <code>.gx-is-hidden-sm-down</code>
      </td>
      <td className="cell-hidden">Hidden</td>
      <td className="cell-hidden">Hidden</td>
      <td className="cell-visible">Visible</td>
      <td className="cell-visible">Visible</td>
      <td className="cell-visible">Visible</td>
    </tr>
    <tr>
      <td>
        <code>.gx-is-hidden-md-down</code>
      </td>
      <td className="cell-hidden">Hidden</td>
      <td className="cell-hidden">Hidden</td>
      <td className="cell-hidden">Hidden</td>
      <td className="cell-visible">Visible</td>
      <td className="cell-visible">Visible</td>
    </tr>
    <tr>
      <td>
        <code>.gx-is-hidden-lg-down</code>
      </td>
      <td className="cell-hidden">Hidden</td>
      <td className="cell-hidden">Hidden</td>
      <td className="cell-hidden">Hidden</td>
      <td className="cell-hidden">Hidden</td>
      <td className="cell-visible">Visible</td>
    </tr>
  </tbody>
</table>

<h2 className="component-doc-heading">Examples</h2>

Some examples of how these classes work.

<div className="hidden-resolution">
  <div>Actual resolution:</div>
  <div className="hidden-resolution__xs">&#60;768px</div>
  <div className="hidden-resolution__sm">≥768px &#60;1024px</div>
  <div className="hidden-resolution__md">≥1024px &#60;1280px</div>
  <div className="hidden-resolution__lg">≥1280px &#60;1440px</div>
  <div className="hidden-resolution__xlg">≥1440px</div>
</div>
<table className="table-hidden-classes">
  <tbody>
    <tr>
      <td>
        <code>.gx-is-hidden</code>
      </td>
      <td>
        <Tag text="Hidden" variant="negative" />
      </td>
    </tr>
    <tr>
      <td>
        <code>.gx-is-hidden-sm-up</code>
      </td>
      <td>
        <Tag className="gx-is-hidden-sm-up" text="Visible" style="positive" />
        <Tag className="gx-is-hidden-xsm-down" text="Hidden" style="negative" />
      </td>
    </tr>
    <tr>
      <td>
        <code>.gx-is-hidden-md-up</code>
      </td>
      <td>
        <Tag className="gx-is-hidden-md-up" text="Visible" style="positive" />
        <Tag className="gx-is-hidden-sm-down" text="Hidden" style="negative" />
      </td>
    </tr>
    <tr>
      <td>
        <code>.gx-is-hidden-lg-up</code>
      </td>
      <td>
        <Tag className="gx-is-hidden-lg-up" text="Visible" style="positive" />
        <Tag className="gx-is-hidden-md-down" text="Hidden" style="negative" />
      </td>
    </tr>
    <tr>
      <td>
        <code>.gx-is-hidden-xlg-up</code>
      </td>
      <td>
        <Tag className="gx-is-hidden-xlg-up" text="Visible" style="positive" />
        <Tag className="gx-is-hidden-lg-down" text="Hidden" style="negative" />
      </td>
    </tr>
    <tr>
      <td>
        <code>.gx-is-hidden-xsm-down</code>
      </td>
      <td>
        <Tag
          className="gx-is-hidden-xsm-down"
          text="Visible"
          style="positive"
        />
        <Tag className="gx-is-hidden-sm-up" text="Hidden" style="negative" />
      </td>
    </tr>
    <tr>
      <td>
        <code>.gx-is-hidden-sm-down</code>
      </td>
      <td>
        <Tag className="gx-is-hidden-sm-down" text="Visible" style="positive" />
        <Tag className="gx-is-hidden-md-up" text="Hidden" style="negative" />
      </td>
    </tr>
    <tr>
      <td>
        <code>.gx-is-hidden-md-down</code>
      </td>
      <td>
        <Tag className="gx-is-hidden-md-down" text="Visible" style="positive" />
        <Tag className="gx-is-hidden-lg-up" text="Hidden" style="negative" />
      </td>
    </tr>
    <tr>
      <td>
        <code>.gx-is-hidden-lg-down</code>
      </td>
      <td>
        <Tag className="gx-is-hidden-lg-down" text="Visible" style="positive" />
        <Tag className="gx-is-hidden-xlg-up" text="Hidden" style="negative" />
      </td>
    </tr>
  </tbody>
</table>

<h2 className="component-doc-heading">Screen reader only</h2>

We have a class <code>.gx-sr-only</code> used to hide information intended only for screen readers from the layout of the rendered page.

<table className="table-hidden">
  <thead>
    <tr>
      <th>className</th>
      <th>
        <code>xsm</code>
        <small>&#60;768px</small>
      </th>
      <th>
        <code>sm</code>
        <small>≥768px &#60;1024px</small>
      </th>
      <th>
        <code>md</code>
        <small>≥1024px &#60;1280px</small>
      </th>
      <th>
        <code>lg</code>
        <small>≥1280px &#60;1440px</small>
      </th>
      <th>
        <code>xlg</code>
        <small>≥1440px</small>
      </th>
    </tr>
  </thead>
  <tbody>
    <tr>
      <td>
        <code>.gx-sr-only</code>
      </td>
      <td className="cell-hidden">Hidden</td>
      <td className="cell-hidden">Hidden</td>
      <td className="cell-hidden">Hidden</td>
      <td className="cell-hidden">Hidden</td>
      <td className="cell-hidden">Hidden</td>
    </tr>
  </tbody>
</table>

</div>
</Unstyled>
