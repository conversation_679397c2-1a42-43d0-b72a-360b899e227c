import { Meta, Unstyled, Source } from "@storybook/blocks";
import "./Grid.scss";

<Meta title="Layout/Grid" />

<Unstyled>
<div className="sb-documentation source-styled">
<h1 className="gx-display-2">Grid</h1>

Gx Design's grid system uses rows and columns to layout and align content. It’s built with flexbox and is fully responsive.
By default, the grid system is based on 12 columns and divided in 5 breakpoint: <strong>xs, sm, md, lg, xlg</strong>.

<h2 className="component-doc-heading">Base rules</h2>

- Every set of columns must be wrapped in a `.gx-row` element to ensure correct positioning.
- When adding classes to columns ensure you start with the lowest breakpoint available (eg. gx-col-xs-[number-of-columns]).
- Breakpoints are designed to work with `min-width` so when you decide number of columns it will be effective from that breakpoint and up.

<h2 className="component-doc-heading">3 columns layout</h2>

Here we are using 3 columns layout

<div className="sb-grid-container">
  <div className="gx-row">
    <div className="gx-col-md-4 gx-col-sm-12 gx-col-xs-12">
      <div className="gx-box-row example-grid-box">
        <span className="gx-is-hidden-sm-up">gx-col-xs-12</span>
        <span className="gx-is-hidden-xsm-down">gx-col-sm-12</span>
        <span className="gx-is-hidden-sm-down">gx-col-md-4</span>
      </div>
    </div>
    <div className="gx-col-md-4 gx-col-sm-12 gx-col-xs-12">
      <div className="gx-box-row example-grid-box">
        <span className="gx-is-hidden-sm-up">gx-col-xs-12</span>
        <span className="gx-is-hidden-xsm-down">gx-col-sm-12</span>
        <span className="gx-is-hidden-sm-down">gx-col-md-4</span>
      </div>
    </div>
    <div className="gx-col-md-4 gx-col-sm-12 gx-col-xs-12">
      <div className="gx-box-row example-grid-box">
        <span className="gx-is-hidden-sm-up">gx-col-xs-12</span>
        <span className="gx-is-hidden-xsm-down">gx-col-sm-12</span>
        <span className="gx-is-hidden-sm-down">gx-col-md-4</span>
      </div>
    </div>
  </div>
</div>

<Source
  dark
  language="html"
  code={`
<div className="gx-row">
  <div className="gx-col-xs-12 gx-col-sm-12 gx-col-md-4"></div>
  <div className="gx-col-xs-12 gx-col-sm-12 gx-col-md-4"></div>
  <div className="gx-col-xs-12 gx-col-sm-12 gx-col-md-4"></div>
</div>
`}
/>

<h2 className="component-doc-heading">Horizontal alignment</h2>

We have several utility classes to apply at row level that can be usefull to change horizontal positioning of columns.

<div className="sb-grid-container">
  <div className="gx-row gx-end-xs">
    <div className="gx-box-row gx-col-md-4 gx-col-sm-12 gx-col-xs-12">
      <div className="example-grid-box">
        <span className="gx-is-hidden-sm-up">gx-col-xs-12</span>
        <span className="gx-is-hidden-xsm-down">gx-col-sm-12</span>
        <span className="gx-is-hidden-sm-down">gx-col-md-4</span>
      </div>
    </div>
    <div className="gx-box-row gx-col-md-4 gx-col-sm-12 gx-col-xs-12">
      <div className="example-grid-box">
        <span className="gx-is-hidden-sm-up">gx-col-xs-12</span>
        <span className="gx-is-hidden-xsm-down">gx-col-sm-12</span>
        <span className="gx-is-hidden-sm-down">gx-col-md-4</span>
      </div>
    </div>
  </div>

<div className="gx-row gx-start-xs">
  <div className="gx-box-row gx-col-md-4 gx-col-sm-12 gx-col-xs-12">
    <div className="example-grid-box">
      <span className="gx-is-hidden-sm-up">gx-col-xs-12</span>
      <span className="gx-is-hidden-xsm-down">gx-col-sm-12</span>
      <span className="gx-is-hidden-sm-down">gx-col-md-4</span>
    </div>
  </div>
  <div className="gx-box-row gx-col-md-4 gx-col-sm-12 gx-col-xs-12">
    <div className="example-grid-box">
      <span className="gx-is-hidden-sm-up">gx-col-xs-12</span>
      <span className="gx-is-hidden-xsm-down">gx-col-sm-12</span>
      <span className="gx-is-hidden-sm-down">gx-col-md-4</span>
    </div>
  </div>
</div>

  <div className="gx-row gx-center-xs">
    <div className="gx-box-row gx-col-md-4 gx-col-sm-12 gx-col-xs-12">
      <div className="example-grid-box">
        <span className="gx-is-hidden-sm-up">gx-col-xs-12</span>
        <span className="gx-is-hidden-xsm-down">gx-col-sm-12</span>
        <span className="gx-is-hidden-sm-down">gx-col-md-4</span>
      </div>
    </div>
    <div className="gx-box-row gx-col-md-4 gx-col-sm-12 gx-col-xs-12">
      <div className="example-grid-box">
        <span className="gx-is-hidden-sm-up">gx-col-xs-12</span>
        <span className="gx-is-hidden-xsm-down">gx-col-sm-12</span>
        <span className="gx-is-hidden-sm-down">gx-col-md-4</span>
      </div>
    </div>
  </div>
</div>

<Source
  dark
  language="html"
  code={`
<div className="gx-row gx-end-xs">
  <div className="gx-box-row gx-col-md-4 gx-col-sm-12 gx-col-xs-12"></div>
  <div className="gx-box-row gx-col-md-4 gx-col-sm-12 gx-col-xs-12"></div>
</div>

<div className="gx-row gx-start-xs">
  <div className="gx-box-row gx-col-md-4 gx-col-sm-12 gx-col-xs-12"></div>
  <div className="gx-box-row gx-col-md-4 gx-col-sm-12 gx-col-xs-12"></div>
</div>

<div className="gx-row gx-center-xs">
  <div className="gx-box-row gx-col-md-4 gx-col-sm-12 gx-col-xs-12"></div>
  <div className="gx-box-row gx-col-md-4 gx-col-sm-12 gx-col-xs-12"></div>
</div>
`} />

<h2 className="component-doc-heading">Vertical alignment</h2>

We have several utility classes to apply at row level that can be useful to change vertical positioning of columns.

<h3>Top vertical alignment</h3>

<div className="sb-grid-container">
  <div className="gx-row gx-top-xs">
    <div className="gx-box-row gx-col-md-6 gx-col-sm-12 gx-col-xs-12">
      <div className="example-grid-box" style={{ height: "80px" }}>
        <span className="gx-is-hidden-sm-up">gx-col-xs-12</span>
        <span className="gx-is-hidden-xsm-down">gx-col-sm-12</span>
        <span className="gx-is-hidden-sm-down">gx-col-md-6</span>
      </div>
    </div>
    <div className="gx-box-row gx-col-md-6 gx-col-sm-12 gx-col-xs-12">
      <div className="example-grid-box">
        <span className="gx-is-hidden-sm-up">gx-col-xs-12</span>
        <span className="gx-is-hidden-xsm-down">gx-col-sm-12</span>
        <span className="gx-is-hidden-sm-down">gx-col-md-6</span>
      </div>
    </div>
  </div>
</div>

<Source
  dark
  language="html"
  code={`
<div className="gx-row gx-top-xs">
  <div className="gx-box-row gx-col-md-6 gx-col-sm-12 gx-col-xs-12"></div>
  <div className="gx-box-row gx-col-md-6 gx-col-sm-12 gx-col-xs-12"></div>
</div>
`}
/>

<h3>Middle vertical alignment</h3>

<div className="sb-grid-container">
  <div className="gx-row gx-middle-xs">
    <div className="gx-box-row gx-col-md-6 gx-col-sm-12 gx-col-xs-12">
      <div className="example-grid-box" style={{ height: "80px" }}>
        <span className="gx-is-hidden-sm-up">gx-col-xs-12</span>
        <span className="gx-is-hidden-xsm-down">gx-col-sm-12</span>
        <span className="gx-is-hidden-sm-down">gx-col-md-6</span>
      </div>
    </div>
    <div className="gx-box-row gx-col-md-6 gx-col-sm-12 gx-col-xs-12">
      <div className="example-grid-box">
        <span className="gx-is-hidden-sm-up">gx-col-xs-12</span>
        <span className="gx-is-hidden-xsm-down">gx-col-sm-12</span>
        <span className="gx-is-hidden-sm-down">gx-col-md-6</span>
      </div>
    </div>
  </div>
</div>

<Source
  dark
  language="html"
  code={`
<div className="gx-row gx-middle-xs">
  <div className="gx-box-row gx-col-md-6 gx-col-sm-12 gx-col-xs-12"></div>
  <div className="gx-box-row gx-col-md-6 gx-col-sm-12 gx-col-xs-12"></div>
</div>
`}
/>

<h3>Bottom vertical alignment</h3>

<div className="sb-grid-container">
  <div className="gx-row gx-bottom-xs">
    <div className="gx-box-row gx-col-md-6 gx-col-sm-12 gx-col-xs-12">
      <div className="example-grid-box" style={{ height: "80px" }}>
        <span className="gx-is-hidden-sm-up">gx-col-xs-12</span>
        <span className="gx-is-hidden-xsm-down">gx-col-sm-12</span>
        <span className="gx-is-hidden-sm-down">gx-col-md-6</span>
      </div>
    </div>
    <div className="gx-box-row gx-col-md-6 gx-col-sm-12 gx-col-xs-12">
      <div className="example-grid-box">
        <span className="gx-is-hidden-sm-up">gx-col-xs-12</span>
        <span className="gx-is-hidden-xsm-down">gx-col-sm-12</span>
        <span className="gx-is-hidden-sm-down">gx-col-md-6</span>
      </div>
    </div>
  </div>
</div>

<Source
  dark
  language="html"
  code={`
<div className="gx-row gx-bottom-xs">
  <div className="gx-box-row gx-col-md-6 gx-col-sm-12 gx-col-xs-12"></div>
  <div className="gx-box-row gx-col-md-6 gx-col-sm-12 gx-col-xs-12"></div>
</div>
`}
/>

<h2 className="component-doc-heading">Distribution</h2>

We have several utility classes to apply at row level that can be useful to change spacing of columns.

<h3>Around</h3>

<div className="sb-grid-container">
  <div className="gx-row gx-around-xs">
    <div className="gx-box-row gx-col-md-4 gx-col-sm-12 gx-col-xs-12">
      <div className="example-grid-box">
        <span className="gx-is-hidden-sm-up">gx-col-xs-12</span>
        <span className="gx-is-hidden-xsm-down">gx-col-sm-12</span>
        <span className="gx-is-hidden-sm-down">gx-col-md-4</span>
      </div>
    </div>
    <div className="gx-box-row gx-col-md-4 gx-col-sm-12 gx-col-xs-12">
      <div className="example-grid-box">
        <span className="gx-is-hidden-sm-up">gx-col-xs-12</span>
        <span className="gx-is-hidden-xsm-down">gx-col-sm-12</span>
        <span className="gx-is-hidden-sm-down">gx-col-md-4</span>
      </div>
    </div>
  </div>
</div>

<Source
  dark
  language="html"
  code={`
<div className="gx-row gx-around-xs">
  <div className="gx-box-row gx-col-md-4 gx-col-sm-12 gx-col-xs-12"></div>
  <div className="gx-box-row gx-col-md-4 gx-col-sm-12 gx-col-xs-12"></div>
</div>
`}
/>

<h3>Between</h3>

<div className="sb-grid-container">
  <div className="gx-row gx-between-xs">
    <div className="gx-box-row gx-first-xs gx-col-md-4 gx-col-sm-12 gx-col-xs-12">
      <div className="example-grid-box">
        <span className="gx-is-hidden-sm-up">gx-col-xs-12</span>
        <span className="gx-is-hidden-xsm-down">gx-col-sm-12</span>
        <span className="gx-is-hidden-sm-down">gx-col-md-4</span>
      </div>
    </div>
    <div className="gx-box-row gx-col-md-4 gx-col-sm-12 gx-col-xs-12">
      <div className="example-grid-box">
        <span className="gx-is-hidden-sm-up">gx-col-xs-12</span>
        <span className="gx-is-hidden-xsm-down">gx-col-sm-12</span>
        <span className="gx-is-hidden-sm-down">gx-col-md-4</span>
      </div>
    </div>
  </div>
</div>

<Source
  dark
  language="html"
  code={`
<div className="gx-row gx-between-xs">
  <div className="gx-box-row gx-col-md-4 gx-col-sm-12 gx-col-xs-12"></div>
  <div className="gx-box-row gx-col-md-4 gx-col-sm-12 gx-col-xs-12"></div>
</div>
`}
/>

<h2 className="component-doc-heading">Ordering</h2>

We have several utility classes to apply at column level that can be useful to change columns ordering.

<h3>First</h3>

<div className="sb-grid-container">
  <div className="gx-row">
    <div className="gx-box-row gx-col-md-4 gx-col-sm-12 gx-col-xs-12">
      <div className="example-grid-box">
        <span className="number">1</span>
      </div>
    </div>
    <div className="gx-box-row gx-col-md-4 gx-col-sm-12 gx-col-xs-12">
      <div className="example-grid-box">
        <span className="number">2</span>
      </div>
    </div>
    <div className="gx-box-row gx-first-xs gx-col-md-4 gx-col-sm-12 gx-col-xs-12">
      <div className="example-grid-box">
        <span className="number">3</span>
      </div>
    </div>
  </div>
</div>

<Source
  dark
  language="html"
  code={`
<div className="gx-row">
  <div className="gx-box-row gx-col-md-4 gx-col-sm-12 gx-col-xs-12"></div>
  <div className="gx-box-row gx-col-md-4 gx-col-sm-12 gx-col-xs-12"></div>
  <div
    className="gx-box-row gx-first-xs gx-col-md-4 gx-col-sm-12 gx-col-xs-12"
  ></div>
</div>
`}
/>

<h3>Last</h3>

<div className="sb-grid-container">
  <div className="gx-row">
    <div className="gx-box-row gx-last-xs gx-col-md-4 gx-col-sm-12 gx-col-xs-12">
      <div className="example-grid-box">
        <span className="number">1</span>
      </div>
    </div>
    <div className="gx-box-row gx-col-md-4 gx-col-sm-12 gx-col-xs-12">
      <div className="example-grid-box">
        <span className="number">2</span>
      </div>
    </div>
    <div className="gx-box-row gx-col-md-4 gx-col-sm-12 gx-col-xs-12">
      <div className="example-grid-box">
        <span className="number">3</span>
      </div>
    </div>
  </div>
</div>

<Source
  dark
  language="html"
  code={`
<div className="gx-row">
  <div
    className="gx-box-row gx-last-xs gx-col-md-4 gx-col-sm-12 gx-col-xs-12"
  ></div>
  <div className="gx-box-row gx-col-md-4 gx-col-sm-12 gx-col-xs-12"></div>
  <div className="gx-box-row gx-col-md-4 gx-col-sm-12 gx-col-xs-12"></div>
</div>
`}
/>

<h2 className="component-doc-heading">Reversing</h2>

We have the class `gx-reverse` to apply at row level that determine the direction of the columns.

<div className="sb-grid-container">
  <div className="gx-row gx-reverse">
    <div className="gx-box-row gx-col-md-2 gx-col-sm-12 gx-col-xs-12">
      <div className="example-grid-box">
        <span className="number">1</span>
      </div>
    </div>
    <div className="gx-box-row gx-col-md-2 gx-col-sm-12 gx-col-xs-12">
      <div className="example-grid-box">
        <span className="number">2</span>
      </div>
    </div>
    <div className="gx-box-row gx-col-md-2 gx-col-sm-12 gx-col-xs-12">
      <div className="example-grid-box">
        <span className="number">3</span>
      </div>
    </div>
    <div className="gx-box-row gx-col-md-2 gx-col-sm-12 gx-col-xs-12">
      <div className="example-grid-box">
        <span className="number">4</span>
      </div>
    </div>
    <div className="gx-box-row gx-col-md-2 gx-col-sm-12 gx-col-xs-12">
      <div className="example-grid-box">
        <span className="number">5</span>
      </div>
    </div>
    <div className="gx-box-row gx-col-md-2 gx-col-sm-12 gx-col-xs-12">
      <div className="example-grid-box">
        <span className="number">6</span>
      </div>
    </div>
  </div>
</div>

<Source
  dark
  language="html"
  code={`
<div className="gx-row gx-reverse">
  <div className="gx-box-row gx-col-md-2 gx-col-sm-12 gx-col-xs-12"></div>
  <div className="gx-box-row gx-col-md-2 gx-col-sm-12 gx-col-xs-12"></div>
  <div className="gx-box-row gx-col-md-2 gx-col-sm-12 gx-col-xs-12"></div>
  <div className="gx-box-row gx-col-md-2 gx-col-sm-12 gx-col-xs-12"></div>
  <div className="gx-box-row gx-col-md-2 gx-col-sm-12 gx-col-xs-12"></div>
  <div className="gx-box-row gx-col-md-2 gx-col-sm-12 gx-col-xs-12"></div>
</div>
`}
/>

</div>

</Unstyled>
