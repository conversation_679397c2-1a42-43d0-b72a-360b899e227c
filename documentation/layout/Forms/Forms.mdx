import { Meta, Unstyled } from "@storybook/blocks";
import { Input } from "@gx-design/input";
import { Radio, RadioGroup } from "@gx-design/radio";
import { Select } from "@gx-design/select";
import { Button } from "@gx-design/button";
import { AddonInput } from "@gx-design/addon-input";
import { Checkbox } from "@gx-design/checkbox";

<Meta title="Layout/Forms" />

<Unstyled>
<div className="sb-documentation">
<h1 className="gx-display-2">Forms</h1>

In this section we will provide some examples of complex forms used inside our products.
(Needs to be updated)

<h2 className="component-doc-heading">Examples</h2>

### Simple example using grid

<div className="gx-row">
  <div className="gx-col-xs-12 gx-col-md-6">
    <div className="gx-box-row">
      <Input label="Nome" required={true} />
    </div>
  </div>
  <div className="gx-col-xs-12 gx-col-md-6">
    <div className="gx-box-row">
      <Input label="Cognome" required={true} />
    </div>
  </div>
  <div className="gx-col-xs-12 gx-col-md-6">
    <div className="gx-box-row">
      <Input
        label="Email"
        type="email"
        disabled
        value="<EMAIL>"
        required={true}
      />
    </div>
  </div>
</div>

### Multiple sections and grid

<div className="gx-section">
  <h4 className="gx-title-1">Informazioni principali</h4>
  <div className="gx-row">
    <div className="gx-col-xs-12 gx-col-md-6">
      <div className="gx-box-row gx-box-row--spaceBetween">
        <Input label="Nome" required={true} />
        <RadioGroup label="Sesso" isSmall variant="button">
          <Radio name="radio-button" label="M" />
          <Radio name="radio-button" defaultChecked label="F" />
        </RadioGroup>
      </div>
    </div>
    <div className="gx-col-xs-12 gx-col-md-6">
      <div className="gx-box-row">
        <Input label="Codice Fiscale" required={true} />
      </div>
    </div>
    <div className="gx-col-xs-12 gx-col-md-6">
      <div className="gx-box-row">
        <Input label="Comune di residenza" value="Roma" required={true} />
      </div>
    </div>
    <div className="gx-col-xs-12 gx-col-md-6">
      <div className="gx-box-row">
        <Input label="Indirizzo di residenza" required={true} />
      </div>
    </div>
    <div className="gx-col-xs-12 gx-col-md-6">
      <div className="gx-box-row">
        <Input label="CAP" required={true} />
      </div>
    </div>
  </div>
</div>
<div className="gx-section">
  <h4 className="gx-title-1">Dati Agente</h4>
  <div className="gx-row">
    <div className="gx-col-xs-12 gx-col-md-6">
      <div className="gx-box-row">
        <Input label="Codice REA" required={true} />
      </div>
    </div>
    <div className="gx-col-xs-12 gx-col-md-6">
      <div className="gx-box-row">
        <Select
          label="Associazione di categoria"
          required={true}
          options={[
            {
              label: "Francesco",
              value: "francesco",
              disabled: false,
            },
            {
              label: "Michele",
              value: "michele",
              disabled: false,
            },
            {
              label: "Guido",
              value: "guido",
              disabled: false,
            },
          ]}
        />
      </div>
    </div>
    <div className="gx-col-xs-12 gx-col-md-6">
      <div className="gx-box-row">
        <Input label="Partita IVA" value="000000000000" required={true} />
      </div>
    </div>
  </div>
</div>
<div className="gx-section">
  <h4 className="gx-title-1">Recapiti</h4>
  <div className="gx-row">
    <div className="gx-col-xs-12 gx-col-md-6">
      <div className="gx-box-row">
        <AddonInput
          addon={{ position: "left", type: "icon", value: "mail" }}
          label="Email"
          required={true}
        />
      </div>
    </div>
    <div className="gx-col-xs-12 gx-col-md-6">
      <div className="gx-box-row">
        <AddonInput
          addon={{ position: "left", type: "icon", value: "mail" }}
          label="Conferma email"
          required={true}
        />
      </div>
    </div>
  </div>
  <div className="gx-row">
    <div className="gx-col-xs-12 gx-col-md-3">
      <div className="gx-box-row">
        <AddonInput
          addon={{ position: "left", type: "icon", value: "phone" }}
          label="Telefono fisso 1"
          required={true}
        />
      </div>
    </div>
    <div className="gx-col-xs-12 gx-col-md-3">
      <div className="gx-box-row">
        <AddonInput
          addon={{ position: "left", type: "icon", value: "phone" }}
          label="Telefono fisso 2"
          required={true}
        />
      </div>
    </div>
    <div className="gx-col-xs-12 gx-col-md-3">
      <div className="gx-box-row">
        <AddonInput
          addon={{ position: "left", type: "icon", value: "mobile" }}
          label="Cellulare"
          required={true}
        />
      </div>
    </div>
    <div className="gx-col-xs-12 gx-col-md-3">
      <div className="gx-box-row">
        <AddonInput
          addon={{ position: "left", type: "icon", value: "print" }}
          label="Fax"
          required={true}
        />
      </div>
    </div>
  </div>
</div>
<div className="gx-section">
  <h4 className="gx-title-1">Privacy</h4>
  <div className="gx-subtitle">
    In questa sezione puoi decidere se ricevere news immobiliari e/o
    comunicazioni promozionali e/o sondaggi, mediante tutti i canali di
    comunicazione quali email, telefono o sms, acconsentendo che i dati
    personali forniti vengano inviati a società controllate da Immobiliare.it, o
    comunque con cui essa sviluppa accordi di collaborazione commerciale.
  </div>
  <div className="gx-row">
    <div className="gx-col-xs-12 gx-box-row">
      <Checkbox label="Acconsento a ricevere le comunicazioni informative e promozionali" />
    </div>
  </div>
</div>
<div className="gx-section gx-end-xs">
  <Button variant="accent">Salva</Button>
</div>
</div>
</Unstyled>
