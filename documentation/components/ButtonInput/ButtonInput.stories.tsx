import {
  ButtonInput,
  ButtonInputProps,
} from "@gx-design/button-input/src/ButtonInput";
import type { Meta, StoryObj } from "@storybook/react";
import packageJson from "@gx-design/button-input/package.json";
import { Icon } from "@gx-design/icon";

/** Button inputs are used to directly associate an action with a value in an input. */
const meta: Meta<typeof ButtonInput> = {
  title: "Components/Form/ButtonInput",
  component: ButtonInput,
  parameters: {
    componentSubtitle: "Component / Form",
    componentTitle: "ButtonInput",
    componentSlug: "button-input",
    componentImport: "ButtonInput",
    version: packageJson.version,
    packageName: packageJson.name,
    // determines the styles to import (must be identical to a package name)
    styles: "@gx-design/input",
  },
};

export default meta;

type Story = StoryObj<typeof ButtonInput>;

const Template: Story = {
  render: (args: ButtonInputProps) => <ButtonInput {...args} />,
};

export const Default: Story = {
  ...Template,
  args: {
    label: "Nome",
    placeholder: "Inserisci il tuo nome",
  },
};

export const WithIconAndText: Story = {
  ...Template,
  args: {
    label: "Nome",
    buttonContent: (
      <>
        <span>Modifica</span>
        <Icon name="pencil" />
      </>
    ),
    placeholder: "Inserisci il tuo nome",
  },
};

export const OnlyWithIcon: Story = {
  ...Template,
  args: {
    label: "Nome",
    buttonContent: <Icon name="pencil" />,
    placeholder: "Inserisci il tuo nome",
  },
};

export const Disabled: Story = {
  ...Template,
  args: {
    ...Default.args,
    disabled: true,
  },
};

export const WithError: Story = {
  ...Template,
  args: {
    ...Default.args,
    error: "Questo campo è obbligatorio",
  },
};

export const Tooltip: Story = {
  ...Template,
  args: {
    ...Default.args,
    tooltipHelper: "Istruzioni per questo input",
  },
};
