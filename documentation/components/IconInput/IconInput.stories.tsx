import React, { useState } from "react";
import { IconInput } from "@gx-design/icon-input/src/IconInput";
import { Input } from "@gx-design/input/src/Input";
import { Textarea } from "@gx-design/textarea/src/Textarea";
import { Button } from "@gx-design/button/src/Button";
import {
  ActionList,
  ActionListItem,
} from "@gx-design/action-list/src/ActionList";
import { Icon, IconProps } from "@gx-design/icon/src/Icon";
import type { Meta } from "@storybook/react";
import packageJson from "@gx-design/icon-input/package.json";

/** IconInput is used when you want to use an icon as label. */
const meta: Meta<typeof IconInput> = {
  title: "Components/Form/IconInput",
  component: IconInput,
  parameters: {
    componentSubtitle: "Component / Form",
    componentTitle: "IconInput",
    componentSlug: "icon-input",
    componentImport: "IconInput",
    version: packageJson.version,
    packageName: packageJson.name,
  },
};

export default meta;

export const Default = () => {
  return (
    <div style={{ margin: "0 0 16px" }}>
      <IconInput isVerticalCentered icon="note">
        <Input
          label="Nome"
          isLabelVisible={false}
          placeholder="Inserisci il tuo nome"
          required={false}
        />
      </IconInput>
    </div>
  );
};

export const TextareaExample = () => {
  return (
    <div style={{ margin: "0 0 16px" }}>
      <IconInput isVerticalCentered={false} icon="note">
        <Textarea
          label="Note"
          placeholder="Inserisci delle note"
          isLabelVisible={false}
          required={false}
        />
      </IconInput>
    </div>
  );
};

export const ButtonExample = () => {
  type StatusType = {
    icon: IconProps["name"];
    variant: string;
  };
  const [status, setStatus] = useState("empty");

  const statusHandler = () => {
    let icon;
    let variant;
    if (status === "success") {
      icon = "check-circle--active";
      variant = "success";
    } else if (status === "error") {
      icon = "cross-circle--active";
      variant = "error";
    } else if (status === "warning") {
      icon = "exclamation-mark-circle--active";
      variant = "warning";
    }

    return { icon, variant } as StatusType;
  };

  return (
    <div style={{ margin: "0 0 150px" }}>
      <IconInput
        variant={statusHandler().variant}
        icon={statusHandler().icon}
        isVerticalCentered
      >
        <Button>Tipo di esito</Button>
        <div
          className="gx-dropdown gx-dropdown--bottomLeft"
          style={{ display: "block" }}
        >
          <ActionList>
            <ActionListItem
              text="Positivo"
              onClick={() => setStatus("success")}
              startElement={
                <Icon className="gx-text-success" name="check-circle--active" />
              }
            />
            <ActionListItem
              text="In approvazione"
              onClick={() => setStatus("warning")}
              startElement={
                <Icon
                  className="gx-text-warning"
                  name="exclamation-mark-circle--active"
                />
              }
            />
            <ActionListItem
              text="Negativo"
              onClick={() => setStatus("error")}
              startElement={
                <Icon className="gx-text-error" name="cross-circle--active" />
              }
            />
          </ActionList>
        </div>
      </IconInput>
    </div>
  );
};
