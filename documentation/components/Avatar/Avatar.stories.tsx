import { Avatar, AvatarProps } from "@gx-design/avatar/src/Avatar";
import type { Meta, StoryObj } from "@storybook/react";
import packageJson from "@gx-design/avatar/package.json";

/** Avatars are used in user tables and in the main header and contains the user profile image. */
const meta: Meta<typeof Avatar> = {
  title: "Components/Avatar",
  component: Avatar,
  parameters: {
    componentSubtitle: "Component",
    componentTitle: "Avatar",
    componentSlug: "avatar",
    componentImport: "Avatar",
    version: packageJson.version,
    packageName: packageJson.name,
  },
};

export default meta;

type Story = StoryObj<typeof Avatar>;

const Template = (args: AvatarProps) => <Avatar {...args} />;

export const Default: Story = {
  ...Template,
  args: {
    avatarImage: "https://ppr.im-cdn.it/agenti/205908/7066/80.jpg",
  },
};

export const Small: Story = {
  ...Template,
  args: {
    ...Default.args,
    size: "small",
  },
};

export const Big: Story = {
  ...Template,
  args: {
    ...Default.args,
    size: "big",
  },
};

export const Horizontal: Story = {
  ...Template,
  args: {
    ...Default.args,
    size: "big",
    avatarImage: "../images/avatar/horizontal-avatar.png",
  },
};

export const Vertical: Story = {
  ...Template,
  args: {
    ...Default.args,
    size: "big",
    avatarImage: "../images/avatar/vertical-avatar.png",
  },
};
