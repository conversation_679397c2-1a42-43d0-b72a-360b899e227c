import {
  ActionList,
  ActionListItem,
} from "@gx-design/action-list/src/ActionList";
import { Badge } from "@gx-design/badge";
import { Icon } from "@gx-design/icon";
import type { Meta, StoryObj } from "@storybook/react";
import packageJson from "@gx-design/action-list/package.json";

/**
  The Action List is a list component, the items of the list can contain icons or other elements.
  Action list are almost always wrapped with a `gx-dropdown`.
 */
const meta: Meta<typeof ActionList> = {
  title: "Components/ActionList",
  component: ActionList,
  subcomponents: { ActionListItem },
  parameters: {
    componentSubtitle: "Component",
    componentTitle: "ActionList",
    componentSlug: "action-list",
    componentImport: "ActionList, ActionListItem",
    version: packageJson.version,
    packageName: packageJson.name,
  },
};

export default meta;
type Story = StoryObj<typeof ActionList>;

export const Template: Story = {
  render: (args) => {
    return (
      <div
        className="gx-dropdown"
        style={{ display: "block", position: "static" }}
      >
        <ActionList {...args}>
          <ActionListItem text="Modifica" />
          <ActionListItem text="Elimina" />
          <ActionListItem text="Stampa" />
        </ActionList>
      </div>
    );
  },
};

export const DefaultActionList: Story = {
  render: (args) => {
    return (
      <div
        className="gx-dropdown"
        style={{ display: "block", position: "static" }}
      >
        <ActionList {...args}>
          <ActionListItem text="Modifica" />
          <ActionListItem text="Elimina" />
          <ActionListItem text="Stampa" />
        </ActionList>
      </div>
    );
  },
};
DefaultActionList.storyName = "Default";

export const IconLeft: Story = {
  render: (args) => {
    return (
      <div
        className="gx-dropdown"
        style={{ display: "block", position: "static" }}
      >
        <ActionList {...args}>
          <ActionListItem
            text="Modifica"
            startElement={<Icon name="pencil" />}
          />
          <ActionListItem text="Elimina" startElement={<Icon name="bin" />} />
          <ActionListItem text="Stampa" startElement={<Icon name="print" />} />
        </ActionList>
      </div>
    );
  },
};

export const IconRight: Story = {
  render: (args) => {
    return (
      <div
        className="gx-dropdown"
        style={{ display: "block", position: "static" }}
      >
        <ActionList {...args}>
          <ActionListItem text="Modifica" endElement={<Icon name="pencil" />} />
          <ActionListItem text="Elimina" endElement={<Icon name="bin" />} />
          <ActionListItem text="Stampa" endElement={<Icon name="print" />} />
        </ActionList>
      </div>
    );
  },
};

export const Title: Story = {
  ...Template,
  args: {
    title: "Header title",
  },
};

export const TagActionList: Story = {
  render: (args) => {
    return (
      <div
        className="gx-dropdown"
        style={{ display: "block", position: "static" }}
      >
        <ActionList {...args}>
          <ActionListItem text="Modifica" endElement={<Badge text="30" />} />
          <ActionListItem text="Elimina" endElement={<Badge text="30" />} />
          <ActionListItem text="Stampa" endElement={<Badge text="30" />} />
        </ActionList>
      </div>
    );
  },
};

export const BadgeActionList: Story = {
  render: (args) => {
    return (
      <div
        className="gx-dropdown"
        style={{ display: "block", position: "static" }}
      >
        <ActionList {...args}>
          <ActionListItem
            text="Modifica"
            endElement={
              <Badge style="notification" withIcon={false} text="Novità" />
            }
          />
          <ActionListItem
            text="Elimina"
            endElement={
              <Badge style="notification" withIcon={false} text="Novità" />
            }
          />
          <ActionListItem
            text="Stampa"
            endElement={
              <Badge style="notification" withIcon={false} text="Novità" />
            }
          />
        </ActionList>
      </div>
    );
  },
};

export const LoaderActionList: Story = {
  render: (args) => {
    return (
      <div
        className="gx-dropdown"
        style={{ display: "block", position: "static" }}
      >
        <ActionList {...args}>
          <ActionListItem
            text="Modifica"
            endElement={<Icon className="gx-spin" name="loader" />}
          />
          <ActionListItem text="Elimina" />
          <ActionListItem text="Stampa" />
        </ActionList>
      </div>
    );
  },
};

LoaderActionList.storyName = "Loader";

export const Disabled: Story = {
  render: (args) => {
    return (
      <div
        className="gx-dropdown"
        style={{ display: "block", position: "static" }}
      >
        <ActionList {...args}>
          <ActionListItem text="Modifica" isDisabled />
          <ActionListItem text="Elimina" />
          <ActionListItem text="Stampa" />
        </ActionList>
      </div>
    );
  },
};

export const Multiple: Story = {
  render: (args) => {
    return (
      <div
        className="gx-dropdown"
        style={{ display: "block", position: "static" }}
      >
        <ActionList {...args}>
          <ActionListItem text="Modifica" />
          <ActionListItem text="Elimina" />
          <ActionListItem text="Stampa" />
        </ActionList>
        <ActionList {...args}>
          <ActionListItem text="Modifica" />
          <ActionListItem text="Elimina" />
          <ActionListItem text="Stampa" />
        </ActionList>
      </div>
    );
  },
};
