import { Loader, LoaderType } from "@gx-design/loader/src/Loader";
import type { <PERSON>a, StoryObj } from "@storybook/react";
import packageJson from "@gx-design/loader/package.json";

/**
 A Loader is an animated spinning icon that lets users know content is being loaded.
*/
const meta: Meta<typeof Loader> = {
  title: "Components/Loader",
  component: Loader,
  parameters: {
    componentSubtitle: "Component",
    componentTitle: "Loader",
    componentSlug: "loader",
    componentImport: "Loader",
    version: packageJson.version,
    packageName: packageJson.name,
  },
};

export default meta;

type Story = StoryObj<typeof Loader>;

/** Used when the loader is over existing content, positioned based on the closest `position: relative` parent */
export const Default: Story = {
  render: () => {
    return (
      <div style={{ backgroundColor: "#428cc6" }}>
        <div style={{ padding: "12rem 0" }}>
          <Loader />
        </div>
      </div>
    );
  },
};

/** Used when the loader is loading an entire page or section */
export const Fixed: Story = {
  render: (args) => {
    return (
      <div style={{ backgroundColor: "#428cc6" }}>
        <div style={{ padding: "5rem 0" }}>
          <Loader {...args} />
        </div>
      </div>
    );
  },
  args: {
    variant: "fixed",
  },
};

/** Used when the loader is rendered before content appears */
export const Inline: Story = {
  render: (args) => {
    return <Loader {...args} />;
  },
  args: {
    variant: "inline",
  },
};
