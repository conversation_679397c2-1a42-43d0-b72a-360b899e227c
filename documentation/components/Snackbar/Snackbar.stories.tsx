import {
  Snackbar,
  INotification,
  useNotifyContext,
  NotifyProvider,
} from "@gx-design/snackbar/src/Snackbar";
import { But<PERSON> } from "@gx-design/button";
import type { Meta, StoryObj } from "@storybook/react";
import packageJson from "@gx-design/snackbar/package.json";
import { useState } from "react";

/** A Snackbar is a short message that temporarily appears above the content to communicate the response of an action and/or show error warnings. */
const meta: Meta<typeof Snackbar> = {
  title: "Components/Snackbar",
  decorators: [
    (Story) => (
      <div style={{ margin: "10rem" }}>
        {/* 👇 Decorators in Storybook also accept a function. Replace <Story/> with Story() to enable it  */}
        <Story />
      </div>
    ),
  ],
  component: Snackbar,
  parameters: {
    design: {
      type: "figma",
      url: "https://www.figma.com/file/ibDgDz7mKyn1dzqRdBN2tg/UI-Kit---Gestionale?type=design&node-id=3231-20808&mode=design&t=KFTPVK3aSzZxXAQ7-0",
    },
    componentSubtitle: "Component",
    componentTitle: "Snackbar",
    componentSlug: "snackbar",
    componentImport: "Snackbar",
    componentAnatomy: {
      image: "images/anatomy/snackbar.png",
      legend: ["Container", "Icon", "Text", "Dismiss"],
    },
    version: packageJson.version,
    packageName: packageJson.name,
  },
};

export default meta;

type Story = StoryObj<typeof Snackbar>;

/** Use to communicate the successful completion of an action or process. */
export const Success = () => {
  return (
    <Snackbar
      notification={{
        type: "success",
        message: "This is a success message!",
      }}
      autoClose={0}
    />
  );
};

/** Use to signal that an error or problem has occurred during an action or process. */
export const Error = () => {
  return (
    <Snackbar
      notification={{
        type: "error",
        message: "This is an error message!",
      }}
      autoClose={0}
    />
  );
};

const AllVersions: React.FC = () => {
  const { showNotification } = useNotifyContext();
  const [open, setIsOpen] = useState(false);

  const onButtonClick = (type: INotification["type"]) => {
    setIsOpen(true);
    showNotification({
      message:
        "This is a super long notification message that will appear every time",
      type: type,
      onClose: () => setIsOpen(false),
    });
  };

  return (
    <div
      style={{
        display: "flex",
        justifyContent: "center",
        alignItems: "center",
      }}
    >
      {!open && (
        <>
          <Button onClick={() => onButtonClick("success")}>
            Success Snackbar
          </Button>
          <Button onClick={() => onButtonClick("error")}>Error Snackbar</Button>
        </>
      )}
    </div>
  );
};

export const WithContext = () => {
  return (
    <NotifyProvider>
      <AllVersions />
    </NotifyProvider>
  );
};
