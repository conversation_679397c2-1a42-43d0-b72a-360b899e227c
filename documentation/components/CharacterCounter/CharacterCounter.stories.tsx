import {
  CharacterCounter,
  CharacterCounterProps,
} from "@gx-design/character-counter/src/CharacterCounter";
import { Meta, StoryObj } from "@storybook/react";
import packageJson from "@gx-design/character-counter/package.json";

/**
  Character Counters are used to display a text when maximum number of characters is reached.
 */
const meta: Meta<typeof CharacterCounter> = {
  title: "Components/CharacterCounter",
  component: CharacterCounter,
  parameters: {
    componentSubtitle: "Component",
    componentTitle: "CharacterCounter",
    componentSlug: "character-counter",
    componentImport: "CharacterCounter",
    version: packageJson.version,
    packageName: packageJson.name,
    styles: "@gx-design/character-counter",
  },
};

export default meta;

type Story = StoryObj<typeof CharacterCounter>;

export const Default: Story = {
  args: {
    characterCount: 50,
    maxLength: 100,
  },
};

/** When success is enabled, a variant class is added in order to be visible. */
export const Success: Story = {
  args: {
    characterCount: 80,
    maxLength: 100,
    variant: "success",
  },
};

/** This variant class is not necessary. It triggers automatically when maxLenght exceeds. */
export const Error: Story = {
  args: {
    characterCount: 120,
    maxLength: 100,
    variant: "error",
  },
};
