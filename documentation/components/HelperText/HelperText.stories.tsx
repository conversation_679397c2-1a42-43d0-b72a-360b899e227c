import {
  HelperText,
  HelperTextProps,
} from "@gx-design/helper-text/src/HelperText";
import type { Meta, StoryObj } from "@storybook/react";
import packageJson from "@gx-design/helper-text/package.json";

/** An helper text is an element that provides instructions to the user after he filled an input.  
We use four variations of helper text: **Primary, Success, Error and Warning.** */
const meta: Meta<typeof HelperText> = {
  title: "Components/HelperText",
  component: HelperText,
  parameters: {
    componentSubtitle: "Component",
    componentTitle: "HelperText",
    componentSlug: "helper-text",
    componentImport: "HelperText",
    version: packageJson.version,
    packageName: packageJson.name,
  },
};

export default meta;

type Story = StoryObj<typeof HelperText>;

const Template = (args) => <HelperText {...args} />;

export const PrimaryHelperText = Template.bind({});
PrimaryHelperText.storyName = "Primary";
PrimaryHelperText.args = {
  style: "info",
  text: "Questa versione non la usiamo mai",
};

export const SuccessHelperText = Template.bind({});
SuccessHelperText.storyName = "Success";
SuccessHelperText.args = {
  style: "success",
  text: "La password è sicura",
};

export const ErrorHelperText = Template.bind({});
ErrorHelperText.storyName = "Error";
ErrorHelperText.args = {
  style: "error",
  text: "Questo campo è obbligatorio",
};

export const WarningHelperText = Template.bind({});
WarningHelperText.storyName = "Warning";
WarningHelperText.args = {
  style: "warning",
  text: "La password non è sicura",
};
