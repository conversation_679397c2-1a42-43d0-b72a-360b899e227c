import { Button } from "@gx-design/button/src/Button";
import {
  EmptyState,
  EmptyStateProps,
} from "@gx-design/empty-state/src/EmptyState";
import type { Meta } from "@storybook/react";
import packageJson from "@gx-design/empty-state/package.json";
import React from "react";

/** An empty state appears when there is no data to display and describes what the user can do next. */
const meta: Meta<typeof EmptyState> = {
  title: "Components/EmptyState",
  component: EmptyState,
  parameters: {
    componentTitle: "EmptyState",
    componentSubtitle: "Component",
    componentSlug: "empty-state",
    componentImport: "EmptyState",
    componentAnatomy: {
      image: "images/anatomy/emptyState.png",
      legend: [
        "Illustration",
        "Title",
        "Description (optional)",
        "Button (optional)",
      ],
    },
    version: packageJson.version,
    packageName: packageJson.name,
  },
};

export default meta;

const Template = (args: EmptyStateProps) => <EmptyState {...args} />;

export const Default = Template.bind({});
Default.storyName = "Default";
Default.args = {
  title: "Title",
  description: "Description",
  children: <Button variant="accent">Button</Button>,
};

export const WithoutSubtitle = Template.bind({});
WithoutSubtitle.storyName = "Without Subtitle";
WithoutSubtitle.args = {
  title: "Title",
  children: <Button variant="accent">Button</Button>,
};

export const WithoutCta = Template.bind({});
WithoutCta.storyName = "Without Children";
WithoutCta.args = {
  title: "Titolo",
  description: "Description",
};

export const OnlyTitle = Template.bind({});
OnlyTitle.storyName = "Only Title";
OnlyTitle.args = {
  title: "Titolo",
};
