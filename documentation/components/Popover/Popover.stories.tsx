import { Popover } from "@gx-design/popover/src/Popover";
import { Button } from "@gx-design/button/src/Button";
import type { Meta, StoryObj } from "@storybook/react";
import packageJson from "@gx-design/popover/package.json";

/** A Popover can be used to display some content on top of another. */
const meta: Meta<typeof Popover> = {
  title: "Components/Popover",
  component: Popover,
  parameters: {
    componentSubtitle: "Component",
    componentTitle: "Popover",
    componentSlug: "popover",
    componentImport: "Popover",
    version: packageJson.version,
    packageName: packageJson.name,
  },
};

export default meta;

type Story = StoryObj<typeof Popover>;

const Template = (args) => {
  return (
    <div
      style={{ display: "flex", justifyContent: "center", margin: "60px 0" }}
    >
      <Popover {...args}>
        <Button>Hover me</Button>
      </Popover>
    </div>
  );
};

export const Default = Template.bind({});
Default.args = {
  title: "Lorem ipsum",
  content: <>Lorem ipsum dolor sit amet, conse ctur adipiscing elit.</>,
};

export const NoTitle = Template.bind({});
NoTitle.args = {
  title: null,
  content: <>Lorem ipsum dolor sit amet, conse ctur adipiscing elit.</>,
};

export const Large = Template.bind({});
Large.args = {
  title: "Large Variant",
  large: true,
  content: (
    <>
      Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod
      tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim
      veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea
      commodo consequat.
    </>
  ),
};

export const TopLeft = Template.bind({});
TopLeft.args = {
  ...Default.args,
  position: "topLeft",
};

export const TopRight = Template.bind({});
TopRight.args = {
  ...Default.args,
  position: "topRight",
};

export const Bottom = Template.bind({});
Bottom.args = {
  ...Default.args,
  position: "bottom",
};

export const Left = Template.bind({});
Left.args = {
  ...Default.args,
  position: "left",
};

export const Right = Template.bind({});
Right.args = {
  ...Default.args,
  position: "right",
};

export const BottomLeft = Template.bind({});
BottomLeft.args = {
  ...Default.args,
  position: "bottomLeft",
};

export const BottomRight = Template.bind({});
BottomRight.args = {
  ...Default.args,
  position: "bottomRight",
};

export const RightBottom = Template.bind({});
RightBottom.args = {
  ...Default.args,
  position: "rightBottom",
};

export const RightTop = Template.bind({});
RightTop.args = {
  ...Default.args,
  position: "rightTop",
};

export const LeftBottom = Template.bind({});
LeftBottom.args = {
  ...Default.args,
  position: "leftBottom",
};

export const LeftTop = Template.bind({});
LeftTop.args = {
  ...Default.args,
  position: "leftTop",
};
