import React from "react";
import { Tooltip } from "@gx-design/tooltip/src/Tooltip";
import { Button } from "@gx-design/button/src/Button";
import type { Meta, StoryObj } from "@storybook/react";
import packageJson from "@gx-design/tooltip/package.json";
import { Icon } from "@gx-design/icon/src/Icon";

/** Tooltips show contextual help or information about specific components when a user hovers on them.
Tooltips currently accept `Buttons`, `Badge` and `Tags` as children. */
const meta: Meta<typeof Tooltip> = {
  title: "Components/Tooltip",
  component: Tooltip,
  parameters: {
    componentSubtitle: "Component",
    componentTitle: "Tooltip",
    componentSlug: "tooltip",
    componentImport: "Tooltip",
    layout: "centered",
    version: packageJson.version,
    packageName: packageJson.name,
  },
};

export default meta;

type Story = StoryObj<typeof Tooltip>;

const Template = (args) => {
  return (
    <div
      style={{ display: "flex", justifyContent: "center", margin: "40px 0" }}
    >
      <Tooltip {...args}>
        <Button>Tooltip</Button>
      </Tooltip>
      <Tooltip {...args}>
        <Button iconOnly>
          <Icon name="ellipsis" />
        </Button>
      </Tooltip>
    </div>
  );
};

export const Default = Template.bind({});
Default.args = {
  position: "top",
  text: "Altre Azioni",
};

export const TopLeft = Template.bind({});
TopLeft.args = {
  ...Default.args,
  position: "topLeft",
};

export const TopRight = Template.bind({});
TopRight.args = {
  ...Default.args,
  position: "topRight",
};

export const Left = Template.bind({});
Left.args = {
  ...Default.args,
  position: "left",
};

export const Right = Template.bind({});
Right.args = {
  ...Default.args,
  position: "right",
};

export const Bottom = Template.bind({});
Bottom.args = {
  ...Default.args,
  ...Default.args,
  position: "bottom",
};

export const BottomLeft = Template.bind({});
BottomLeft.args = {
  ...Default.args,
  position: "bottomLeft",
};

export const BottomRight = Template.bind({});
BottomRight.args = {
  ...Default.args,
  position: "bottomRight",
};

export const Multiline: React.FC = () => {
  return (
    <div
      style={{
        display: "flex",
        justifyContent: "center",
        margin: "40px 0",
      }}
    >
      <Tooltip text="Lorem ipsum dolor sit amet" position="top">
        <Button>Tooltip</Button>
      </Tooltip>
    </div>
  );
};
