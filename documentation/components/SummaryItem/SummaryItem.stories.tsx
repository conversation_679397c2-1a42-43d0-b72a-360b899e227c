import {
  SummaryItem,
  SummaryItemProps,
} from "@gx-design/summary-item/src/SummaryItem";
import type { Meta, StoryObj } from "@storybook/react";
import packageJson from "@gx-design/summary-item/package.json";
import { EnergeticTag } from "@gx-design/energetic-tag/src/EnergeticTag";
import { Badge } from "@gx-design/badge";

/** SummaryItem component is used to present pairs of related information: a label and his value. */
const meta: Meta<typeof SummaryItem> = {
  title: "Components/SummaryItem",
  component: SummaryItem,
  parameters: {
    componentSubtitle: "Component",
    componentTitle: "SummaryItem",
    componenSlug: "summary-item",
    componentImport: "SummaryItem",
    version: packageJson.version,
    packageName: packageJson.name,
  },
};

export default meta;

type Story = StoryObj<typeof SummaryItem>;

export const Default = (args) => <SummaryItem {...args} />;
Default.args = {
  label: "Camere da letto",
  children: "5",
};

const Template = (args: SummaryItemProps) => <SummaryItem {...args} />;

export const WithEnergeticTag = Template.bind({});
WithEnergeticTag.args = {
  ...Default.args,
  label: "Classe energetica",
  children: <EnergeticTag score="g"></EnergeticTag>,
};

export const WithTag = Template.bind({});
WithTag.args = {
  ...Default.args,
  label: "Status",
  children: <Badge style="success" text="Attivo"></Badge>,
};
