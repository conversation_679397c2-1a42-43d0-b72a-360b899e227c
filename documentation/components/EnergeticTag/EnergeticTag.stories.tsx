import { EnergeticTag } from "@gx-design/energetic-tag/src/EnergeticTag";
import type { Meta, StoryObj } from "@storybook/react";
import packageJson from "@gx-design/energetic-tag/package.json";

/**
  Energetic Tags are used to display the Energetic category of a property.  
  We use these variations of Energetic Tags: **a4, a3, a2, a1, a-plus, a, b, c, d, e, f, g.**
 */
const meta: Meta<typeof EnergeticTag> = {
  title: "Components/EnergeticTag",
  component: EnergeticTag,
  parameters: {
    componentSubtitle: "Component",
    componentTitle: "EnergeticTag",
    componentSlug: "energetic-tag",
    componentImport: "EnergeticTag",
    version: packageJson.version,
    packageName: packageJson.name,
  },
};

export default meta;

type Story = StoryObj<typeof EnergeticTag>;

const Template = (args) => <EnergeticTag {...args} />;

export const a4 = Template.bind({});
a4.args = {
  score: "a4",
};

export const a3 = Template.bind({});
a3.args = {
  score: "a3",
};

export const a2 = Template.bind({});
a2.args = {
  score: "a2",
};

export const a1 = Template.bind({});
a1.args = {
  score: "a1",
};

export const aPlus = Template.bind({});
aPlus.args = {
  score: "a-plus",
};

export const A = Template.bind({});
A.args = {
  score: "a",
};

export const B = Template.bind({});
B.args = {
  score: "b",
};

export const C = Template.bind({});
C.args = {
  score: "c",
};

export const D = Template.bind({});
D.args = {
  score: "d",
};

export const E = Template.bind({});
E.args = {
  score: "e",
};

export const F = Template.bind({});
F.args = {
  score: "f",
};

export const G = Template.bind({});
G.args = {
  score: "g",
};
