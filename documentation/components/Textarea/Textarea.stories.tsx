import { useState } from "react";
import { Textarea } from "@gx-design/textarea/src/Textarea";
import type { Meta, StoryObj } from "@storybook/react";
import packageJson from "@gx-design/textarea/package.json";

/** A text area lets users enter long form text which spans over multiple lines. */
const meta: Meta<typeof Textarea> = {
  title: "Components/Form/Textarea",
  component: Textarea,
  parameters: {
    componentSubtitle: "Component / Form",
    componentTitle: "Textarea",
    componentSlug: "textarea",
    componentImport: "Textarea",
    version: packageJson.version,
    packageName: packageJson.name,
  },
};

export default meta;

type Story = StoryObj<typeof Textarea>;

const Template = (args) => <Textarea {...args} />;

export const DefaultTextarea = Template.bind({});
DefaultTextarea.storyName = "Default";
DefaultTextarea.args = {
  label: "Name",
  placeholder: "Fill in your name",
};

export const WithoutLabel = Template.bind({});
WithoutLabel.storyName = "Without label";
WithoutLabel.args = {
  ...DefaultTextarea.args,
  isLabelVisible: false,
};

export const Required = Template.bind({});
Required.storyName = "Required";
Required.args = {
  ...DefaultTextarea.args,
  required: true,
};

export const Disabled = Template.bind({});
Disabled.storyName = "Disabled";
Disabled.args = {
  ...DefaultTextarea.args,
  disabled: true,
};

export const WithError = Template.bind({});
WithError.storyName = "With Error";
WithError.args = {
  ...DefaultTextarea.args,
  error: "This field is required",
};

export const WithCounter = (args) => {
  const [value, setValue] = useState("");
  return (
    <Textarea
      {...args}
      value={value}
      onChange={(e) => setValue(e.target.value)}
    />
  );
};

WithCounter.storyName = "With Counter";
WithCounter.args = {
  ...DefaultTextarea.args,
  maxLength: 50,
};

export const WithCounterAndError = (args) => {
  const [value, setValue] = useState("");
  return (
    <Textarea
      {...args}
      value={value} // Pass the value
      onChange={(e) => setValue(e.target.value)}
    />
  );
};

WithCounterAndError.storyName = "With Counter and Required Filling Error";
WithCounterAndError.args = {
  ...DefaultTextarea.args,
  maxLength: 50,
  error: "This field is required",
};

export const Tooltip = Template.bind({});
Tooltip.storyName = "Tooltip";
Tooltip.args = {
  ...DefaultTextarea.args,
  tooltipHelper: "Instructions for filling out this field",
};
