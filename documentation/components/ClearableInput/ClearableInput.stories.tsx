import {
  ClearableInput,
  ClearableInputProps,
} from "@gx-design/clearable-input/src/ClearableInput";
import { useState } from "react";
import type { Meta, StoryObj } from "@storybook/react";
import packageJson from "@gx-design/clearable-input/package.json";

/** Clearable inputs are inputs that shows a button to reset themselves when they are filled with a value. */
const meta: Meta<typeof ClearableInput> = {
  title: "Components/Form/ClearableInput",
  component: ClearableInput,
  parameters: {
    componentSubtitle: "Component / Form",
    componentTitle: "ClearableInput",
    componentSlug: "clearable-input",
    componentImport: "ClearableInput",
    version: packageJson.version,
    packageName: packageJson.name,
    // determines the styles to import (must be identical to a package name)
    styles: "@gx-design/input",
  },
};

export default meta;

type Story = StoryObj<typeof ClearableInput>;

const Template = (args: ClearableInputProps) => {
  const [value, setValue] = useState("");
  return (
    <ClearableInput
      {...args}
      value={value}
      onChange={(e) => setValue(e.target.value)}
      onClearClick={() => setValue("")}
    />
  );
};

export const Default = Template.bind({});
Default.storyName = "Default";
Default.args = {
  label: "Nome",
  placeholder: "Inserisci il tuo nome",
};

/** When label is disabled, a class is added to the label in order to be accessible by screen readers.  */
export const WithoutLabel = Template.bind({});
WithoutLabel.storyName = "Without label";
WithoutLabel.args = {
  ...Default.args,
  isLabelVisible: false,
};

export const WithError = Template.bind({});
WithError.storyName = "With Error";
WithError.args = {
  ...Default.args,
  error: "Questo campo è obbligatorio",
};

export const Tooltip = Template.bind({});
Tooltip.storyName = "Tooltip";
Tooltip.args = {
  ...Default.args,
  tooltipHelper: "Istruzioni per questo input",
};
