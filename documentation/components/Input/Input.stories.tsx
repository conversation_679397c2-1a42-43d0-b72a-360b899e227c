import { Input } from "@gx-design/input/src/Input";
import type { Meta, StoryObj } from "@storybook/react";
import packageJson from "@gx-design/input/package.json";

/** A text field that allows a user to write or edit text. */
const meta: Meta<typeof Input> = {
  title: "Components/Form/Input",
  component: Input,
  parameters: {
    componentSubtitle: "Component / Form",
    componentTitle: "Input",
    componentSlug: "input",
    componentImport: "Input",
    version: packageJson.version,
    packageName: packageJson.name,
  },
};

export default meta;

type Story = StoryObj<typeof Input>;

const Template = (args) => {
  return <Input {...args} />;
};

export const DefaultInput = Template.bind({});
DefaultInput.storyName = "Default";
DefaultInput.args = {
  label: "Nome",
  name: "name",
  placeholder: "Inserisci il tuo nome",
};

export const WithoutLabel = Template.bind({});
WithoutLabel.storyName = "Without label";
WithoutLabel.args = {
  ...DefaultInput.args,
  isLabelVisible: false,
};

export const Required = Template.bind({});
Required.storyName = "Required";
Required.args = {
  ...DefaultInput.args,
  required: true,
};

export const Disabled = Template.bind({});
Disabled.storyName = "Disabled";
Disabled.args = {
  ...DefaultInput.args,
  disabled: true,
};

export const WithError = Template.bind({});
WithError.storyName = "With Error";
WithError.args = {
  ...DefaultInput.args,
  error: "Questo campo è obbligatorio",
};

export const Password = Template.bind({});
Password.storyName = "Password";
Password.args = {
  name: "password",
  type: "password",
  label: "Password",
  placeholder: "Inserisci la tua password",
};

export const Tooltip = Template.bind({});
Tooltip.storyName = "Tooltip";
Tooltip.args = {
  name: "name",
  type: "text",
  label: "Nome e Cognome",
  tooltipHelper: "Indicazioni su questo input",
  placeholder: "Inserisci il tuo nome completo",
};
