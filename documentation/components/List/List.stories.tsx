import { List, ListItem } from "@gx-design/list/src/List";
import type { Meta, StoryObj } from "@storybook/react";
import packageJson from "@gx-design/list/package.json";

/** The List is a list component, the items of the list can contain icons or other elements. */
const meta: Meta<typeof List> = {
  title: "Components/List",
  component: List,
  subcomponents: { ListItem },
  parameters: {
    componentSubtitle: "Component",
    componentTitle: "List",
    componentSlug: "list",
    componentImport: "List",
    version: packageJson.version,
    packageName: packageJson.name,
  },
};

export default meta;

type Story = StoryObj<typeof List>;

export const DefaultList = (args) => {
  return (
    <List {...args}>
      <ListItem content="Cane"></ListItem>
      <ListItem content="Gatto"></ListItem>
      <ListItem content="Topo"></ListItem>
      <ListItem content="Formaggio"></ListItem>
    </List>
  );
};
DefaultList.storyName = "Default";

export const WithIconList = (args) => {
  return (
    <List {...args}>
      <ListItem content="Cane" icon="clock"></ListItem>
      <ListItem content="Gatto" icon="clock"></ListItem>
      <ListItem content="Topo"></ListItem>
      <ListItem content="Formaggio"></ListItem>
    </List>
  );
};
WithIconList.storyName = "With Icon";
