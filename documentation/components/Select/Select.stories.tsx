import { Select, SelectProps } from "@gx-design/select/src/Select";
import type { Meta } from "@storybook/react";
import packageJson from "@gx-design/select/package.json";

const options = [
  {
    label: "<PERSON>",
    value: "francesco",
    disabled: false,
  },
  {
    label: "<PERSON>",
    value: "michele",
    disabled: false,
  },
  {
    label: "<PERSON>",
    value: "guido",
    disabled: false,
  },
];

/** Select allows users to make a single selection from a list of options. */
const meta: Meta<typeof Select> = {
  title: "Components/Form/Select",
  component: Select,
  parameters: {
    componentSubtitle: "Component / Form",
    componentTitle: "Select",
    componentSlug: "select",
    componentImport: "Select",
    version: packageJson.version,
    packageName: packageJson.name,
  },
};

export default meta;

const Template = (args: SelectProps) => <Select {...args} options={options} />;

export const DefaultSelect = Template.bind({});
DefaultSelect.storyName = "Default";
DefaultSelect.args = {
  label: "Nome",
};

export const WithoutLabel = Template.bind({});
WithoutLabel.storyName = "Without label";
WithoutLabel.args = {
  ...DefaultSelect.args,
  isLabelVisible: false,
};

export const Required = Template.bind({});
Required.storyName = "Required";
Required.args = {
  ...DefaultSelect.args,
  required: true,
};

export const IsLoading = Template.bind({});
IsLoading.storyName = "IsLoading";
IsLoading.args = {
  ...DefaultSelect.args,
  isLoading: true,
};

export const Disabled = Template.bind({});
Disabled.storyName = "Disabled";
Disabled.args = {
  ...DefaultSelect.args,
  disabled: true,
};

export const WithError = Template.bind({});
WithError.storyName = "With Error";
WithError.args = {
  ...DefaultSelect.args,
  error: "Questo campo è obbligatorio",
};

export const WithPlaceholderOption = Template.bind({});
WithPlaceholderOption.storyName = "With Placeholder Option";
WithPlaceholderOption.args = {
  ...DefaultSelect.args,
  placeholder: "Scegli un nome da questa lista",
};

export const ChipStyle = Template.bind({});
ChipStyle.storyName = "Chip Style";
ChipStyle.args = {
  ...DefaultSelect.args,
  placeholder: "Scegli un nome da questa lista",
  variant: "chip",
};

export const WithTooltip = Template.bind({});
WithTooltip.storyName = "Tooltip";
WithTooltip.args = {
  ...DefaultSelect.args,
  tooltipHelper: "Indicazioni su questo input",
  placeholder: "Scegli un nome da questa lista",
};
