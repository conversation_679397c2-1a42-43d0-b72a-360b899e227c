import { Accordion, AccordionItem } from "@gx-design/accordion/src/Accordion";
import { SummaryItem } from "@gx-design/summary-item/src/SummaryItem";
import { EnergeticTag } from "@gx-design/energetic-tag";
import { Badge } from "@gx-design/badge";
import type { Meta, StoryObj } from "@storybook/react";
import packageJson from "@gx-design/accordion/package.json";
import { Button } from "@gx-design/button/src/Button";

/**
 Accordion is an expandable table for showing nested hierarchies of information.
*/
const meta: Meta<typeof Accordion | typeof AccordionItem> = {
  title: "Components/Accordion",
  component: Accordion,
  subcomponents: { AccordionItem },
  parameters: {
    componentSubtitle: "Component",
    componentTitle: "Accordion",
    componentSlug: "accordion",
    componentImport: "Accordion, AccordionItem",
    version: packageJson.version,
    packageName: packageJson.name,
  },
};

export default meta;

type Story = StoryObj<typeof Accordion>;

export const Default: Story = {
  render: () => {
    return (
      <Accordion>
        <AccordionItem title="Composizione dell'immobile">
          <div className="gx-row">
            <div className="gx-col-xs-12 gx-col-md-3">
              <div className="gx-box-row">
                <SummaryItem label="Camere da letto">4</SummaryItem>
              </div>
            </div>
            <div className="gx-col-xs-12 gx-col-md-3">
              <div className="gx-box-row">
                <SummaryItem label="Altre camere/stanze">2</SummaryItem>
              </div>
            </div>
            <div className="gx-col-xs-12 gx-col-md-3">
              <div className="gx-box-row">
                <SummaryItem label="Bagni">2</SummaryItem>
              </div>
            </div>
            <div className="gx-col-xs-12 gx-col-md-3">
              <div className="gx-box-row">
                <SummaryItem label="Posti auto">1</SummaryItem>
              </div>
            </div>
            <div className="gx-col-xs-12 gx-col-md-3">
              <div className="gx-box-row">
                <SummaryItem label="Giardino">Sì</SummaryItem>
              </div>
            </div>
            <div className="gx-col-xs-12 gx-col-md-3">
              <div className="gx-box-row">
                <SummaryItem label="Ascensore">No</SummaryItem>
              </div>
            </div>
          </div>
        </AccordionItem>
        <AccordionItem title="Certificazione energetica">
          <div className="gx-row">
            <div className="gx-col-xs-12 gx-col-md-6">
              <div className="gx-box-row">
                <SummaryItem label="Classe energetica">
                  <EnergeticTag score="g"></EnergeticTag>
                </SummaryItem>
              </div>
            </div>
            <div className="gx-col-xs-12 gx-col-md-6">
              <div className="gx-box-row">
                <SummaryItem label="Indice di prestazione energetica">
                  3
                </SummaryItem>
              </div>
            </div>
          </div>
        </AccordionItem>
        <AccordionItem title="Utente">
          <div className="gx-row">
            <div className="gx-col-xs-12 gx-col-md-4">
              <div className="gx-box-row">
                <SummaryItem label="Nome">Mario Mari</SummaryItem>
              </div>
            </div>
            <div className="gx-col-xs-12 gx-col-md-4">
              <div className="gx-box-row">
                <SummaryItem label="Ruolo">Agente</SummaryItem>
              </div>
            </div>
            <div className="gx-col-xs-12 gx-col-md-4">
              <div className="gx-box-row">
                <SummaryItem label="Stato">
                  <Badge style="success" text="Attivo"></Badge>
                </SummaryItem>
              </div>
            </div>
          </div>
        </AccordionItem>
      </Accordion>
    );
  },
};

export const WithCTA: Story = {
  render: () => {
    return (
      <Accordion>
        <AccordionItem
          cta={<Button>Ottieni dati catastali</Button>}
          title="Composizione dell'immobile"
        >
          <div className="gx-row">
            <div className="gx-col-xs-12 gx-col-md-3">
              <div className="gx-box-row">
                <SummaryItem label="Camere da letto">4</SummaryItem>
              </div>
            </div>
            <div className="gx-col-xs-12 gx-col-md-3">
              <div className="gx-box-row">
                <SummaryItem label="Altre camere/stanze">2</SummaryItem>
              </div>
            </div>
            <div className="gx-col-xs-12 gx-col-md-3">
              <div className="gx-box-row">
                <SummaryItem label="Bagni">2</SummaryItem>
              </div>
            </div>
            <div className="gx-col-xs-12 gx-col-md-3">
              <div className="gx-box-row">
                <SummaryItem label="Posti auto">1</SummaryItem>
              </div>
            </div>
            <div className="gx-col-xs-12 gx-col-md-3">
              <div className="gx-box-row">
                <SummaryItem label="Giardino">Sì</SummaryItem>
              </div>
            </div>
            <div className="gx-col-xs-12 gx-col-md-3">
              <div className="gx-box-row">
                <SummaryItem label="Ascensore">No</SummaryItem>
              </div>
            </div>
          </div>
        </AccordionItem>
        <AccordionItem title="Certificazione energetica">
          <div className="gx-row">
            <div className="gx-col-xs-12 gx-col-md-6">
              <div className="gx-box-row">
                <SummaryItem label="Classe energetica">
                  <EnergeticTag score="g"></EnergeticTag>
                </SummaryItem>
              </div>
            </div>
            <div className="gx-col-xs-12 gx-col-md-6">
              <div className="gx-box-row">
                <SummaryItem label="Indice di prestazione energetica">
                  3
                </SummaryItem>
              </div>
            </div>
          </div>
        </AccordionItem>
        <AccordionItem title="Utente">
          <div className="gx-row">
            <div className="gx-col-xs-12 gx-col-md-4">
              <div className="gx-box-row">
                <SummaryItem label="Nome">Mario Mari</SummaryItem>
              </div>
            </div>
            <div className="gx-col-xs-12 gx-col-md-4">
              <div className="gx-box-row">
                <SummaryItem label="Ruolo">Agente</SummaryItem>
              </div>
            </div>
            <div className="gx-col-xs-12 gx-col-md-4">
              <div className="gx-box-row">
                <SummaryItem label="Stato">
                  <Badge style="success" text="Attivo"></Badge>
                </SummaryItem>
              </div>
            </div>
          </div>
        </AccordionItem>
      </Accordion>
    );
  },
};
