import { useState } from "react";
import { NumberInput } from "@gx-design/number-input/src/NumberInput";
import type { Meta, StoryObj } from "@storybook/react";
import packageJson from "@gx-design/number-input/package.json";

const meta: Meta<typeof NumberInput> = {
  title: "Components/Form/NumberInput",
  component: NumberInput,
  parameters: {
    componentSubtitle: "Component / Form",
    componentTitle: "NumberInput",
    componentSlug: "number-input",
    componentImport: "NumberInput",
    version: packageJson.version,
    packageName: packageJson.name,
    // determines the styles to import (must be identical to a package name)
    styles: "@gx-design/input",
  },
};

export default meta;

type Story = StoryObj<typeof NumberInput>;

const Template = (args) => {
  const [count, setCount] = useState(0);
  return (
    <div style={{ width: "auto" }}>
      <NumberInput
        {...args}
        onChange={(e) => setCount(Number(e.target.value))}
        onMinusClick={() => setCount(count - 1)}
        onPlusClick={() => setCount(count + 1)}
        value={count}
      />
    </div>
  );
};

export const Default = Template.bind({});
Default.storyName = "Default";
Default.args = {
  label: "Unità commerciali totali",
  placeholder: "--",
};

export const NoLabel = Template.bind({});
NoLabel.storyName = "Without Label";
NoLabel.args = {
  label: "Unità commerciali totali",
  isLabelVisible: false,
  placeholder: "--",
};

export const Disabled = Template.bind({});
Disabled.storyName = "Disabled";
Disabled.args = {
  label: "Unità commerciali totali",
  disabled: true,
  placeholder: "--",
};

export const Error = Template.bind({});
Error.storyName = "Error";
Error.args = {
  label: "Unità commerciali totali",
  error: "Numero inserito non valido",
  placeholder: "--",
};

export const WithTooltip = Template.bind({});
WithTooltip.storyName = "Tooltip";
WithTooltip.args = {
  label: "Unità commerciali totali",
  placeholder: "--",
  tooltipHelper: "Inserisci minimo 3 unità",
};
