import React from "react";
import {
  Toolbar,
  ToolbarNavigation,
  ToolbarActions,
  ToolbarResults,
} from "@gx-design/toolbar/src/Toolbar";
import type { Meta } from "@storybook/react";
import packageJson from "@gx-design/toolbar/package.json";

/** A toolbar provides convenient access to frequently used commands and controls that perform actions relevant to the current view. */
const meta: Meta<typeof Toolbar> = {
  title: "Components/Toolbar",
  component: Toolbar,
  subcomponents: { ToolbarNavigation, ToolbarActions, ToolbarResults },
  parameters: {
    componentSubtitle: "Component",
    componentTitle: "Toolbar",
    componentSlug: "toolbar",
    componentImport: "Toolbar",
    version: packageJson.version,
    packageName: packageJson.name,
  },
};

export default meta;

const MOCK_NAVIGATION_ITEMS = [
  { label: "Attivi", value: "attivi", active: true, counter: 10 },
  { label: "Archiviati", value: "archiviati", active: false, counter: 1000 },
];

const MOCK_NAVIGATION_LOADER = [
  { label: "Attivi", value: "attivi", active: true, counter: null },
  { label: "Archiviati", value: "archiviati", active: false, counter: null },
];

const DEFAULT_LABELS = {
  filterAction: "Filter",
  filters: "filters",
  searchInputPlaceholder: "Insert",
  remove: "Remove",
  removeFiltersAction: "Remove filters",
  resetAction: "Reset",
  result: "result",
  resultPlural: "results",
  resultFor: "result for",
  resultsFor: "results for",
  searchAction: "Search",
  ascendingSorting: "ascending",
  descendingSorting: "descending",
  sortingAction: "Sort",
};

const MOCK_SORTING_CONFIG = {
  currentSortKey: "key",
  currentSortDirection: "ASC",
  sortItems: [
    {
      label: "name",
      key: "name",
    },
    {
      label: "surname",
      key: "name",
    },
  ],
  onSortAction: (key: string, direction: string) => {},
  showSortingOnDesktop: true,
  isSortingActive: false,
};

export const Default = () => {
  return (
    <Toolbar
      labels={DEFAULT_LABELS}
      isFiltersSearchActive={false}
      searchedText={""}
      //eslint-disable-next-line
      onShowFiltersAction={() => {}}
      //eslint-disable-next-line
      onInputSearchAction={() => {}}
      //eslint-disable-next-line
      onNavigateAction={() => console.log("onNavigateAction")}
      //eslint-disable-next-line
      onResetInputSearchAction={() => console.log("onResetInputSearchAction")}
      //eslint-disable-next-line
      onRemoveFiltersAction={() => console.log("onRemoveFiltersActiona")}
      searchItemsCount={0}
    >
      <Toolbar.Navigation navigationItems={MOCK_NAVIGATION_ITEMS} />
      <Toolbar.Actions />
      <Toolbar.Results />
    </Toolbar>
  );
};

export const WithLoadingCounter = () => {
  return (
    <Toolbar
      labels={DEFAULT_LABELS}
      isFiltersSearchActive={false}
      searchedText={""}
      //eslint-disable-next-line
      onShowFiltersAction={() => console.log("onShowFiltersAction")}
      //eslint-disable-next-line
      onInputSearchAction={() => {}}
      //eslint-disable-next-line
      onNavigateAction={() => console.log("onNavigateAction")}
      //eslint-disable-next-line
      onResetInputSearchAction={() => console.log("onResetInputSearchAction")}
      //eslint-disable-next-line
      onRemoveFiltersAction={() => console.log("onRemoveFiltersActiona")}
      searchItemsCount={0}
    >
      <Toolbar.Navigation navigationItems={MOCK_NAVIGATION_LOADER} />
      <Toolbar.Actions />
      <Toolbar.Results />
    </Toolbar>
  );
};

export const WithResults = () => {
  return (
    <Toolbar
      labels={DEFAULT_LABELS}
      isFiltersSearchActive={false}
      searchedText={"Ricerca"}
      //eslint-disable-next-line
      onShowFiltersAction={() => console.log("onShowFiltersAction")}
      //eslint-disable-next-line
      onInputSearchAction={() => console.log("onInputSearchAction")}
      //eslint-disable-next-line
      onNavigateAction={() => console.log("onNavigateAction")}
      //eslint-disable-next-line
      onResetInputSearchAction={() => console.log("onResetInputSearchAction")}
      //eslint-disable-next-line
      onRemoveFiltersAction={() => console.log("onRemoveFiltersActiona")}
      searchItemsCount={10}
    >
      <Toolbar.Navigation navigationItems={MOCK_NAVIGATION_ITEMS} />
      <Toolbar.Actions />
      <Toolbar.Results />
    </Toolbar>
  );
};

export const WithFiltersSearchActive = () => {
  return (
    <Toolbar
      labels={DEFAULT_LABELS}
      isFiltersSearchActive={true}
      searchedText={"Ricerca"}
      //eslint-disable-next-line
      onShowFiltersAction={() => console.log("onShowFiltersAction")}
      //eslint-disable-next-line
      onInputSearchAction={() => console.log("onInputSearchAction")}
      //eslint-disable-next-line
      onNavigateAction={() => console.log("onNavigateAction")}
      //eslint-disable-next-line
      onResetInputSearchAction={() => console.log("onResetInputSearchAction")}
      //eslint-disable-next-line
      onRemoveFiltersAction={() => console.log("onRemoveFiltersActiona")}
      searchItemsCount={10}
    >
      <Toolbar.Navigation navigationItems={MOCK_NAVIGATION_ITEMS} />
      <Toolbar.Actions />
      <Toolbar.Results />
    </Toolbar>
  );
};

export const NoFilters = () => {
  return (
    <Toolbar
      hasFilters={false}
      labels={DEFAULT_LABELS}
      isFiltersSearchActive={false}
      searchedText={""}
      //eslint-disable-next-line
      onShowFiltersAction={() => console.log("onShowFiltersAction")}
      //eslint-disable-next-line
      onInputSearchAction={() => console.log("onInputSearchAction")}
      //eslint-disable-next-line
      onNavigateAction={() => console.log("onNavigateAction")}
      //eslint-disable-next-line
      onResetInputSearchAction={() => console.log("onResetInputSearchAction")}
      //eslint-disable-next-line
      onRemoveFiltersAction={() => console.log("onRemoveFiltersActiona")}
      searchItemsCount={0}
    >
      <Toolbar.Navigation navigationItems={MOCK_NAVIGATION_ITEMS} />
      <Toolbar.Actions />
      <Toolbar.Results />
    </Toolbar>
  );
};

export const NoNavbar = () => {
  return (
    <Toolbar
      labels={DEFAULT_LABELS}
      isFiltersSearchActive={false}
      searchedText={""}
      //eslint-disable-next-line
      onShowFiltersAction={() => console.log("onShowFiltersAction")}
      //eslint-disable-next-line
      onInputSearchAction={() => console.log("onInputSearchAction")}
      //eslint-disable-next-line
      onNavigateAction={() => console.log("onNavigateAction")}
      //eslint-disable-next-line
      onResetInputSearchAction={() => console.log("onResetInputSearchAction")}
      //eslint-disable-next-line
      onRemoveFiltersAction={() => console.log("onRemoveFiltersActiona")}
      searchItemsCount={0}
    >
      <Toolbar.Actions />
      <Toolbar.Results />
    </Toolbar>
  );
};

export const NoSearchInput = () => {
  return (
    <Toolbar
      labels={DEFAULT_LABELS}
      isFiltersSearchActive={false}
      searchedText={""}
      //eslint-disable-next-line
      onShowFiltersAction={() => console.log("onShowFiltersAction")}
      //eslint-disable-next-line
      onInputSearchAction={() => console.log("onInputSearchAction")}
      //eslint-disable-next-line
      onNavigateAction={() => console.log("onNavigateAction")}
      //eslint-disable-next-line
      onResetInputSearchAction={() => console.log("onResetInputSearchAction")}
      //eslint-disable-next-line
      onRemoveFiltersAction={() => console.log("onRemoveFiltersActiona")}
      searchItemsCount={0}
      hideSearchInput
    >
      <Toolbar.Actions />
      <Toolbar.Results />
    </Toolbar>
  );
};

export const WithIsFilterSearchActiveBoolean = () => {
  return (
    <Toolbar
      hasFilters={true}
      labels={DEFAULT_LABELS}
      isFiltersSearchActive={true}
      searchedText={""}
      //eslint-disable-next-line
      onShowFiltersAction={() => console.log("onShowFiltersAction")}
      //eslint-disable-next-line
      onInputSearchAction={() => console.log("onInputSearchAction")}
      //eslint-disable-next-line
      onNavigateAction={() => console.log("onNavigateAction")}
      //eslint-disable-next-line
      onResetInputSearchAction={() => console.log("onResetInputSearchAction")}
      //eslint-disable-next-line
      onRemoveFiltersAction={() => console.log("onRemoveFiltersActiona")}
      searchItemsCount={10}
    >
      <Toolbar.Navigation navigationItems={MOCK_NAVIGATION_ITEMS} />
      <Toolbar.Actions />
      <Toolbar.Results />
    </Toolbar>
  );
};

export const WithIsFilterSearchActiveNumber = () => {
  return (
    <Toolbar
      labels={DEFAULT_LABELS}
      isFiltersSearchActive={30}
      searchedText={""}
      //eslint-disable-next-line
      onShowFiltersAction={() => console.log("onShowFiltersAction")}
      //eslint-disable-next-line
      onInputSearchAction={() => console.log("onInputSearchAction")}
      //eslint-disable-next-line
      onNavigateAction={() => console.log("onNavigateAction")}
      //eslint-disable-next-line
      onResetInputSearchAction={() => console.log("onResetInputSearchAction")}
      //eslint-disable-next-line
      onRemoveFiltersAction={() => console.log("onRemoveFiltersActiona")}
      searchItemsCount={10}
    >
      <Toolbar.Navigation navigationItems={MOCK_NAVIGATION_ITEMS} />
      <Toolbar.Actions />
      <Toolbar.Results />
    </Toolbar>
  );
};

export const WithSortingOnDesktop = () => {
  return (
    <Toolbar
      labels={DEFAULT_LABELS}
      isFiltersSearchActive={false}
      searchedText={""}
      //eslint-disable-next-line
      onShowFiltersAction={() => {}}
      //eslint-disable-next-line
      onInputSearchAction={() => {}}
      //eslint-disable-next-line
      onNavigateAction={() => console.log("onNavigateAction")}
      //eslint-disable-next-line
      onResetInputSearchAction={() => console.log("onResetInputSearchAction")}
      //eslint-disable-next-line
      onRemoveFiltersAction={() => console.log("onRemoveFiltersActiona")}
      searchItemsCount={0}
    >
      <Toolbar.Navigation navigationItems={MOCK_NAVIGATION_ITEMS} />
      <Toolbar.Actions sortingConfig={MOCK_SORTING_CONFIG} />
      <Toolbar.Results />
    </Toolbar>
  );
};
export const WithSortingActive = () => {
  return (
    <Toolbar
      labels={DEFAULT_LABELS}
      isFiltersSearchActive={false}
      searchedText={""}
      //eslint-disable-next-line
      onShowFiltersAction={() => {}}
      //eslint-disable-next-line
      onInputSearchAction={() => {}}
      //eslint-disable-next-line
      onNavigateAction={() => console.log("onNavigateAction")}
      //eslint-disable-next-line
      onResetInputSearchAction={() => console.log("onResetInputSearchAction")}
      //eslint-disable-next-line
      onRemoveFiltersAction={() => console.log("onRemoveFiltersActiona")}
      searchItemsCount={0}
    >
      <Toolbar.Navigation navigationItems={MOCK_NAVIGATION_ITEMS} />
      <Toolbar.Actions
        sortingConfig={{
          ...MOCK_SORTING_CONFIG,
          isSortingActive: true,
        }}
      />
      <Toolbar.Results />
    </Toolbar>
  );
};
