import { NotificationBadge } from "@gx-design/notification-badge/src/NotificationBadge";
import type { Meta, StoryObj } from "@storybook/react";
import packageJson from "@gx-design/notification-badge/package.json";

/** NotificationBadge are used in user tables and in the main header and contains the user profile image. */
const meta: Meta<typeof NotificationBadge> = {
  title: "Components/NotificationBadge",
  component: NotificationBadge,
  parameters: {
    componentSubtitle: "Component",
    componentTitle: "NotificationBadge",
    componentSlug: "notification-badge",
    componentImport: "NotificationBadge",
    version: packageJson.version,
    packageName: packageJson.name,
  },
};

export default meta;

type Story = StoryObj<typeof NotificationBadge>;

const Template = (args) => <NotificationBadge {...args} />;

/**  It is a simple circle, used to indicate an unread notification. */
export const Dot = Template.bind({});
Dot.args = {};

/** Number variant contains a number communicating item count information */
export const Number = Template.bind({});
Number.args = {
  ...Dot.args,
  number: "42",
};

/** Notifications max value is a two-digit number,
if greater a '+' sign is added */
export const NumberMaxLength = Template.bind({});
NumberMaxLength.args = {
  ...Dot.args,
  number: "99+",
};
