import { useState } from "react";
import {
  PaginationBar,
  PaginationDropDown,
  PaginationLoadMore,
} from "@gx-design/pagination-bar/src/PaginationBar";
import { Pager } from "@gx-design/pager/src/Pager";
import type { Meta, StoryObj } from "@storybook/react";
import packageJson from "@gx-design/pagination-bar/package.json";

/** PaginationBar is used at the end of a Table that has more than 1 page of results.  
It helps users to navigate through results. */
const meta: Meta<typeof PaginationBar> = {
  title: "Components/PaginationBar",
  component: PaginationBar,
  subcomponents: {
    PaginationDropDown,
    PaginationLoadMore,
    PaginationPager: Pager,
  },
  parameters: {
    componentSubtitle: "Component",
    componentTitle: "PaginationBar",
    componentSlug: "pagination-bar",
    componentImport: "PaginationBar",
    version: packageJson.version,
    packageName: packageJson.name,
    docs: {
      source: {
        type: "code",
      },
    },
  },
};

export default meta;

type Story = StoryObj<typeof PaginationBar>;

export const Default = () => {
  const [activePage, setActivePage] = useState(1);
  const [activeResult, setActiveResult] = useState(10);
  return (
    <PaginationBar
      separatorString="di"
      resultString="Risultati"
      currentResults={30}
      totalResults={90}
    >
      <PaginationBar.DropDown
        onResultsChange={(results) => setActiveResult(results)}
        options={[10, 30, 50, 100]}
        value={activeResult}
        resultString="Risultati"
      />

      <PaginationBar.Pager
        activePage={activePage}
        maxPagesToShow={3}
        onPageClick={(pageNum) => {
          setActivePage(pageNum);
        }}
        totalPages={12}
      />
    </PaginationBar>
  );
};

export const LoadMore = () => {
  const [activeResult, setActiveResult] = useState(10);
  return (
    <PaginationBar
      separatorString="di"
      resultString="Risultati"
      currentResults={30}
      totalResults={90}
    >
      <PaginationBar.DropDown
        onResultsChange={(results) => setActiveResult(results)}
        options={[10, 30, 50, 100]}
        value={activeResult}
        resultString="Risultati"
      />

      <PaginationBar.PaginationLoadMore
        // eslint-disable-next-line
        onLoadMoreClick={() => console.log("Loading")}
        buttonText="Carica altri"
      />
    </PaginationBar>
  );
};
