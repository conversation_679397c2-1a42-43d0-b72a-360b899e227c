import { Icon } from "@gx-design/icon/src/Icon";
import type { Meta, StoryObj } from "@storybook/react";
import packageJson from "@gx-design/icon/package.json";

/** An icon is a visual representation of a command, device, directory, or common action. */
const meta: Meta<typeof Icon> = {
  title: "Components/Icon",
  component: Icon,
  parameters: {
    componentSubtitle: "Component",
    componentTitle: "Icon",
    componentSlug: "icon",
    componentImport: "icon",
    version: packageJson.version,
    packageName: packageJson.name,
  },
};

export default meta;

type Story = StoryObj<typeof Icon>;

export const Default = (args) => (
  <div style={{ fontSize: "24px" }}>
    <Icon {...args} />
  </div>
);
Default.args = {
  name: "note",
};
