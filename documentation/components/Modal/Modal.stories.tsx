import { Modal } from "@gx-design/modal/src/Modal";
import packageJson from "@gx-design/modal/package.json";
import type { Meta, StoryObj } from "@storybook/react";
import { Button } from "@gx-design/button/src/Button";
import { Icon } from "@gx-design/icon/src/Icon";
import { Input } from "@gx-design/input/src/Input";
import { useState } from "react";

/** A Modal is a type of overlay that can be used for confirming actions, asking for disambiguation, and presenting small forms. They generally allow the user to focus on a quick task without having to navigate to a different page. */
const meta: Meta<typeof Modal> = {
  title: "Components/Modal",
  component: Modal,
  parameters: {
    layout: "centered",
    componentSubtitle: "Component",
    componentTitle: "Modal",
    componentSlug: "modal",
    componentImport: "Modal",
    packageName: packageJson.name,
    version: packageJson.version,
  },
};

export default meta;

type Story = StoryObj<typeof Modal>;

const ModalWrapper = (props: any) => {
  const [isModalOpen, setIsModalOpen] = useState(false);

  const ModalExample = () => {
    return (
      <Modal
        isOpen={isModalOpen}
        title="Il Titolo della modale Il Titolo della modale Il Titolo della modale"
        size={props.size}
        bodyHeight={props.bodyHeight}
        onClose={() => setIsModalOpen(false)}
        onConfirm={() => setIsModalOpen(false)}
        headerActions={props.headerActions}
        footer={props.footer}
      >
        {props.minHeightContent ? (
          <h3>Un contenuto breve</h3>
        ) : props.maxHeightContent ? (
          <div className="gx-row">
            <div className="gx-col-xs-12 gx-box-row">
              <h3>
                Un contenuto molto lungo che sarà visibile solo scrollando il
                body della modale.
              </h3>
              <p>
                Lorem ipsum dolor sit amet, consectetur adipiscing elit. Morbi
                vehicula tellus in imperdiet semper. Donec tincidunt mauris at
                orci lacinia, eu fermentum quam sodales. Sed non volutpat nulla.
                Duis arcu ante, consequat in est quis, molestie tristique enim.
                Praesent orci ex, pulvinar non neque id, viverra cursus lacus.
                Donec nec efficitur urna. Sed elementum molestie metus eu
                sollicitudin. Cras nec ligula sapien. Maecenas malesuada mauris
                sit amet tellus elementum, et commodo ligula ullamcorper. Sed
                nunc tortor, imperdiet in fringilla id, egestas id erat. Aliquam
                dapibus suscipit lacinia. Vivamus accumsan nisl nec odio
                consequat, at bibendum ligula aliquam. Praesent cursus eros
                congue, vulputate sapien in, iaculis urna.
              </p>
              <p>
                Vestibulum nisi dui, tempor nec nisl ut, commodo vestibulum mi.
                Nunc varius tincidunt leo, sit amet rhoncus ex consectetur eget.
                In congue turpis sit amet ultricies tempor. Donec posuere
                ullamcorper libero, nec pulvinar nisi aliquet vitae. Fusce
                ultrices, tellus vel commodo gravida, mauris lectus mattis
                neque, ut tempor magna massa quis augue. Etiam pharetra risus
                vitae vestibulum tempor. Duis euismod dolor id eros semper
                sodales et in velit. Donec ultricies massa at iaculis vulputate.
              </p>
              <p>
                Lorem ipsum dolor sit amet, consectetur adipiscing elit. Morbi
                vehicula tellus in imperdiet semper. Donec tincidunt mauris at
                orci lacinia, eu fermentum quam sodales. Sed non volutpat nulla.
                Duis arcu ante, consequat in est quis, molestie tristique enim.
                Praesent orci ex, pulvinar non neque id, viverra cursus lacus.
                Donec nec efficitur urna. Sed elementum molestie metus eu
                sollicitudin. Cras nec ligula sapien. Maecenas malesuada mauris
                sit amet tellus elementum, et commodo ligula ullamcorper. Sed
                nunc tortor, imperdiet in fringilla id, egestas id erat. Aliquam
                dapibus suscipit lacinia. Vivamus accumsan nisl nec odio
                consequat, at bibendum ligula aliquam. Praesent cursus eros
                congue, vulputate sapien in, iaculis urna.
              </p>
              <p>
                Vestibulum nisi dui, tempor nec nisl ut, commodo vestibulum mi.
                Nunc varius tincidunt leo, sit amet rhoncus ex consectetur eget.
                In congue turpis sit amet ultricies tempor. Donec posuere
                ullamcorper libero, nec pulvinar nisi aliquet vitae. Fusce
                ultrices, tellus vel commodo gravida, mauris lectus mattis
                neque, ut tempor magna massa quis augue. Etiam pharetra risus
                vitae vestibulum tempor. Duis euismod dolor id eros semper
                sodales et in velit. Donec ultricies massa at iaculis vulputate.
              </p>
              <p>
                Lorem ipsum dolor sit amet, consectetur adipiscing elit. Morbi
                vehicula tellus in imperdiet semper. Donec tincidunt mauris at
                orci lacinia, eu fermentum quam sodales. Sed non volutpat nulla.
                Duis arcu ante, consequat in est quis, molestie tristique enim.
                Praesent orci ex, pulvinar non neque id, viverra cursus lacus.
                Donec nec efficitur urna. Sed elementum molestie metus eu
                sollicitudin. Cras nec ligula sapien. Maecenas malesuada mauris
                sit amet tellus elementum, et commodo ligula ullamcorper. Sed
                nunc tortor, imperdiet in fringilla id, egestas id erat. Aliquam
                dapibus suscipit lacinia. Vivamus accumsan nisl nec odio
                consequat, at bibendum ligula aliquam. Praesent cursus eros
                congue, vulputate sapien in, iaculis urna.
              </p>
              <p>
                Vestibulum nisi dui, tempor nec nisl ut, commodo vestibulum mi.
                Nunc varius tincidunt leo, sit amet rhoncus ex consectetur eget.
                In congue turpis sit amet ultricies tempor. Donec posuere
                ullamcorper libero, nec pulvinar nisi aliquet vitae. Fusce
                ultrices, tellus vel commodo gravida, mauris lectus mattis
                neque, ut tempor magna massa quis augue. Etiam pharetra risus
                vitae vestibulum tempor. Duis euismod dolor id eros semper
                sodales et in velit. Donec ultricies massa at iaculis vulputate.
              </p>
            </div>
            <div className="gx-col-xs-12 gx-col-md-6">
              <div className="gx-box-row">
                <Input label="Nome" required={true} />
              </div>
            </div>
            <div className="gx-col-xs-12 gx-col-md-6">
              <div className="gx-box-row">
                <Input label="Cognome" required={true} />
              </div>
            </div>
            <div className="gx-col-xs-12 gx-col-md-6">
              <div className="gx-box-row">
                <Input
                  label="Email"
                  type="email"
                  disabled
                  value="<EMAIL>"
                  required={true}
                />
              </div>
            </div>
            <div className="gx-col-xs-12 gx-col-md-6">
              <div className="gx-box-row">
                <Input
                  label="Telefono"
                  type="telefono"
                  disabled
                  value="+39 333 12 34 567"
                  required={true}
                />
              </div>
            </div>
          </div>
        ) : (
          <div className="gx-row">
            <div className="gx-col-xs-12 gx-box-row">
              <h3>Anagrafica cliente</h3>
              <p>
                Compila il form sottostante per aggiungere un nuovo cliente.
              </p>
            </div>
            <div className="gx-col-xs-12 gx-col-md-6">
              <div className="gx-box-row">
                <Input label="Nome" required={true} />
              </div>
            </div>
            <div className="gx-col-xs-12 gx-col-md-6">
              <div className="gx-box-row">
                <Input label="Cognome" required={true} />
              </div>
            </div>
            <div className="gx-col-xs-12 gx-col-md-6">
              <div className="gx-box-row">
                <Input
                  label="Email"
                  type="email"
                  disabled
                  value="<EMAIL>"
                  required={true}
                />
              </div>
            </div>
            <div className="gx-col-xs-12 gx-col-md-6">
              <div className="gx-box-row">
                <Input
                  label="Telefono"
                  type="telefono"
                  disabled
                  value="+39 333 12 34 567"
                  required={true}
                />
              </div>
            </div>
          </div>
        )}
      </Modal>
    );
  };

  return (
    <>
      <Button variant="accent" onClick={() => setIsModalOpen(true)}>
        Open modal
      </Button>
      <ModalExample />
    </>
  );
};

const ModalWrapperStack = (props: any) => {
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [isSecondModalOpen, setIsSecondModalOpen] = useState(false);

  const FirstModal = () => {
    return (
      <Modal
        isOpen={isModalOpen}
        title="Il Titolo della modale Il Titolo della modale Il Titolo della modale"
        size={props.size}
        bodyHeight={props.bodyHeight}
        onClose={() => setIsModalOpen(false)}
        onConfirm={() => setIsSecondModalOpen(true)}
        headerActions={props.headerActions}
        footer={props.footer}
      >
        <div className="gx-row">
          <div className="gx-col-xs-12 gx-box-row">
            <h3>Anagrafica cliente</h3>
            <p>Compila il form sottostante per aggiungere un nuovo cliente.</p>
          </div>
          <div className="gx-col-xs-12 gx-col-md-6">
            <div className="gx-box-row">
              <Input label="Nome" required={true} />
            </div>
          </div>
          <div className="gx-col-xs-12 gx-col-md-6">
            <div className="gx-box-row">
              <Input label="Cognome" required={true} />
            </div>
          </div>
          <div className="gx-col-xs-12 gx-col-md-6">
            <div className="gx-box-row">
              <Input
                label="Email"
                type="email"
                disabled
                value="<EMAIL>"
                required={true}
              />
            </div>
          </div>
        </div>
      </Modal>
    );
  };

  const SecondModal = () => {
    return (
      <Modal
        isOpen={isSecondModalOpen}
        title="Il Titolo della modale Il Titolo della modale Il Titolo della modale"
        onClose={() => setIsSecondModalOpen(false)}
        onConfirm={() => setIsSecondModalOpen(false)}
      >
        <div className="gx-row">
          <div className="gx-col-xs-12 gx-box-row">
            <h3>Anagrafica cliente</h3>
            <p>Compila il form sottostante per aggiungere un nuovo cliente.</p>
          </div>
        </div>
      </Modal>
    );
  };

  return (
    <>
      <Button variant="accent" onClick={() => setIsModalOpen(true)}>
        Open modal
      </Button>
      <FirstModal />
      <SecondModal />
    </>
  );
};

export const Default = () => {
  return <ModalWrapper footer="default" />;
};

export const SmallModal = () => {
  return <ModalWrapper footer="default" size="small" />;
};

export const LargeModal = () => {
  return <ModalWrapper footer="default" size="large" />;
};

export const FullScreenModal = () => {
  return <ModalWrapper footer="default" size="fullScreen" />;
};

export const MinHeightModal = () => {
  return (
    <ModalWrapper
      footer="default"
      size="large"
      bodyHeight="minHeight"
      minHeightContent
    />
  );
};

export const MaxHeightModal = () => {
  return (
    <ModalWrapper
      footer="default"
      size="large"
      bodyHeight="maxHeight"
      maxHeightContent
    />
  );
};

export const WithoutFooter = () => {
  return <ModalWrapper size="large" />;
};

export const WithHeaderActions = () => {
  return (
    <ModalWrapper
      footer="default"
      headerActions={
        <div className="gx-multiButton">
          <Button iconOnly>
            <Icon name="pencil" />
          </Button>
          <Button iconOnly>
            <Icon name="user-round" />
          </Button>
        </div>
      }
    />
  );
};

export const Stacking = () => {
  return <ModalWrapperStack />;
};
