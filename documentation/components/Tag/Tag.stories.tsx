import { Tag, TagProps } from "@gx-design/tag/src/Tag";
import type { Meta } from "@storybook/react";
import packageJson from "@gx-design/tag/package.json";

/** Tag is a small interactive label used to indicate selections of values.  
The component can be with or without an **icon**, **dismissable** or not. */
const meta: Meta<typeof Tag> = {
  title: "Components/Tag",
  component: Tag,
  parameters: {
    componentSubtitle: "Component",
    componentTitle: "Tag",
    componentSlug: "tag",
    componentImport: "Tag",
    componentAnatomy: {
      image: "images/anatomy/tag.png",
      legend: ["Container", "Icon", "Text", "Dismiss"],
    },
    version: packageJson.version,
    packageName: packageJson.name,
  },
};

export default meta;

const Template = (args) => <Tag {...args} />;

export const Default = Template.bind({});
Default.args = {
  text: "Lorem ipsum",
};

export const WithIcon = () => {
  return (
    <div style={{ display: "flex", flexWrap: "wrap" }}>
      <div style={{ margin: "8px" }}>
        <Tag text="Lorem ipsum" icon="image" />
      </div>
    </div>
  );
};

export const Dismissable = () => {
  return (
    <div style={{ display: "flex", flexWrap: "wrap" }}>
      <div style={{ margin: "8px" }}>
        <Tag
          dismissable
          text="Lorem ipsum"
          onCloseClick={() => {
            console.log("chiuso");
          }}
        />
      </div>
    </div>
  );
};

export const DismissableWithIcon = () => {
  return (
    <div style={{ display: "flex", flexWrap: "wrap" }}>
      <div style={{ margin: "8px" }}>
        <Tag
          icon="image"
          dismissable
          text="Lorem ipsum"
          onCloseClick={() => {
            console.log("chiuso");
          }}
        />
      </div>
    </div>
  );
};
