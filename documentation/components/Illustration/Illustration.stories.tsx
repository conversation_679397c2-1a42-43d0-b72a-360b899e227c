import { Illustration } from "@gx-design/illustration/src/Illustration";
import type { Meta, StoryObj } from "@storybook/react";
import packageJson from "@gx-design/illustration/package.json";

/** Simple illustration component. */
const meta: Meta<typeof Illustration> = {
  title: "Components/Illustration",
  component: Illustration,
  parameters: {
    componentSubtitle: "Component",
    componentTitle: "Illustration",
    componentSlug: "illustration",
    componentImport: "Illustration",
    version: packageJson.version,
    packageName: packageJson.name,
    // determines the styles to import (must be identical to a package name)
    styles: "@gx-design/icon",
  },
};

export default meta;

type Story = StoryObj<typeof Illustration>;

export const Default = (args) => (
  <div style={{ fontSize: "80px" }}>
    <Illustration {...args} />
  </div>
);
Default.args = {
  name: "country-house",
};
