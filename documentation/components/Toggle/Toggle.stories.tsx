import { Toggle, ToggleProps } from "@gx-design/toggle/src/Toggle";
import type { Meta, StoryObj } from "@storybook/react";
import packageJson from "@gx-design/toggle/package.json";

/** The Toggle is a user interface component used to represent a binary state, such as on/off or enabled/disabled. This element allows users to switch a function on or off with a simple tap or click, providing immediate visual feedback. */
const meta: Meta<typeof Toggle> = {
  title: "Components/Toggle",
  component: Toggle,
  parameters: {
    // design: {
    //   type: "figma",
    //   url: "URL",
    // },
    componentSubtitle: "Component",
    componentTitle: "Toggle",
    componentSlug: "toggle",
    componentImport: "Toggle",
    version: packageJson.version,
    packageName: packageJson.name,
    componentAnatomy: {
      image: "images/anatomy/toggle.png",
      legend: ["Handle", "Track"],
    },
  },
};

export default meta;

type Story = StoryObj<typeof Toggle>;

const Template: Story = {
  render: (args: ToggleProps) => <Toggle {...args} />,
};

/**  The toggle has two states: **false**, indicating that the feature is deactivated and not in use, and **true**, indicating that the feature is activated and currently in use. */
export const Default: Story = {
  ...Template,
  args: {
    label: "Label Toggle",
    id: "check-1",
    name: "check-1",
  },
};

/** Toggle without label */
export const WithoutLabel: Story = {
  ...Template,
  args: {
    ...Default.args,
    label: "",
  },
};

/** Indicates that the toggle is inactive and cannot be interacted with. */
export const Disabled: Story = {
  ...Template,
  args: {
    ...Default.args,
    disabled: true,
  },
};

/** Add an error to the Toggle. */
export const Error: Story = {
  ...Template,
  args: {
    ...Default.args,
    error: "Questo campo è obbligatorio",
    id: "check-error",
    name: "check-error",
  },
};
