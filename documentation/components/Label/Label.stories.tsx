import { Label, LabelProps } from "@gx-design/label/src/Label";
import type { Meta, StoryObj } from "@storybook/react";
import packageJson from "@gx-design/label/package.json";

/** The Label component is used to render accessible and styled labels for form inputs, with optional helper tooltips and required asterisks. */
const meta: Meta<typeof Label> = {
  title: "Components/Form/Label",
  component: Label,
  parameters: {
    componentSubtitle: "Component / Form",
    componentTitle: "Label",
    componentSlug: "label",
    componentImport: "Label",
    version: packageJson.version,
    packageName: packageJson.name,
  },
};

export default meta;

type Story = StoryObj<typeof Label>;

const Template: Story = {
  render: (args: LabelProps) => <Label {...args}>Default Label</Label>,
};

export const Default: Story = { ...Template };

export const Required: Story = {
  ...Template,
  args: {
    required: true,
  },
  render: (args: LabelProps) => <Label {...args}>Required Label</Label>,
};

export const WithTooltip: Story = {
  ...Template,
  args: {
    tooltipText: "This is some helpful information",
  },
  render: (args: LabelProps) => <Label {...args}>Label with Tooltip</Label>,
};

export const Hidden: Story = {
  ...Template,
  args: {
    isVisible: false,
  },
  render: (args: LabelProps) => <Label {...args}>Hidden Label</Label>,
};

export const TooltipAndRequired: Story = {
  ...Template,
  args: {
    required: true,
    tooltipText: "This is some helpful information",
  },
  render: (args: LabelProps) => (
    <Label {...args}>Tooltip and Required Label</Label>
  ),
};
