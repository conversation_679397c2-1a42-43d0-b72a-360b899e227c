import { Badge } from "@gx-design/badge/src/Badge";
import { Tooltip } from "@gx-design/tooltip/src/Tooltip";
import type { Meta, StoryObj } from "@storybook/react";
import packageJson from "@gx-design/badge/package.json";

/** It is a non-interactive element used to display descriptive information, such as status or descriptive labels, within a user interface. ** */
const meta: Meta<typeof Badge> = {
  title: "Components/Badge",
  component: Badge,
  parameters: {
    componentSubtitle: "Component",
    componentTitle: "Badge",
    componentSlug: "badge",
    componentImport: "Badge",
    componentAnatomy: {
      image: "images/anatomy/badge.png",
      legend: ["Container", "Icon", "Text"],
    },
    version: packageJson.version,
    packageName: packageJson.name,
  },
  argTypes: {
    style: {
      options: [
        "reversed",
        "brand",
        "promotion",
        "success",
        "success-high",
        "warning",
        "warning-high",
        "error",
        "error-high",
        "info",
        "info-high",
      ],
      description: "Specify the variant of the badge.",
      control: {
        type: "select",
        labels: {
          default: "Default",
          reversed: "Reversed",
          brand: "Brand",
          promotion: "Promotion",
          success: "Success",
          successHigh: "SuccessHigh",
          warning: "Warning",
          warningHigh: "WarningHigh",
          error: "Error",
          errorHigh: "ErrorHigh",
          info: "Info",
          infoHigh: "InfoHigh",
        },
      },
    },
  },
};

export default meta;

type Story = StoryObj<typeof Badge>;

const defaultText = "Text only";
const defaultTextIcon = "Text with Icon";
const defaultIcon = "note";

const badgeArgs = {
  defaultArgs: {
    text: defaultText,
  },
  textIconArgs: {
    text: defaultTextIcon,
    icon: defaultIcon,
  },
  iconArgs: {
    icon: defaultIcon,
  },
};

const renderBadgeContent = (args: any, badgeArgs: any) => (
  <div style={{ display: "flex" }}>
    <Badge {...args} {...badgeArgs.defaultArgs} />
    <Badge {...args} {...badgeArgs.textIconArgs} />
    <Tooltip position="top" text="Icon only">
      <Badge {...args} {...badgeArgs.iconArgs} />
    </Tooltip>
  </div>
);

/** Light version to be used as a basis for informing without communicating any status */
export const Default: Story = {
  render: ({ ...args }) => renderBadgeContent(args, badgeArgs),
  args: {
    style: undefined,
  },
};

/** Dark version to be used as a basis for informing without communicating any status */
export const Reversed: Story = {
  render: ({ ...args }) => renderBadgeContent(args, badgeArgs),
  args: {
    style: "reversed",
  },
};

/** To be used as when the brand wants to communicate the status of the system, for example to label a feature in beta */
export const Brand: Story = {
  render: ({ ...args }) => {
    const brandBadgeArgs = { ...badgeArgs };
    return renderBadgeContent(args, brandBadgeArgs);
  },
  args: {
    style: "brand",
  },
};

/** To be used when you need to notify the user of a new element on the page */
export const Promotion: Story = {
  render: ({ ...args }) => {
    const promotionBadgeArgs = { ...badgeArgs };
    const customIcon = "cockade";
    promotionBadgeArgs.textIconArgs.icon = customIcon;
    promotionBadgeArgs.iconArgs.icon = customIcon;
    return renderBadgeContent(args, promotionBadgeArgs);
  },
  args: {
    style: "promotion",
  },
};

/** To be used when positive, successful information must be communicated */
export const Success: Story = {
  render: ({ ...args }) => {
    const promotionBadgeArgs = { ...badgeArgs };
    const customIcon = "check";
    promotionBadgeArgs.textIconArgs.icon = customIcon;
    promotionBadgeArgs.iconArgs.icon = customIcon;
    return renderBadgeContent(args, promotionBadgeArgs);
  },
  args: {
    style: "success",
  },
};

/** To be used when positive, successful information must be communicated -> highlighted */
export const SuccessHigh: Story = {
  render: ({ ...args }) => {
    const promotionBadgeArgs = { ...badgeArgs };
    const customIcon = "double-arrow-top";
    promotionBadgeArgs.textIconArgs.icon = customIcon;
    promotionBadgeArgs.iconArgs.icon = customIcon;
    return renderBadgeContent(args, promotionBadgeArgs);
  },
  args: {
    style: "success-high",
  },
};

/** To be used when an alert or warning information must be communicated -> highlighted */
export const Warning: Story = {
  render: ({ ...args }) => {
    const promotionBadgeArgs = { ...badgeArgs };
    const customIcon = "exclamation-mark-circle";
    promotionBadgeArgs.textIconArgs.icon = customIcon;
    promotionBadgeArgs.iconArgs.icon = customIcon;
    return renderBadgeContent(args, promotionBadgeArgs);
  },
  args: {
    style: "warning",
  },
};

/** To be used when an alert or warning information must be communicated -> highlighted */
export const WarningHigh: Story = {
  render: ({ ...args }) => {
    const promotionBadgeArgs = { ...badgeArgs };
    const customIcon = "equal";
    promotionBadgeArgs.textIconArgs.icon = customIcon;
    promotionBadgeArgs.iconArgs.icon = customIcon;
    return renderBadgeContent(args, promotionBadgeArgs);
  },
  args: {
    style: "warning-high",
  },
};

/** To be used when critical information or an error must be communicated */
export const Error: Story = {
  render: ({ ...args }) => {
    const promotionBadgeArgs = { ...badgeArgs };
    const customIcon = "cross-circle";
    promotionBadgeArgs.textIconArgs.icon = customIcon;
    promotionBadgeArgs.iconArgs.icon = customIcon;
    return renderBadgeContent(args, promotionBadgeArgs);
  },
  args: {
    style: "error",
  },
};

/** To be used when critical information or an error must be communicated -> highlighted */
export const ErrorHigh: Story = {
  render: ({ ...args }) => {
    const promotionBadgeArgs = { ...badgeArgs };
    const customIcon = "double-arrow-bottom";
    promotionBadgeArgs.textIconArgs.icon = customIcon;
    promotionBadgeArgs.iconArgs.icon = customIcon;
    return renderBadgeContent(args, promotionBadgeArgs);
  },
  args: {
    style: "error-high",
  },
};

/** To be used when information must be communicated */
export const Info: Story = {
  render: ({ ...args }) => {
    const promotionBadgeArgs = { ...badgeArgs };
    const customIcon = "info-circle";
    promotionBadgeArgs.textIconArgs.icon = customIcon;
    promotionBadgeArgs.iconArgs.icon = customIcon;
    return renderBadgeContent(args, promotionBadgeArgs);
  },
  args: {
    style: "info",
  },
};

/** To be used when information must be communicated -> highlighted */
export const InfoHigh: Story = {
  render: ({ ...args }) => {
    const promotionBadgeArgs = { ...badgeArgs };
    const customIcon = "info-circle";
    promotionBadgeArgs.textIconArgs.icon = customIcon;
    promotionBadgeArgs.iconArgs.icon = customIcon;
    return renderBadgeContent(args, promotionBadgeArgs);
  },
  args: {
    style: "info-high",
  },
};
