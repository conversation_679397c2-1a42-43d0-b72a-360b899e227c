import { Checkbox, CheckboxProps } from "@gx-design/checkbox/src/Checkbox";
import type { <PERSON>a, StoryObj } from "@storybook/react";
import packageJson from "@gx-design/checkbox/package.json";

/** Checkboxes are used for a list of options where the user may select multiple options, including all or none. */
const meta: Meta<typeof Checkbox> = {
  title: "Components/Form/Checkbox",
  component: Checkbox,
  parameters: {
    design: {
      type: "figma",
      url: "https://www.figma.com/design/ibDgDz7mKyn1dzqRdBN2tg/branch/CtKszvkJp07Yy4XyU19bFn/UI-Kit---Gestionale?node-id=5389-9233&t=AoqZfIwdmmqYuCIs-0",
    },
    componentSubtitle: "Component / Form",
    componentTitle: "Checkbox",
    componentSlug: "checkbox",
    componentImport: "Checkbox",
    version: packageJson.version,
    packageName: packageJson.name,
    componentAnatomy: {
      image: "images/anatomy/check.png",
      legend: ["State Layer (hover)", "Check", "Text (Show on/off)"],
    },
  },
};

export default meta;

type Story = StoryObj<typeof Checkbox>;

const Template: Story = {
  render: (args: CheckboxProps) => <Checkbox {...args} />,
};

export const Default: Story = {
  ...Template,
  args: {
    label: "Checkbox label",
  },
};

/** A checkbox with a long text. */
export const LongText: Story = {
  ...Template,
  args: {
    label:
      "This is a very long label that spans multiple lines. Lorem ipsum dolor sit amet, consectetur adipiscing elit. Phasellus imperdiet, nulla et dictum interdum, nisi lorem egestas odio, vitae scelerisque enim ligula venenatis dolor. Maecenas nisl est, ultrices nec congue eget, auctor vitae massa. Fusce luctus vestibulum augue ut aliquet. Nunc sagittis dictum nisi, sed ullamcorper ipsum dignissim ac. In at libero sed nunc venenatis imperdiet sed ornare",
    id: "check-LongText",
    name: "check-LongText",
  },
};

/** Specify whether the Checkbox should be interactive or not. */
export const Disabled: Story = {
  ...Template,
  args: {
    label: "Checkbox label",
    disabled: true,
    id: "check-Disabled",
    name: "check-Disabled",
  },
};

/** Add an error to the Checkbox. */
export const Error: Story = {
  ...Template,
  args: {
    label: "Checkbox label",
    id: "check-Error",
    name: "check-Error",
    error: "Questo campo è obbligatorio",
  },
};

/** Sometimes we use the "button-like" version in forms. */
export const Button: Story = {
  ...Template,
  args: {
    label: "Checkbox label",
    id: "check-Button",
    name: "check-Button",
    variant: "button",
  },
};

/** Sometimes we use the "button-like" with icon and reversed order. */
export const ButtonWithIcon: Story = {
  ...Template,
  args: {
    label: "Checkbox label",
    variant: "button",
    isReversed: true,
    icon: "magic-wand",
    id: "check-ButtonWithIcon",
    name: "check-ButtonWithIcon",
  },
};

/** Sometimes we use the "button-like" with icon, reversed order and full width. */
export const ButtonWithIconFullWidth: Story = {
  ...Template,
  args: {
    label: "Checkbox label",
    variant: "button",
    isReversed: true,
    isFullWidth: true,
    icon: "magic-wand",
    id: "check-ButtonWithIconFullWidth",
    name: "check-ButtonWithIconFullWidth",
  },
};

/** Sometimes we use the "chip" version in the filters. */
export const Chip: Story = {
  render: () => (
    <div className="chips-wrapper">
      <Checkbox
        label="Checkbox 1"
        variant="chip"
        isReversed
        icon="magic-wand"
        id="chip-1"
        name="chip-1"
      />
      <Checkbox
        label="Checkbox 2"
        variant="chip"
        isReversed
        id="chip-2"
        name="chip-2"
      />
      <Checkbox
        label="Checkbox 3"
        variant="chip"
        isReversed
        icon="magic-wand"
        id="chip-3"
        name="chip-3"
      />
      <Checkbox
        label="Checkbox 4"
        variant="chip"
        isReversed
        id="chip-4"
        name="chip-4"
      />
    </div>
  ),
};

/**
 * We typically use the indeterminate state with "Select All" checkboxes to indicate that only some of the elements are selected.
 */
export const indeterminate: Story = {
  ...Template,
  args: {
    label: "Checkbox label",
    indeterminate: true,
  },
};
