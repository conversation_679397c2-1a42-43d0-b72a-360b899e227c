import { Button, ButtonProps } from "@gx-design/button/src/Button";
import { Icon } from "@gx-design/icon";
import type { Meta, StoryObj } from "@storybook/react";
import packageJson from "@gx-design/button/package.json";

/** Buttons allow users to take actions, and make choices, with a single tap.  
We use these variations of buttons: **Default, Accent** and **Ghost** in **default** and **small** sizes. */
const meta: Meta<typeof Button> = {
  title: "Components/Button",
  component: Button,
  parameters: {
    componentSubtitle: "Component",
    componentTitle: "Button",
    componentSlug: "button",
    componentImport: "Button",
    componentAnatomy: {
      image: "images/anatomy/button.png",
      legend: ["Container", "Icon Left/Right", "Text"],
    },
    version: packageJson.version,
    packageName: packageJson.name,
  },
};

export default meta;

type Story = StoryObj<typeof Button>;

const Template: Story = {
  render: (args: ButtonProps) => (
    <>
      <Button {...args}>Button</Button>{" "}
      <Button {...args} size="small">
        Button
      </Button>
    </>
  ),
};

export const Default: Story = { ...Template };

export const Accent: Story = {
  ...Template,
  args: {
    variant: "accent",
  },
};

export const Ghost: Story = {
  ...Template,
  args: {
    variant: "ghost",
  },
};

export const Chip: Story = {
  ...Template,
  args: {
    variant: "chip",
  },
  render: (args: ButtonProps) => <Button {...args}>Button</Button>,
};

export const Disabled: Story = {
  ...Template,
  args: {
    disabled: true,
  },
};

export const WithIcon: Story = {
  render: () => {
    return (
      <>
        <Button>
          <Icon name="sliders" />
          <span>Button</span>
        </Button>
        <Button size="small">
          <Icon name="sliders" />
          <span>Button</span>
        </Button>
        <Button variant="chip" dropdown>
          <Icon name="sliders" />
          <span>Button</span>
        </Button>
      </>
    );
  },
};

export const WithOnlyIcon: Story = {
  render: () => {
    return (
      <>
        <Button iconOnly>
          <Icon name="sliders" />
        </Button>
        <Button iconOnly size="small">
          <Icon name="sliders" />
        </Button>
        <Button iconOnly variant="chip">
          <Icon name="sliders" />
        </Button>
      </>
    );
  },
};

export const MultiButton: Story = {
  render: (args) => {
    return (
      <div style={{ padding: " 0" }}>
        <div className="gx-multiButton">
          <Button {...args} iconOnly>
            <Icon name="search" />
          </Button>
          <Button {...args} iconOnly>
            <Icon name="ellipsis" />
          </Button>
        </div>
      </div>
    );
  },
};

export const ButtonGroup: Story = {
  render: (args) => {
    return (
      <div className="gx-buttonGroup">
        <Button {...args}>Button</Button>
        <Button {...args}>Button</Button>
        <Button {...args}>Button</Button>
      </div>
    );
  },
};
