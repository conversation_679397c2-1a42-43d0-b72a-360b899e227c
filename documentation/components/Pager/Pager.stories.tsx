import { useState } from "react";
import { Pager } from "@gx-design/pager/src/Pager";
import type { Meta, StoryObj } from "@storybook/react";
import packageJson from "@gx-design/pager/package.json";

/** The Pagination component enables the user to select a specific page from a range of pages. */
const meta: Meta<typeof Pager> = {
  title: "Components/Pager",
  component: Pager,
  parameters: {
    componentSubtitle: "Component",
    componentTitle: "Pager",
    componentSlug: "pager",
    componentImport: "Pager",
    version: packageJson.version,
    packageName: packageJson.name,
  },
};

export default meta;

type Story = StoryObj<typeof Pager>;

export const Default = () => {
  const [activePage, setActivePage] = useState(1);
  return (
    <Pager
      totalPages={4}
      activePage={activePage}
      maxPagesToShow={3}
      onPageClick={(pageNum) => setActivePage(pageNum)}
    />
  );
};
