import { Radio, RadioGroup } from "@gx-design/radio/src/Radio";
import type { Meta, StoryObj } from "@storybook/react";
import packageJson from "@gx-design/radio/package.json";

/** Use radio buttons when the user needs to see all available options. If available options can be collapsed, consider using a Select component because it uses less space.
Radio buttons should have the most commonly used option selected by default.
Every Radio group should be wrapped in a div with class: `.gx-radio-wrapper` */
const meta: Meta<typeof Radio> = {
  title: "Components/Form/Radio",
  component: Radio,
  subcomponents: { RadioGroup },
  parameters: {
    design: {
      type: "figma",
      url: "https://www.figma.com/design/ibDgDz7mKyn1dzqRdBN2tg/branch/WHudE5EJN8YfZsxu2JvZ6p/UI-Kit---Gestionale?node-id=5647-324&t=aXCcLVtH8bzUQwtn-0",
    },
    componentSubtitle: "Component / Form",
    componentTitle: "Radio",
    componentSlug: "radio",
    componentImport: "Radio, RadioGroup",
    version: packageJson.version,
    packageName: packageJson.name,
    componentAnatomy: {
      image: "images/anatomy/radio.png",
      legend: ["State Layer (hover)", "Check", "Text (Show on/off)"],
    },
  },
};

export default meta;

type Story = StoryObj<typeof Radio>;

export const Default = (args) => {
  return (
    <RadioGroup {...args} label="Posizione" column>
      <Radio name="radio-default" defaultChecked label="In alto" />
      <Radio name="radio-default" label="Al centro" />
      <Radio name="radio-default" label="In basso" />
    </RadioGroup>
  );
};

export const Horizontal = (args) => {
  return (
    <RadioGroup
      {...args}
      label="Posizione"
      tooltipHelper="This is a tooltip helper"
    >
      <Radio name="radio-horizontal" defaultChecked label="In alto" />
      <Radio name="radio-horizontal" label="Al centro" />
      <Radio name="radio-horizontal" label="In basso" />
    </RadioGroup>
  );
};

export const WithTooltip = (args) => {
  return (
    <RadioGroup
      {...args}
      label="Posizione"
      tooltipHelper="This is a tooltip helper"
    >
      <Radio name="radio-horizontal" defaultChecked label="In alto" />
      <Radio name="radio-horizontal" label="Al centro" />
      <Radio name="radio-horizontal" label="In basso" />
    </RadioGroup>
  );
};

export const Error = (args) => {
  return (
    <RadioGroup {...args} label="Posizione" error="Questo campo è obbligatorio">
      <Radio name="radio-error" label="In alto" />
      <Radio name="radio-error" label="Al centro" />
      <Radio name="radio-error" label="In basso" />
    </RadioGroup>
  );
};

export const Disabled = (args) => {
  return (
    <RadioGroup {...args} label="Posizione">
      <Radio name="radio-disabled" defaultChecked label="In alto" />
      <Radio name="radio-disabled" disabled label="Al centro" />
      <Radio name="radio-disabled" disabled label="In basso" />
    </RadioGroup>
  );
};

export const Button = (args) => {
  return (
    <RadioGroup {...args} label="Posizione" variant="button">
      <Radio name="radio-button" label="In alto" />
      <Radio name="radio-button" defaultChecked label="Al centro" />
      <Radio name="radio-button" label="In basso" />
    </RadioGroup>
  );
};

export const ButtonWithIcon = (args) => {
  return (
    <RadioGroup {...args} label="Posizione" variant="button">
      <Radio
        name="radio-button-icon"
        label="In alto"
        icon="align-vertical-top"
      />
      <Radio
        name="radio-button-icon"
        defaultChecked
        label="Al centro"
        icon="align-vertical-center"
      />
      <Radio
        name="radio-button-icon"
        label="In basso"
        icon="align-vertical-bottom"
      />
    </RadioGroup>
  );
};

export const Chip = (args) => {
  return (
    <RadioGroup {...args} label="Bathrooms" variant="chip">
      <Radio name="chip-button-icon" defaultChecked label="1" />
      <Radio name="chip-button-icon" label="2" />
      <Radio name="chip-button-icon" label="3+" />
      <Radio name="chip-button-icon" label="with icon" icon="bath" />
    </RadioGroup>
  );
};
