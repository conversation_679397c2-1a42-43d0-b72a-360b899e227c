import { AddonInput } from "@gx-design/addon-input/src/AddonInput";
import type { Meta, StoryObj } from "@storybook/react";
import packageJson from "@gx-design/addon-input/package.json";

/** Addon inputs are inputs with left or right indicators, they can be icons or text and usually add additional informations about the input itself. */
const meta: Meta<typeof AddonInput> = {
  title: "Components/Form/AddonInput",
  component: AddonInput,
  parameters: {
    componentSubtitle: "Component / Form",
    componentTitle: "AddonInput",
    componentSlug: "addon-input",
    componentImport: "AddonInput",
    version: packageJson.version,
    packageName: packageJson.name,
    // determines the styles to import (must be identical to a package name)
    styles: "@gx-design/input",
  },
};

export default meta;

type Story = StoryObj<typeof AddonInput>;

const Template: Story = {
  render: (args) => <AddonInput {...args} />,
};

export const Default: Story = {
  ...Template,
  args: {
    label: "Nome",
    name: "Name",
    id: "name",
    placeholder: "Inserisci il tuo nome",
    addon: {
      position: "left",
      type: "text",
      value: "€/mese",
    },
  },
};

export const AddonRight: Story = {
  ...Template,
  args: {
    ...Default.args,
    addon: {
      ...Default.args?.addon,
      position: "right",
    },
  },
};

export const Icon: Story = {
  ...Template,
  args: {
    ...Default.args,
    addon: {
      position: "left",
      type: "icon",
      value: "user-round",
    },
  },
};

export const IconRight: Story = {
  ...Template,
  args: {
    ...Default.args,
    addon: {
      position: "right",
      type: "icon",
      value: "user-round",
    },
  },
};

export const Disabled: Story = {
  ...Template,
  args: {
    ...Default.args,
    disabled: true,
  },
};

export const Error: Story = {
  ...Template,
  args: {
    ...Default.args,
    error: "Questo campo è obbligatorio",
  },
};
