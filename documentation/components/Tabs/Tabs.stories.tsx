import { Tabs, TabsItem } from "@gx-design/tabs/src/Tabs";
import { Icon } from "@gx-design/icon/src/Icon";
import type { Meta, StoryObj } from "@storybook/react";
import { useState } from "react";
import packageJson from "@gx-design/tabs/package.json";

const items = [
  {
    text: "Profilo",
  },
  {
    text: "Password",
  },
  {
    text: "Impostazioni",
  },
];

const itemsWithStartElement = [
  {
    text: "Ristoran<PERSON>",
    startElement: <Icon name="poi-restaurant" />,
  },
  {
    text: "Supermercati",
    startElement: <Icon name="poi-market" />,
  },
  {
    text: "Scuole",
    startElement: <Icon name="poi-school" />,
  },
];

const itemsWithEndElement = [
  {
    text: "Tutte",
    endElement: <span>(568)</span>,
  },
  {
    text: "Attive",
    endElement: <span>(456)</span>,
  },
  {
    text: "Archiviate",
    endElement: <span>(112)</span>,
  },
];

/** Tabs make it easy to explore and switch between different views. */
const meta: Meta<typeof Tabs> = {
  title: "Components/Tabs",
  component: Tabs,
  subcomponents: { TabsItem },
  parameters: {
    componentSubtitle: "Component",
    componentTitle: "Tabs",
    componentSlug: "tabs",
    componentImport: "Tabs",
    version: packageJson.version,
    packageName: packageJson.name,
  },
};

export default meta;

type Story = StoryObj<typeof Tabs>;

export const Default = (args) => {
  const [active, setActive] = useState(0);

  return (
    <Tabs>
      {items.map((item, index) => {
        return (
          <TabsItem
            key={index}
            active={active === index}
            onClick={() => setActive(index)}
            {...item}
          />
        );
      })}
    </Tabs>
  );
};

export const WithStartElement = (args) => {
  const [active, setActive] = useState(0);

  return (
    <Tabs>
      {itemsWithStartElement.map((item, index) => {
        return (
          <TabsItem
            key={index}
            active={active === index}
            onClick={() => setActive(index)}
            {...item}
          />
        );
      })}
    </Tabs>
  );
};

WithStartElement.storyName = "With Start Element";

export const WithEndElement = (args) => {
  const [active, setActive] = useState(0);

  return (
    <Tabs>
      {itemsWithEndElement.map((item, index) => {
        return (
          <TabsItem
            key={index}
            active={active === index}
            onClick={() => setActive(index)}
            {...item}
          />
        );
      })}
    </Tabs>
  );
};

WithEndElement.storyName = "With End Element";
