import { Dropdown } from "@gx-design/dropdown/src/Dropdown";
import {
  ActionList,
  ActionListItem,
} from "@gx-design/action-list/src/ActionList";
import type { Meta, StoryObj } from "@storybook/react";
import packageJson from "@gx-design/dropdown/package.json";
import { Icon } from "@gx-design/icon/src/Icon";

/** A dropdown menu displays a list of actions or options to a user. */
const meta: Meta<typeof Dropdown> = {
  title: "Components/Dropdown",
  component: Dropdown,
  parameters: {
    layout: "centered",
    componentSubtitle: "Component",
    componentTitle: "Dropdown",
    componentSlug: "dropdown",
    componentImport: "Dropdown",
    version: packageJson.version,
    packageName: packageJson.name,
  },
};

export default meta;

type Story = StoryObj<typeof Dropdown>;

export const DefaultDropdown = (args) => {
  return (
    <>
      <Dropdown
        buttonIsIconOnly={true}
        position="bottomRight"
        buttonContent={<Icon name="ellipsis" />}
      >
        <ActionList>
          <ActionListItem text="Modifica" />
          <ActionListItem text="Elimina" />
          <ActionListItem text="Stampa" />
        </ActionList>
        <ActionList>
          <ActionListItem text="Modifica 2" />
          <ActionListItem text="Elimina 2" />
          <ActionListItem text="Stampa 2" />
        </ActionList>
      </Dropdown>
      <Dropdown
        buttonIsIconOnly={true}
        position="bottomRight"
        buttonSize="small"
        buttonContent={<Icon name="ellipsis" />}
      >
        <ActionList>
          <ActionListItem text="Modifica" />
          <ActionListItem text="Elimina" />
          <ActionListItem text="Stampa" />
        </ActionList>
        <ActionList>
          <ActionListItem text="Modifica 2" />
          <ActionListItem text="Elimina 2" />
          <ActionListItem text="Stampa 2" />
        </ActionList>
      </Dropdown>
    </>
  );
};

export const BottomLeft = (args) => {
  return (
    <Dropdown
      buttonIsIconOnly={true}
      position="bottomLeft"
      buttonContent={<Icon name="ellipsis" />}
    >
      <ActionList>
        <ActionListItem text="Modifica" />
        <ActionListItem text="Elimina" />
        <ActionListItem text="Stampa" />
      </ActionList>
    </Dropdown>
  );
};

export const TopLeft = (args) => {
  return (
    <Dropdown
      buttonIsIconOnly={true}
      position="topLeft"
      buttonContent={<Icon name="ellipsis" />}
    >
      <ActionList>
        <ActionListItem text="Modifica" />
        <ActionListItem text="Elimina" />
        <ActionListItem text="Stampa" />
      </ActionList>
    </Dropdown>
  );
};

export const TopRight = (args) => {
  return (
    <Dropdown
      buttonIsIconOnly={true}
      position="topRight"
      buttonContent={<Icon name="ellipsis" />}
    >
      <ActionList>
        <ActionListItem text="Modifica" />
        <ActionListItem text="Elimina" />
        <ActionListItem text="Stampa" />
      </ActionList>
    </Dropdown>
  );
};
