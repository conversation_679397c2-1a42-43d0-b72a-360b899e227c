import { <PERSON>ert } from "@gx-design/alert/src/Alert";
import type { Meta, StoryObj } from "@storybook/react";
import packageJson from "@gx-design/alert/package.json";

const componentName = "Alert";

/** An alert is an in-line message that attracts the user's attention without interrupting the user's task. They usually appear at the top of the primary content area or close to the item needing attention. It supports several variants (Info, Success, Warning, Critical) to provide context to the message. */
const meta: Meta<typeof Alert> = {
  title: "Components/Alert",
  component: Alert,
  parameters: {
    componentSubtitle: "Component",
    componentTitle: "Alert",
    componentSlug: "alert",
    componentImport: "Alert",
    componentAnatomy: {
      image: "images/anatomy/alert.png",
      legend: ["Container", "Icon", "Text", "Dismiss"],
    },
    design: {
      type: "figma",
      url: "https://www.figma.com/file/ibDgDz7mKyn1dzqRdBN2tg/UI-Kit---Gestionale?type=design&node-id=3-2&mode=design&t=AkRsn0kxiZwW4aQp-0",
    },
    version: packageJson.version,
    packageName: packageJson.name,
  },
};

export default meta;

type Story = StoryObj<typeof Alert>;

const Template: Story = {
  render: (args) => <Alert {...args} />,
};

/** Use them to guide a user’s attention to relevant details. */
export const InfoAlert: Story = {
  ...Template,
  args: {
    children: "This is an alert",
    style: "info",
    withMarginBottom: true,
  },
};

/** Success alerts confirm that an instruction from the user was processed successfully. */
export const SuccessAlert: Story = {
  ...Template,
  args: {
    ...InfoAlert.args,
    style: "success",
  },
};

/** Use warning alerts when you need to inform users about a potentially unfavorable situation that requires eventual action from them. */
export const WarningAlert: Story = {
  ...Template,
  args: {
    ...InfoAlert.args,
    style: "warning",
  },
};

/** Use critical alerts when something an issue needs to be resolved immediately. */
export const ErrorAlert: Story = {
  ...Template,
  args: {
    ...InfoAlert.args,
    style: "error",
  },
};

/** A small “x” in the top right corner is used to dismiss alert. Including the close button is optional and should not be included if it is critical for a user to read. */
export const DismissableAlert: Story = {
  ...Template,
  args: {
    ...InfoAlert.args,
    dismissable: true,
    // eslint-disable-next-line
    onDismiss: () => console.log("clicked dismiss"),
  },
};

export const WithSimpleLink: Story = {
  ...Template,
  args: {
    children: (
      <>
        <span>This is an alert. </span>
        <a>Learn more</a>
      </>
    ),
    style: "info",
    withMarginBottom: true,
  },
};

export const WithRichContent: Story = {
  ...Template,
  args: {
    children: (
      <>
        <p>Gentile Agenzia,</p>
        <p>
          Immobiliare.it ha da sempre a cuore la{" "}
          <strong>sicurezza dei propri clienti</strong>,pertanto, al fine di
          garantirti la massima protezione della tua area riservata, ti
          chiediamo di <strong>aggiornare ora la tua password</strong>.
        </p>
        <p>
          Per eseguire tale operazione sarà necessaria una verifica telefonica.
        </p>
        <p>
          Inoltre, come buona pratica di sicurezza, ti suggeriamo di utilizzare
          password:
        </p>
        <ul>
          <li>lunghe 6 o più caratteri;</li>
          <li>non facilmente prevedibili;</li>
          <li>composte da lettere maiuscole, minuscole e numeri.</li>
        </ul>
      </>
    ),
    style: "info",
    withMarginBottom: true,
  },
};
