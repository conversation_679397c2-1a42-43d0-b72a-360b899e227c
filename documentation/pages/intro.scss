@use "@gx-design/theme/styles" as *;
@use "@gx-design/tools/styles" as *;

// Welcome Box
.welcome-box {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  border-radius: space(xl);

  &__head {
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center;
    min-height: 400px;
    padding: space(3xl) space(xl) space(xl);
    background-color: color(background-selected);
    background-image: url("/gx-design/images/background-welcome.png");
    background-position: center bottom;
    background-size: 100%;
    background-repeat: no-repeat;
    border-bottom: 3px solid color(border-selected);

    h1 {
      color: color(content-high);
      margin-bottom: space(2xl);
      font-family: "Inter" !important;
    }

    &Box {
      display: block;
      background-color: color(background-main);
      max-width: 600px;
      padding: space(lg);
      color: color(content-medium);
      border-radius: radius(lg);

      p {
        font-size: 16px;
        line-height: 1.5;
        font-family: "Inter" !important;
      }
    }
  }

  &__content {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: space(lg);

    &Text {
      max-width: 600px;
      font-family: "Inter" !important;
    }
  }
}

.gxd-iconListWrapper {
  width: 100%;
  border: 1px solid color(border-main);
  border-radius: radius(sm);
  padding: space(lg);
  margin-top: 2rem;

  .gx-input-wrapper {
    margin-bottom: space(xl);
    max-width: 350px;
  }
}

.gxd-iconList {
  display: grid;
  grid-template-columns: 1fr 1fr 1fr 1fr 1fr;
  gap: space(md);
}

.gxd-iconBox {
  padding: space(xl) space(md) space(md);
  border: 1px solid color(border-main);
  border-radius: radius(sm);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: space-between;
  height: 150px;

  .gx-icon {
    font-size: 24px;
    flex-shrink: 0;
  }

  .gx-icon--illustration {
    font-size: 48px;
  }

  > div {
    text-align: center;
    @include typography(body-small);
  }
}

// Tokens Colors
$token-colors: (
  background-main,
  background-alt,
  background-reversed,
  background-brand,
  background-brand-alt,
  background-accent,
  background-action,
  background-selectable,
  background-selected,
  background-selected-high,
  background-info,
  background-success,
  background-warning,
  background-error,
  background-notification,
  content-high,
  content-medium,
  content-low,
  content-reversed,
  content-accent,
  content-action,
  content-selectable,
  content-selected,
  content-selected-high,
  content-info,
  content-success,
  content-warning,
  content-error,
  content-notification,
  border-main,
  border-reversed,
  border-action,
  border-selectable,
  border-selected,
  border-info,
  border-success,
  border-warning,
  border-error
);

// Tokens Spacing
$token-spacing: (xs, sm, md, lg, xl, 2xl, 3xl, 4xl);

// Tokens Radius
$token-radii: (sm, md, lg, rounded);

// Tokens elevations
$token-elevations: (fixed-top, fixed-bottom, raised, overlay);

.tokens-list {
  display: flex;
  flex-wrap: wrap;
  margin-bottom: space(xl);

  &__column {
    width: 100%;
  }

  &--colors {
    @include media("screen", ">=#{breakpoint(sm)}") {
      gap: space(lg);
    }

    @include media("screen", ">=#{breakpoint(sm)}") {
      .tokens-list {
        &__column {
          width: calc(#{percentage(1 / 3)} - #{space(md)});
        }
      }
    }
  }

  &__item {
    display: flex;
    padding: space(md) 0;
    border-bottom: 1px solid color(border-main);

    &Typo {
      padding: space(lg);
      border: 0.1rem solid color(border-main);
      border-radius: radius(md);
      background-color: color(background-alt);
    }

    &--typo {
      flex-direction: column;
      margin-top: space(lg);
    }

    &Variant {
      display: flex;
      margin-top: space(lg);

      > * + * {
        margin-left: space(lg);
      }

      .gx-title-2 {
        margin-left: space(md);
      }

      span {
        margin-top: space(sm);
      }
    }

    &Badge {
      display: inline-flex;
      padding: space(xs) space(md);
      border-radius: radius(rounded);
      @include typography(body-small);

      &--token {
        border: 1px solid color(border-main);
        margin-bottom: space(sm);
      }

      &--option {
        background-color: color(background-alt);
      }
    }

    &Name {
      display: flex;
      flex-direction: column;
      align-items: flex-start;
    }
  }

  .tokens-visibility {
    font-size: 2.4rem;
    margin-left: space(sm);
  }

  &__color {
    &Hex {
      flex-shrink: 0;
      width: 40px;
      height: 40px;
      margin-right: space(md);
      border-radius: radius(sm);
      border: 1px solid color(border-main);

      @each $token-color in $token-colors {
        &--#{$token-color} {
          background-color: color(#{$token-color});
        }
      }
    }
  }

  &__space {
    flex-shrink: 0;
    border: 0.1rem dashed #ff21b5;

    &Wrap {
      width: space(4xl);
      margin-right: space(md);
    }

    @each $token-space in $token-spacing {
      &--#{$token-space} {
        width: space($token-space);
        height: space($token-space);
      }
    }
  }

  &__radius {
    width: 140px;
    height: 56px;
    margin-right: space(md);
    border: 2px solid color(border-main);

    @each $token-radius in $token-radii {
      &--#{$token-radius} {
        border-radius: radius($token-radius);
      }
    }
  }

  &__elevation {
    width: 56px;
    height: 56px;
    margin: 0 space(lg) space(lg);
    border: 1px solid color(border-main);

    @each $token-elevation in $token-elevations {
      &--#{$token-elevation} {
        box-shadow: elevation($token-elevation);
      }
    }
  }
}

.typo-button {
  @include typography(button);
}

.typo-visibility {
  font-size: 2.4rem;
  margin-left: space(sm) !important;
}

.sb-documentation {
  font-family: "Inter";

  ul {
    list-style: disc;
    padding-left: 2rem;
  }

  p {
    font-size: 1.6rem;
  }

  h2 {
    margin-top: 3rem;
    margin-bottom: 1rem;
  }

  h3 {
    margin-bottom: 1rem;
    margin-top: 3rem;
  }
}

/**
 * Dracula Theme originally by Zeno Rocha [@zenorocha]
 * https://draculatheme.com/
 *
 * Ported for PrismJS by Albert Vallverdu [@byverdu]
 */

.prismjs {
  background-color: #37474f !important;
}
