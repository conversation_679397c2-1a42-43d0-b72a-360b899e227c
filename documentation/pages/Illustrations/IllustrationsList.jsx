import React, { useState } from "react";
import { Illustration } from "@gx-design/illustration";
import { illustrationSet } from "@gx-design/illustration-set";
import { Input } from "@gx-design/input";

export const IllustrationsList = () => {
  const [illustrations, setIllustrations] = useState(
    Object.keys(illustrationSet)
  );
  const [originalIllustrations, setOriginalIllustrations] = useState(
    Object.keys(illustrationSet)
  );

  const filterIllustrations = (value) => {
    const illustrationsCopy = [...originalIllustrations];
    const filteredIllustrations = illustrationsCopy.filter((illustration) => {
      return illustration.includes(value);
    });
    setIllustrations(filteredIllustrations);
  };

  return (
    <div className="gxd-iconListWrapper">
      <Input
        onChange={(e) => filterIllustrations(e.currentTarget.value)}
        label="Ricerca illustratione"
        placeholder="Digita il nome dell'illustrazione che stai cercando qui"
      />
      <div className="gxd-iconList">
        {illustrations.map((illustration) => {
          return (
            <div className="gxd-iconBox" key={illustration}>
              <Illustration name={illustration} />
              <div>{illustration}</div>
            </div>
          );
        })}
      </div>
    </div>
  );
};
