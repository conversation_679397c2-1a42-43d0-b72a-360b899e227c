import React from "react";
import clsx from "clsx";
import { Tag } from "@gx-design/tag";

export const TokenItem = ({ type, token, option, optionDesktop = [] }) => {
  const getTokenContent = () => {
    switch (type) {
      case "color":
        return (
          <div
            className={clsx(
              "tokens-list__colorHex",
              token && `tokens-list__colorHex--${token}`
            )}
          ></div>
        );
      case "elevation":
        return (
          <div
            className={clsx(
              "tokens-list__elevation",
              token && `tokens-list__elevation--${token}`
            )}
          ></div>
        );
      case "radius":
        return (
          <div
            className={clsx(
              "tokens-list__radius",
              token && `tokens-list__radius--${token}`
            )}
          ></div>
        );
      case "spacing":
        return (
          <div className="tokens-list__spaceWrap">
            <div
              className={clsx(
                "tokens-list__space",
                token && `tokens-list__space--${token}`
              )}
            ></div>
          </div>
        );
      default:
        return null;
    }
  };

  if (type === "typography") {
    return (
      <div className="tokens-list__item tokens-list__item--typo">
        <div className="tokens-list__itemPreview">
          <div className="tokens-list__itemPreviewHead">
            {token && (
              <code className="tokens-list__itemBadge tokens-list__itemBadge--token">
                {token}
              </code>
            )}
          </div>
          <div
            className={clsx(
              "tokens-list__itemTypo gx-text-ellipsis",
              token === "button" ? "typo-button" : `gx-${token}`
            )}
          >
            Ma la volpe col suo balzo ha raggiunto il quieto Fido
          </div>
        </div>
        <div className="tokens-list__itemVariant">
          {option && Array.isArray(option) ? (
            <div className="tokens-list__itemName">
              {optionDesktop &&
              Array.isArray(optionDesktop) &&
              optionDesktop.length > 0 ? (
                <div className="gx-title-2">
                  Mobile
                  <Tag
                    style="positive"
                    className="typo-visibility gx-is-hidden-md-up"
                    icon="eye"
                  />
                  <Tag
                    style="negative"
                    className="typo-visibility gx-is-hidden-sm-down"
                    icon="eye-off"
                  />
                </div>
              ) : null}
              <React.Fragment>
                {option.map((item, index) => (
                  <span
                    className="tokens-list__itemBadge tokens-list__itemBadge--option"
                    key={index}
                  >
                    {item}
                  </span>
                ))}
              </React.Fragment>
            </div>
          ) : (
            <span className="tokens-list__itemBadge tokens-list__itemBadge--option">
              {option}
            </span>
          )}
          {optionDesktop && !Array.isArray(optionDesktop) ? (
            <span className="tokens-list__itemBadge tokens-list__itemBadge--option">
              {optionDesktop}
            </span>
          ) : optionDesktop &&
            Array.isArray(optionDesktop) &&
            optionDesktop.length > 0 ? (
            <div className="tokens-list__itemName">
              <div className="gx-title-2">
                Desktop
                <Tag
                  style="positive"
                  className="typo-visibility gx-is-hidden-sm-down"
                  icon="eye"
                />
                <Tag
                  style="negative"
                  className="typo-visibility gx-is-hidden-md-up"
                  icon="eye-off"
                />
              </div>
              <React.Fragment>
                {optionDesktop.map((item, index) => (
                  <span
                    className="tokens-list__itemBadge tokens-list__itemBadge--option"
                    key={index}
                  >
                    {item}
                  </span>
                ))}
              </React.Fragment>
            </div>
          ) : null}
        </div>
      </div>
    );
  } else {
    return (
      <div
        className={clsx(
          "tokens-list__item",
          type && `tokens-list__item--${type}`
        )}
      >
        {getTokenContent()}
        <div className="tokens-list__itemName">
          {token && (
            <code className="tokens-list__itemBadge tokens-list__itemBadge--token">
              {token}
            </code>
          )}
          {option && (
            <span className="tokens-list__itemBadge tokens-list__itemBadge--option">
              {option}
            </span>
          )}
        </div>
      </div>
    );
  }
};
