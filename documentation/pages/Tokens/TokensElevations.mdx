import { Meta, Unstyled } from "@storybook/blocks";
import { TokenItem } from "./TokenItem";

<Meta title="Foundation/Tokens/Elevation" />

<Unstyled>
<div className="sb-documentation">
<h1 className="gx-display-2">Elevation</h1>

Elevations are used to elevate interface elements above the background.

<div className="tokens-list">
  <div className="tokens-list__column">
    <TokenItem
      type="elevation"
      token="fixed-top"
      option="0px 0px 2px 0px rgba(brand01-900, 0.16) / 0px 1px 4px 0px rgba(brand01-900, 0.12)"
    />
    <TokenItem
      type="elevation"
      token="fixed-bottom"
      option="0px 0px 2px 0px rgba(brand01-900, 0.16) / 0px -1px 4px 0px rgba(brand01-900, 0.12)"
    />
    <TokenItem
      type="elevation"
      token="raised"
      option="0px 4px 8px 0px rgba(brand01-900, 0.16) / 0px 8px 24px 0px rgba(brand01-900, 0.16)"
    />
    <TokenItem
      type="elevation"
      token="overlay"
      option="0px 8px 60px 0px rgba(brand01-900, 0.16) / 0px 12px 24px -4px rgba(brand01-900, 0.24)"
    />
  </div>
</div>
</div>
</Unstyled>
