import { Meta, Unstyled } from "@storybook/blocks";
import { TokenItem } from "./TokenItem";

<Meta title="Foundation/Tokens/Colors" />

<Unstyled>
<div className="sb-documentation">
  <h1 className="gx-display-2"> Colors</h1>

<h2 className="component-doc-heading">Background</h2>

They are surface and typically found behind the content.

### Static

Used for not interactive element that don’t perform an action.

{" "}

<div className="tokens-list tokens-list--colors">
  <div className="tokens-list__column">
    <TokenItem
      type="color"
      token="background-main"
      option="white"
      hex="#ffffff"
    />
    <TokenItem type="color" token="background-alt" option="gray-050" />
  </div>
  <div className="tokens-list__column">
    <TokenItem type="color" token="background-brand" option="brand01-500" />
    <TokenItem type="color" token="background-brand-alt" option="brand01-050" />
  </div>
  <div className="tokens-list__column">
    <TokenItem type="color" token="background-reversed" option="gray-900" />
  </div>
</div>

{" "}

<br />

### Interactive

Used for the UI elements that perform an interaction.

{" "}

<div className="tokens-list tokens-list--colors">
  <div className="tokens-list__column">
    <TokenItem
      type="color"
      token="background-accent"
      colorClass="red"
      option="white"
    />
    <TokenItem type="color" token="background-action" option="white" />
  </div>
  <div className="tokens-list__column">
    <TokenItem type="color" token="background-selectable" option="white" />
    <TokenItem
      type="color"
      token="background-selected-high"
      option="brand01-500"
    />
    <TokenItem type="color" token="background-selected" option="brand01-500" />
  </div>
  <div className="tokens-list__column">
    <TokenItem type="color" token="background-notification" option="red500" />
  </div>
</div>

{" "}

<br />

### Support

Semantic colors that support informations.

{" "}

<div className="tokens-list tokens-list--colors">
  <div className="tokens-list__column">
    <TokenItem type="color" token="background-info" option="blue-050" />
    <TokenItem type="color" token="background-success" option="green-050" />
  </div>
  <div className="tokens-list__column">
    <TokenItem type="color" token="background-warning" option="yellow-050" />
    <TokenItem type="color" token="background-error" option="red-050" />
  </div>
</div>

{" "}

<br />
<br />

<h2 className="component-doc-heading">Content</h2>

Content colors are applied over a background and are used for texts and icons

### Static

Used for not interactive element that don’t perform an action.

{" "}

<div className="tokens-list tokens-list--colors">
  <div className="tokens-list__column">
    <TokenItem type="color" token="content-high" option="gray-900" />
    <TokenItem type="color" token="content-medium" option="gray-700" />
    <TokenItem type="color" token="content-low" option="gray-400" />
  </div>
  <div className="tokens-list__column">
    <TokenItem type="color" token="content-reversed" option="white" />
  </div>
</div>

### Interactive

Used for the UI elements that perform an interaction.

{" "}

<div className="tokens-list tokens-list--colors">
  <div className="tokens-list__column">
    <TokenItem type="color" token="content-accent" option="white" />
    <TokenItem type="color" token="content-action" option="brand01-500" />
  </div>
  <div className="tokens-list__column">
    <TokenItem type="color" token="content-selectable" option="gray-900" />
    <TokenItem type="color" token="content-selected-high" option="white" />
    <TokenItem type="color" token="content-selected" option="brand01-500" />
  </div>
  <div className="tokens-list__column">
    <TokenItem type="color" token="content-notification" option="white" />
  </div>
</div>

### Support

Semantic colors that support informations.

{" "}

<div className="tokens-list tokens-list--colors">
  <div className="tokens-list__column">
    <TokenItem type="color" token="content-info" option="blue-600" />
    <TokenItem type="color" token="content-success" option="green-600" />
  </div>
  <div className="tokens-list__column">
    <TokenItem type="color" token="content-warning" option="yellow-600" />
    <TokenItem type="color" token="content-error" option="red-600" />
  </div>
</div>

{" "}

<br />
<br />

<h2 className="component-doc-heading">Border</h2>

Border colors are applied as border on elements.

### Static

Used for not interactive element that don’t perform an action.

{" "}

<div className="tokens-list tokens-list--colors">
  <div className="tokens-list__column">
    <TokenItem type="color" token="border-main" option="gray-200" />
    <TokenItem type="color" token="border-reversed" option="white" />
  </div>
</div>

### Interactive

Used for the UI elements that perform an interaction.

{" "}

<div className="tokens-list tokens-list--colors">
  <div className="tokens-list__column">
    <TokenItem type="color" token="border-action" option="brand01-200" />
  </div>
  <div className="tokens-list__column">
    <TokenItem type="color" token="border-selectable" option="gray-200" />
    <TokenItem type="color" token="border-selected" option="brand01-200" />
  </div>
</div>

### Support

Semantic colors that support informations.

  <div className="tokens-list tokens-list--colors">
    <div className="tokens-list__column">
      <TokenItem type="color" token="border-info" option="blue-300" />
      <TokenItem type="color" token="border-success" option="green-300" />
    </div>
    <div className="tokens-list__column">
      <TokenItem type="color" token="border-warning" option="yellow-400" />
      <TokenItem type="color" token="border-error" option="red-300" />
    </div>
  </div>
  </div>
</Unstyled>
