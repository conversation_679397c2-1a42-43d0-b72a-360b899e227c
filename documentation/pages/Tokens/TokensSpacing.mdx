import { Meta, Unstyled } from "@storybook/blocks";
import { TokenItem } from "./TokenItem";

<Meta title="Foundation/Tokens/Spacing" />

<Unstyled>
<div className="sb-documentation">
<h1 className="gx-display-2">Spacing</h1>

Spacing is the negative area between elements and components.
It is commonly controlled in code with margin and padding.

<div className="tokens-list">
  <div className="tokens-list__column">
    <TokenItem type="spacing" token="xs" option="4px" />
  </div>
  <div className="tokens-list__column">
    <TokenItem type="spacing" token="sm" option="8px" />
  </div>
  <div className="tokens-list__column">
    <TokenItem type="spacing" token="md" option="16px" />
  </div>
  <div className="tokens-list__column">
    <TokenItem type="spacing" token="lg" option="24px" />
  </div>
  <div className="tokens-list__column">
    <TokenItem type="spacing" token="xl" option="32px" />
  </div>
  <div className="tokens-list__column">
    <TokenItem type="spacing" token="2xl" option="48px" />
  </div>
  <div className="tokens-list__column">
    <TokenItem type="spacing" token="3xl" option="64px" />
  </div>
  <div className="tokens-list__column">
    <TokenItem type="spacing" token="4xl" option="80px" />
  </div>
</div>
</div>
</Unstyled>
