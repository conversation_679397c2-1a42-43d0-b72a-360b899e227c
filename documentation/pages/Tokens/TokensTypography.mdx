import { Meta, Unstyled } from "@storybook/blocks";
import { TokenItem } from "./TokenItem";

<Meta title="Foundation/Tokens/Typography" />

<Unstyled>
<div className="sb-documentation">
<h1 className="gx-display-2">Typography</h1>

The typographic styles we use to present the user interface and content as clearly and efficiently as possible.

<h2 className="component-doc-heading">Displays</h2>

Displays are used in landing pages to show big and bold titles

<div className="tokens-list">
  <div className="tokens-list__column">
    <TokenItem
      type="typography"
      token="display-1"
      option={[
        "font-size-2xl",
        "weight-medium",
        "line-height-2xl",
        "letter-spacing-md",
      ]}
      optionDesktop={[
        "font-size-3xl",
        "weight-medium",
        "line-height-3xl",
        "letter-spacing-md",
      ]}
    />
  </div>
  <div className="tokens-list__column">
    <TokenItem
      type="typography"
      token="display-2"
      option={[
        "font-size-xl",
        "weight-medium",
        "line-height-2xl",
        "letter-spacing-sm",
      ]}
    />
  </div>
  <div className="tokens-list__column">
    <TokenItem
      type="typography"
      token="display-subtitle"
      option={[
        "font-size-md",
        "weight-regular",
        "line-height-sm",
        "letter-spacing-null",
      ]}
    />
  </div>
</div>

<h2 className="component-doc-heading">Titles</h2>

Title are used as headings in the product UI and can be used in the landing pages.

<div className="tokens-list">
  <div className="tokens-list__column">
    <TokenItem
      type="typography"
      token="title-1"
      option={[
        "font-size-md",
        "weight-bold",
        "line-height-sm",
        "letter-spacing-xs",
      ]}
      optionDesktop={[
        "font-size-lg",
        "weight-bold",
        "line-height-md",
        "letter-spacing-xs",
      ]}
    />
  </div>
  <div className="tokens-list__column">
    <TokenItem
      type="typography"
      token="title-2"
      option={[
        "font-size-sm",
        "weight-bold",
        "line-height-sm",
        "letter-spacing-xs",
      ]}
      optionDesktop={[
        "font-size-sm",
        "weight-bold",
        "line-height-sm",
        "letter-spacing-xs",
      ]}
    />
  </div>
</div>

<h2 className="component-doc-heading">Body</h2>

Title are used as headings in the product UI and can be used in the landing pages.

<div className="tokens-list">
  <div className="tokens-list__column">
    <TokenItem
      type="typography"
      token="body"
      option={[
        "font-size-md",
        "weight-bold",
        "line-height-sm",
        "letter-spacing-xs",
      ]}
    />
  </div>
  <div className="tokens-list__column">
    <TokenItem
      type="typography"
      token="button"
      optionDesktop={[
        "font-size-sm",
        "weight-bold",
        "line-height-sm",
        "letter-spacing-xs",
      ]}
      option={[
        "font-size-sm",
        "weight-bold",
        "line-height-sm",
        "letter-spacing-xs",
      ]}
    />
  </div>
  <div className="tokens-list__column">
    <TokenItem
      type="typography"
      token="body-small"
      option={[
        "font-size-md",
        "weight-bold",
        "line-height-sm",
        "letter-spacing-xs",
      ]}
    />
  </div>
  <div className="tokens-list__column">
    <TokenItem
      type="typography"
      token="overline"
      option={[
        "font-size-sm",
        "weight-bold",
        "line-height-sm",
        "letter-spacing-xs",
      ]}
    />
  </div>
  <div className="tokens-list__column">
    <TokenItem
      type="typography"
      token="body-tiny"
      option={[
        "font-size-md",
        "weight-bold",
        "line-height-sm",
        "letter-spacing-xs",
      ]}
    />
  </div>
</div>

</div>
</Unstyled>
