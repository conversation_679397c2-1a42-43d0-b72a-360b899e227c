import { Meta, Unstyled } from "@storybook/blocks";
import { TokenItem } from "./TokenItem";
import { BREAKPOINTS } from "../../../packages/useMediaMatch/src/useMedia";

<Meta title="Foundation/Tokens/Breakpoint" />

<Unstyled>
<div className="sb-documentation">
  <h1 className="gx-display-2"> Breakpoint</h1>

Breakpoints set a visual point on a screen to adapt the design of content (responsive design), ensuring a consistent, optimised experience across different screen widths.

  <div className="tokens-list">
    {Object.entries(BREAKPOINTS).map(([key, value]) => (
      <div key={key} className="tokens-list__column">
        <TokenItem token={key} option={`${value}px`} />
      </div>
    ))}
  </div>
  </div>
</Unstyled>
