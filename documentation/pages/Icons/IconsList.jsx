import React, { useState } from "react";
import { Icon } from "@gx-design/icon";
import iconSet from "@pepita-fe/sprite-b2b/sprite.json";
import { Input } from "@gx-design/input";

export const IconsList = () => {
  const [icons, setIcons] = useState(
    iconSet.children.filter((x) => x.name === "symbol")
  );
  const [originalIcons, setOriginalIcons] = useState(
    iconSet.children.filter((x) => x.name === "symbol")
  );
  const filterIcons = (value) => {
    const iconsCopy = [...originalIcons];
    const filteredIcons = iconsCopy.filter((icon) => {
      return icon.attributes.id.includes(value);
    });
    setIcons(filteredIcons);
  };

  return (
    <div className="gxd-iconListWrapper">
      <Input
        onChange={(e) => filterIcons(e.currentTarget.value)}
        label="Ricerca icone"
        placeholder="Digita il nome dell'icona che stai cercando"
      />
      <div className="gxd-iconList">
        {icons.map((icon) => {
          return (
            <div className="gxd-iconBox" key={icon.attributes.id}>
              <Icon name={icon.attributes.id} />
              <div>{icon.attributes.id}</div>
            </div>
          );
        })}
      </div>
    </div>
  );
};
