@use "@gx-design/tools/styles" as tools;
@use "@gx-design/theme/styles" as theme;

$gx-size-devices: (xsm: (null, #{theme.breakpoint(sm)}),
    sm: (#{theme.breakpoint(sm)}, #{theme.breakpoint(md)}),
    md: (#{theme.breakpoint(md)}, #{theme.breakpoint(lg)}),
    lg: (#{theme.breakpoint(lg)}, #{theme.breakpoint(xl)}),
    xlg: (#{theme.breakpoint(xl)}, null));

.gx-is-hidden,
.hidden { // Classe di bootstrap (serve per far funzionare JS)
    display: none !important;
}

@each $gx-size-device,
$gx-size-device-sizes in $gx-size-devices {

    @if nth($gx-size-device-sizes, 1)==null {}

    @else {

        @include tools.media('screen', '>=#{nth($gx-size-device-sizes, 1)}') {

            .gx-is-hidden-#{$gx-size-device}-up {
                display: none !important;
            }

        }
    }

    @if nth($gx-size-device-sizes, 2)==null {}

    @else {

        @include tools.media('screen', '<#{nth($gx-size-device-sizes, 2)}') {

            .gx-is-hidden-#{$gx-size-device}-down {
                display: none !important;
            }

        }
    }


}

.gx-sr-only {
    position: absolute;
    width: 0.1rem;
    height: 0.1rem;
    padding: 0;
    margin: -0.1rem;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    border: 0;
}