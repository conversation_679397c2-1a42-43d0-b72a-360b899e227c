@use "@gx-design/theme/styles" as theme;

// gx-pulse
// amimazione pulsazione (es. stella preferiti)
@keyframes gx-pulse {
  from {
    transform: scale(1);
  }

  50% {
    transform: scale(1.2);
  }

  to {
    transform: scale(1);
  }
}

.gx-pulse {
  animation-name: gx-pulse;
  animation-duration: 0.3s;
}

// gx-spin
// amimazione rotazione (es. loading paginazione)
@keyframes gx-spin {
  0% {
    -webkit-transform: rotate(0deg);
    transform: rotate(0deg);
  }
  100% {
    -webkit-transform: rotate(359deg);
    transform: rotate(359deg);
  }
}

.gx-spin {
  animation: gx-spin 1.25s infinite linear;
}

// gx-skeleton
// amimazione skeleton (es. dashboard)
@-webkit-keyframes placeholderSkeleton {
  0% {
    background-position: -50rem 0;
  }
  100% {
    background-position: 50rem 0;
  }
}

@-webkit-keyframes skeletonAnimate {
  from {
    background-position: top left;
  }
  to {
    background-position: top right;
  }
}

$skeleton-color-1: rgba(theme.color(content-medium, true), 0.05);
$skeleton-color-2: rgba(theme.color(background-main, true), 0.2);

.gx-skeleton {
  position: relative;
  height: 10rem;
  border-radius: theme.radius(sm);
  animation-duration: 1s;
  animation-fill-mode: forwards;
  animation-iteration-count: infinite;
  animation-name: placeholderSkeleton;
  animation-timing-function: linear;
  background: $skeleton-color-1;
  background-image: -webkit-gradient(
    linear,
    left center,
    right center,
    from($skeleton-color-1),
    color-stop(0.2, $skeleton-color-2),
    color-stop(0.4, $skeleton-color-1),
    to($skeleton-color-1)
  );
  background-image: -webkit-linear-gradient(
    left,
    $skeleton-color-1 0%,
    $skeleton-color-2 20%,
    $skeleton-color-1 40%,
    $skeleton-color-1 100%
  );
  background-repeat: no-repeat;
  background-size: 100% 10rem;
}

// Gx slide
// Animazione alert notify
@keyframes slideInDown {
  from {
    transform: translateY(-100%);
    visibility: visible;
  }

  to {
    transform: translateY(0);
    opacity: 1;
  }
}

@keyframes slideOutUp {
  from {
    transform: translateY(0);
    opacity: 1;
  }

  to {
    visibility: hidden;
    opacity: 0;
    transform: translateY(-100%);
  }
}

.gx-slideInDown {
  animation-name: slideInDown;
}

.gx-slideOutUp {
  animation-name: slideOutUp;
}

.gx-animated {
  animation-duration: 0.3s;
  animation-fill-mode: both;
}

// fade animation (used in tooltip and popover components)
.gx-fade-in {
  animation: fadeIn 0.3s forwards;
}

.gx-fade-out {
  animation: fadeOut 0.3s forwards;
}

@keyframes fadeIn {
  from {
    opacity: 0
  }

  to {
    opacity: 1
  }
}

@keyframes fadeOut {
  from {
    opacity: 1
  }

  to {
    opacity: 0
  }
}