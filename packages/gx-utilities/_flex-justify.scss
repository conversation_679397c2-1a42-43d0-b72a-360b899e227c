@use "@gx-design/tools/styles" as tools;

$gx-devices: (
    xsm: (null, gx-small),
    sm: (gx-small, gx-medium),
    md: (gx-medium, gx-large),
    lg: (gx-large, gx-extra-large),
    xlg: (gx-extra-large, null)
);

$justify-statuses: (start, center, end);

@each $gx-device, $gx-device-sizes in $gx-devices {
    
    @if nth($gx-device-sizes, 1) == null {
        
        @each $justify-status in $justify-statuses {
            .gx-#{$justify-status} {
                @if $justify-status == center {
                    justify-content: center;
                } @else {
                    justify-content: flex-#{$justify-status};
                }
            }
        }
        
    } @else {
        
        @include tools.media('screen','>=#{nth($gx-device-sizes, 1)}') {
            @each $justify-status in $justify-statuses {

                .gx-#{$justify-status}-#{$gx-device}-up {
                    @if $justify-status == center {
                        justify-content: center;
                    } @else {
                        justify-content: flex-#{$justify-status};
                    }
                }
            }  
        }
    }

    @if nth($gx-device-sizes, 2) == null {
  
    } @else {

        @include tools.media('screen','<#{nth($gx-device-sizes, 2)}') {   
            @each $justify-status in $justify-statuses {

                .gx-#{$justify-status}-#{$gx-device}-down {
                    @if $justify-status == center {
                        justify-content: center;
                    } @else {
                        justify-content: flex-#{$justify-status};
                    }
                }
            }  
        }
    }

    
}
