# Change Log

All notable changes to this project will be documented in this file.
See [Conventional Commits](https://conventionalcommits.org) for commit guidelines.

## [1.1.4](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/utilities@1.1.3...@gx-design/utilities@1.1.4) (2025-07-17)

### Bug Fixes

- **gx-skeleton:** fixed border radius ([4366840](https://gitlab.pepita.io/getrix/gx-design/commit/43668401beefdde7909aefe932c013fac6d3583b))

## [1.1.3](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/utilities@1.1.2...@gx-design/utilities@1.1.3) (2025-07-08)

**Note:** Version bump only for package @gx-design/utilities

## [1.1.2](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/utilities@1.1.1...@gx-design/utilities@1.1.2) (2025-05-12)

### Bug Fixes

- **spacingsMargin:** update spacings for all margins ([b563957](https://gitlab.pepita.io/getrix/gx-design/commit/b5639579a9015b8bba8cfe84027755d9fe97e8fa))

## [1.1.1](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/utilities@1.1.0...@gx-design/utilities@1.1.1) (2025-02-11)

**Note:** Version bump only for package @gx-design/utilities

# 1.1.0 (2024-03-18)

**Note:** Version bump only for package @gx-design/utilities

# 1.1.0-alpha.0 (2024-03-07)

### Features

- **styles:** general ([fc4d50a](https://gitlab.pepita.io/getrix/gx-design/commit/fc4d50a8c23c848f36e9a3c9831cea4606eff985))
