import React, { createContext, useContext, useEffect, useState } from "react";
import { Icon } from "@gx-design/icon";
import { Button } from "@gx-design/button";
import clsx from "clsx";

// Props
export interface INotification {
  type: "error" | "success";
  message: string;
  /* If 0 notification will remain visible */
  autoClose?: number;
  onClose?: () => void;
}

export type INotifyContext = {
  showNotification: React.Dispatch<React.SetStateAction<INotification | null>>;
};

export type SnackbarProps = {
  notification: INotification | null;
  onClose?: () => void;
  /* If 0 notification will remain visible */
  autoClose?: number;
};

export const NotifyContext = createContext<INotifyContext | null>(null);

export const useNotifyContext = () => {
  const context = useContext(NotifyContext);
  if (!context) {
    throw new Error("useNotifyContext must be used within a NotifyProvider");
  }
  return context;
};

export const NotifyProvider: React.FC<React.PropsWithChildren<{}>> = ({
  children,
}) => {
  const [notification, showNotification] = useState<INotification | null>(null);

  return (
    <NotifyContext.Provider value={{ showNotification }}>
      {children}
      {notification && (
        <Snackbar
          onClose={() => (
            notification.onClose && notification.onClose(),
            showNotification(null)
          )}
          notification={notification}
          autoClose={notification.autoClose}
        />
      )}
    </NotifyContext.Provider>
  );
};

export const Snackbar: React.FC<SnackbarProps> = ({
  notification,
  onClose,
  autoClose = 3000,
}) => {
  enum VisibilityStatus {
    Hidden = "HIDDEN",
    Visible = "VISIBLE",
    IsHiding = "IS_HIDING",
  }
  const [visibilityStatus, setVisibilityStatus] = useState<VisibilityStatus>(
    VisibilityStatus.Hidden
  );

  const onCloseHandler = () => {
    setVisibilityStatus(VisibilityStatus.IsHiding);
    onClose &&
      setTimeout(() => {
        onClose();
      }, 300); // Timing is equal to css animation time
  };

  useEffect(() => {
    if (notification) {
      setVisibilityStatus(VisibilityStatus.Visible);
    }
  }, [notification]);

  useEffect(() => {
    if (!notification || !autoClose) return;

    const timeoutId = setTimeout(() => {
      onCloseHandler();
    }, autoClose);

    return () => {
      clearTimeout(timeoutId);
    };
  }, [notification]);

  const getIcons = (style: INotification["type"]) => {
    switch (style) {
      case "error":
        return "cross-circle";
      case "success":
        return "check-circle";
    }
  };

  return (
    <div
      className={clsx(`gx-snackbar-wrapper gx-animated`, {
        "gx-slideInDown": visibilityStatus === VisibilityStatus.Visible,
        "gx-slideOutUp": visibilityStatus === VisibilityStatus.IsHiding,
      })}
      data-testid="snackbar-wrapper"
    >
      <div
        className={clsx("gx-snackbar", `gx-snackbar--${notification?.type}`)}
        data-testid="snackbar"
      >
        <Button
          onClick={onCloseHandler}
          variant="ghost"
          iconOnly
          size="small"
          className="gx-snackbar__close"
          data-testid="snackbar-close-button"
        >
          <Icon name="cross" />
        </Button>
        <Icon name={getIcons(notification?.type || "success")} />
        <span>{notification?.message}</span>
      </div>
    </div>
  );
};
