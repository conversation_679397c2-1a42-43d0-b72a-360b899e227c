import { describe, it, expect, vi } from "vitest";
import { act, render, screen, waitFor } from "@testing-library/react";
import {
  Snackbar,
  INotification,
  useNotifyContext,
  NotifyProvider,
} from "./Snackbar";
import React, { useEffect } from "react";

const notificationType: INotification[] = [
  {
    type: "success",
    message: "Success message",
    autoClose: 3000,
  },
  {
    type: "error",
    message: "Error message",
    autoClose: 0,
  },
];
describe("Snackbar", () => {
  notificationType.forEach((nType) => {
    // Test for rendering the Snackbar component with different notification types
    it(`renders ${nType.type} notification and check message`, () => {
      render(<Snackbar notification={nType} />);
      const notificationElement = screen.getByText(nType.message);
      const snackbar = screen.getByTestId("snackbar");
      expect(notificationElement).toBeInTheDocument();
      expect(snackbar).toHaveClass(`gx-snackbar--${nType.type}`);
    });

    it(`renders ${nType.message} message`, () => {
      render(<Snackbar notification={nType} />);
      expect(screen.getByText(nType.message)).toBeInTheDocument();
    });
    // Test for autoClose functionality when autoClose is 0 or greater than 0
    it("handles autoClose functionality correctly", async () => {
      vi.useRealTimers();
      const spy = vi.fn();
      const AutoShow: React.FC = () => {
        const { showNotification } = useNotifyContext();

        useEffect(() => {
          showNotification({
            ...nType,
            onClose: () => {
              spy();
            },
          });
        }, [showNotification]);

        return null;
      };

      render(
        <NotifyProvider>
          <AutoShow />
        </NotifyProvider>
      );

      if (nType.autoClose !== 0) {
        await waitFor(
          () => {
            expect(spy).toHaveBeenCalled();
          },
          { timeout: 10000 }
        );
      } else {
        // If autoClose is 0
        expect(spy).not.toHaveBeenCalled();
      }
    });
    // Test for close button functionality
    it("checks closing function when clicking on the close button", async () => {
      render(<Snackbar notification={nType} />);
      const closeButton = screen.getByTestId("snackbar-close-button");
      expect(closeButton).toBeInTheDocument();

      act(() => {
        closeButton.click();
      });

      await waitFor(() => {
        expect(screen.getByTestId("snackbar-wrapper")).toHaveClass(
          "gx-slideOutUp"
        );
      });
    });
  });
});
