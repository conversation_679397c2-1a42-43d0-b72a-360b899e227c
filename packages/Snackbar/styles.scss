// ==========================================================================
// Snackbar - Snackbar/Components
// ==========================================================================
@use "@gx-design/theme/styles" as *;
@use "@gx-design/tools/styles" as *;

// Colors map
$gx-snackbar-color: (
  success: "success",
  error: "error",
);

.gx-snackbar {
  padding: space(md) space(2xl) space(md) space(md);
  border-width: 0.1rem;
  border-style: solid;
  border-radius: radius(md);
  position: relative;
  display: flex;
  align-items: center;
  color: color(content-high);
  background-color: color(background-main);
  border-color: color(border-main);

  &-wrapper {
    position: fixed;
    left: space(sm);
    right: space(sm);
    top: space(lg);
    margin: 0 auto;
    max-width: 50rem;
    opacity: 0;
    @include z-index(snackbar);

    @include media("screen", ">=#{breakpoint(sm)}") {
      left: 0;
      right: 0;
      min-width: 40rem;
    }
  }

  @each $snackbar-typology, $role in $gx-snackbar-color {
    &--#{$snackbar-typology} {
      background-color: color(background-#{$role});
      border-color: color(border-#{$role});

      > .gx-icon {
        color: color(content-#{$role});
      }
    }
  }

  > .gx-icon {
    flex-shrink: 0;
    align-self: flex-start;
    @include icon-size(md);

    + span {
      margin-left: space(md);
      @include typography(body-small);
    }
  }

  &__close {
    position: absolute;
    right: space(md);
    top: 50%;
    margin-top: -#{space(md)};
    cursor: pointer;
  }
}
