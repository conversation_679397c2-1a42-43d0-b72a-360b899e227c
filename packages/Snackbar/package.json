{"name": "@gx-design/snackbar", "version": "5.2.17", "description": "Gx Design Snackbar component", "source": "src/Snackbar.tsx", "main": "dist/index.js", "module": "dist/index.mjs", "peerDependencies": {"react": "*"}, "devDependencies": {"tsup": "^7.2.0"}, "dependencies": {"@gx-design/button": "^5.3.13", "@gx-design/icon": "^5.6.3", "@gx-design/theme": "^1.4.0"}, "scripts": {"build": "tsup", "watch": "tsup --watch"}, "repository": {"type": "git", "url": "********************:getrix/gx-design.git", "directory": "packages/Snackbar"}, "typings": "dist/index.d.ts", "volta": {"extends": "../../package.json"}}