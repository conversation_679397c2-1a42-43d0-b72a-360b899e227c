import React, { createContext, ReactElement, RefObject } from "react";

type DropdownContextProps = {
  /**
   * The root element where the dropdowns will be rendered.
   * If not provided, dropdowns will render in the body by default.
   */
  dropdownAnchorElement?: RefObject<HTMLElement | null>;
  /**
   * Function to set the open state of the dropdown.
   * This function is used to reflect the open state of the dropdown in the context.
   */
  onDropdownChange?: (isOpen: boolean) => void;
};

/**
 * ### ⚠️ WARNING ⚠️
 * This Context is intended to be used by the **Dropdown component only**
 * and should not be used directly by other components.
 *
 * It is designed to provide a way for dropdowns to render in a specific root element
 */
export const dropdownContext = createContext<DropdownContextProps | undefined>(
  undefined
);

/**
 * ### DropdownProvider component.
 *
 * If declared the Dropdown will render a portal using the children element ref as the root element.
 *
 * It also acceprts an `onDropdownChange` prop that will be called whenever the dropdown state changes.
 */
export const DropdownProvider = ({
  children,
  onDropdownChange,
  dropdownAnchorElement,
}: {
  children: ReactElement;
  onDropdownChange?: (isOpen: boolean) => void;
  dropdownAnchorElement?: RefObject<HTMLElement | null>;
}) => {
  return (
    <dropdownContext.Provider
      value={{
        dropdownAnchorElement,
        onDropdownChange,
      }}
    >
      {children}
    </dropdownContext.Provider>
  );
};

DropdownProvider.displayName = "DropdownProvider";
