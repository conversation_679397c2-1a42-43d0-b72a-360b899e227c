import { expect, it, vi } from "vitest";
import { render, screen, waitFor } from "../../../test/utilities";
import { DropdownProvider } from "./DropdownProvider";
import { Dropdown } from "./Dropdown";
import { useRef } from "react";

// Mock external modules
vi.mock("@gx-design/use-forked-refs", async () => {
  const actual = await vi.importActual<any>("@gx-design/use-forked-refs");
  return actual.default;
});

it("renders children correctly", () => {
  render(
    <DropdownProvider>
      <div data-testid="provider-child">Provider Child</div>
    </DropdownProvider>
  );

  expect(screen.getByTestId("provider-child")).toBeInTheDocument();
});

it("calls onDropdownChange when dropdown state changes", async () => {
  const onDropdownChange = vi.fn();
  const { user } = render(
    <DropdownProvider onDropdownChange={onDropdownChange}>
      <div data-testid="root-container">
        <Dropdown buttonContent="Test Button">
          <div>
            <button>Dropdown Item</button>
          </div>
        </Dropdown>
      </div>
    </DropdownProvider>
  );

  // Click to open the dropdown
  await user.click(screen.getByRole("button", { name: "Test Button" }));

  // Verify that onDropdownChange was called with true
  await waitFor(() => {
    expect(onDropdownChange).toHaveBeenCalledWith(true);
  });

  // Click to close the dropdown
  await user.click(screen.getByRole("button", { name: "Test Button" }));

  // Verify that onDropdownChange was called with false
  await waitFor(() => {
    expect(onDropdownChange).toHaveBeenCalledWith(false);
  });
});

it("renders dropdown inside provider container instead of body", async () => {
  const ComponentToRender = () => {
    const containerRef = useRef<HTMLDivElement>(null);

    return (
      <div>
        <div data-testid="outside-container">Outside content</div>
        <div data-testid="provider-container" ref={containerRef}>
          <DropdownProvider dropdownAnchorElement={containerRef}>
            <Dropdown buttonContent="Test Button">
              <div>
                <button data-testid="dropdown-item">Dropdown Item</button>
              </div>
            </Dropdown>
          </DropdownProvider>
        </div>
      </div>
    );
  };
  const { user } = render(<ComponentToRender />);

  const button = screen.getByRole("button", { name: "Test Button" });

  await user.click(button);

  const providerContainer = screen.getByTestId("provider-container");
  const dropdownItem = screen.getByTestId("dropdown-item");

  // Verify that the dropdown is a child of the provider container

  expect(providerContainer).toContainElement(dropdownItem);

  // Verify that the dropdown is NOT a direct child of the body
  expect(Array.from(document.body.children)).not.toContain(dropdownItem);
});

it("renders dropdown inside the body if provider is not declared", async () => {
  const { user } = render(
    <div data-testid="root-container">
      <Dropdown buttonContent="Click me">
        <div data-testid="dropdown-content">
          <button>Dropdown Item</button>
        </div>
      </Dropdown>
    </div>
  );

  await user.click(screen.getByRole("button", { name: "Click me" }));

  const dropDownContent = screen.getByTestId("dropdown-content");
  const rootContainer = screen.getByTestId("root-container");

  // Verify that the dropdown content is rendered in the body and not in the root container
  expect(rootContainer).not.toContainElement(dropDownContent);
});

it("works without onDropdownChange callback", async () => {
  const { user } = render(
    <DropdownProvider>
      <div data-testid="root-container">
        <Dropdown buttonContent="Test Button">
          <div>
            <button>Dropdown Item</button>
          </div>
        </Dropdown>
      </div>
    </DropdownProvider>
  );

  // Should work without errors even without callback
  await user.click(screen.getByRole("button", { name: "Test Button" }));

  expect(screen.getByText("Dropdown Item")).toBeInTheDocument();
});

it("sets display name correctly", () => {
  expect(DropdownProvider.displayName).toBe("DropdownProvider");
});
