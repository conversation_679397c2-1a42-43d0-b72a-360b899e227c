import React, {
  useState,
  useRef,
  ReactElement,
  useEffect,
  useLayoutEffect,
} from "react";
import { Button, ButtonProps } from "@gx-design/button";
import { ActionListProps } from "@gx-design/action-list";
import { Icon } from "@gx-design/icon";
import { createPortal } from "react-dom";
import clsx from "clsx";
import useForkedRefs from "@gx-design/use-forked-refs";
import { useOnClickOutside } from "@gx-design/use-on-click-outside";
import { dropdownContext } from "./DropdownProvider";

// Props
export type DropdownProps = {
  /**
   The Dropdown's content
  */
  children:
    | ReactElement<ActionListProps>
    | Array<ReactElement<ActionListProps>>;
  /**
    Custom class names
  */
  className?: string;
  /**
   The Dropdown's position, defaults to `bottomLeft`
  */
  position?: "topLeft" | "topRight" | "bottomLeft" | "bottomRight";
  /**
   The content of the button that will trigger the dropdown
  */
  buttonContent: React.ReactNode;
  /**
   Dertermine if the trigger button is an iconOnlyButton; if `showCaret === true`, please set this prop to `false`
  */
  buttonIsIconOnly?: boolean;
  /**
   Custom classname for the button
  */
  buttonClassName?: string;
  /**
   Custom style for the button
  */
  buttonVariant?: ButtonProps["variant"];
  /**
   Determine size for the button
  */
  buttonSize?: ButtonProps["size"];
  /**
   Determine if the dropdown has a maxHeight of 280px
  */
  maxHeight?: boolean;
  /**
   Is Dropdown disabled
  */
  disabled?: boolean;
  /**
   Show animated caret, defaults to `false`
  */
  showCaret?: boolean;
  /**
    Test id for testing purposes
  */
  ["data-testid"]?: string;
};

export const Dropdown = React.forwardRef<HTMLElement, DropdownProps>(
  (
    {
      children,
      className,
      position = "bottomLeft",
      maxHeight,
      buttonContent,
      buttonIsIconOnly,
      buttonClassName,
      buttonVariant,
      buttonSize,
      disabled,
      showCaret,
      ["data-testid"]: dataTestId,
    },
    ref
  ) => {
    const dropDownContext = React.useContext(dropdownContext);

    const rootElement =
      dropDownContext?.dropdownAnchorElement?.current || document.body;

    const onDropdownChange = dropDownContext?.onDropdownChange;

    const [openDropdown, setOpenDropdown] = useState(false);
    const buttonRef = useRef<HTMLElement>(null);
    const innerRef = useRef<HTMLElement>(null);
    const dropdownRef = useForkedRefs(innerRef, ref);

    type CoordsProps = {
      top: number;
      left: number;
    };

    useOnClickOutside({
      refs: [innerRef, buttonRef],
      handler: () => setOpenDropdown(false),
      shouldListen: openDropdown,
    });

    const [coords, setCoords] = useState<CoordsProps>({
      top: 0,
      left: 0,
    });

    /**
     * Updates the dropdown position when the dropdown is opened.
     * This is necessary to ensure the dropdown is positioned correctly
     */
    useLayoutEffect(() => {
      const dropdownPositionHandler = () => {
        if (
          !buttonRef ||
          !buttonRef.current ||
          !innerRef ||
          !innerRef.current
        ) {
          return;
        }

        const buttonRect = buttonRef?.current?.getBoundingClientRect();
        const dropdownRect = innerRef?.current?.getBoundingClientRect();
        const rootRect = rootElement?.getBoundingClientRect?.();

        // Adjust coordinates relative to rootElement if possible
        const buttonX =
          buttonRect && rootRect
            ? buttonRect.x - rootRect.x
            : buttonRect?.x ?? 0;
        const buttonY =
          buttonRect && rootRect
            ? buttonRect.y - rootRect.y
            : buttonRect?.y ?? 0;

        const gxSpacing: number = 4;

        switch (position) {
          case "topLeft":
            setCoords({
              left: buttonX + rootElement.scrollLeft,
              top:
                buttonY -
                dropdownRect.height -
                gxSpacing +
                rootElement.scrollTop,
            });
            break;
          case "topRight":
            setCoords({
              left:
                buttonX +
                buttonRect.width -
                dropdownRect.width +
                rootElement.scrollLeft,
              top:
                buttonY -
                dropdownRect.height -
                gxSpacing +
                rootElement.scrollTop,
            });
            break;
          case "bottomRight":
            setCoords({
              left:
                buttonX +
                buttonRect.width -
                dropdownRect.width +
                rootElement.scrollLeft,
              top:
                buttonY + buttonRect.height + gxSpacing + rootElement.scrollTop,
            });
            break;
          case "bottomLeft":
          default:
            setCoords({
              left: buttonX + rootElement.scrollLeft,
              top:
                buttonY + buttonRect.height + gxSpacing + rootElement.scrollTop,
            });
            break;
        }
      };
      if (openDropdown) {
        dropdownPositionHandler();
      }

      window.addEventListener("resize", dropdownPositionHandler);
      return () => {
        window.removeEventListener("resize", dropdownPositionHandler);
      };
    }, [openDropdown, rootElement, position]);

    /**
     * Updates the open state of the dropdown in the context.
     */
    useEffect(() => {
      if (onDropdownChange) {
        onDropdownChange(openDropdown);
      }
    }, [openDropdown, onDropdownChange]);

    const onButtonClick = () => setOpenDropdown((prevState) => !prevState);

    return (
      <>
        <Button
          disabled={disabled}
          className={clsx(buttonClassName, {
            "gx-dropupButton": showCaret,
            "is-open": openDropdown,
          })}
          iconOnly={buttonIsIconOnly}
          variant={buttonVariant}
          size={buttonSize}
          ref={buttonRef}
          onClick={onButtonClick}
        >
          {buttonContent}
          {showCaret && (
            <Icon
              data-testid={dataTestId ? `${dataTestId}_dropdown_caret` : ""}
              className="gx-button__caret"
              name="arrow-down"
            />
          )}
        </Button>
        {openDropdown &&
          createPortal(
            <div
              style={{
                top: coords.top,
                left: coords.left,
                display: "block",
              }}
              ref={dropdownRef}
              className={clsx(className, `gx-dropdown`, {
                "gx-dropdown--maxHeight": maxHeight,
              })}
            >
              {/* // This is needed to close the dropdown after ActionListItemClick  */}
              {React.Children.map(children, (child: any) => {
                if (child) {
                  const actionListItemsArray = Array.isArray(
                    child.props.children
                  )
                    ? child.props.children
                    : [child.props.children];
                  return React.cloneElement(child, {
                    children: actionListItemsArray.map(
                      (actionListItem: any) => {
                        if (actionListItem) {
                          return React.cloneElement(actionListItem, {
                            onClick: (ev) => {
                              {
                                actionListItem.props.onClick &&
                                  actionListItem.props.onClick(ev);
                              }
                              setOpenDropdown(false);
                            },
                          });
                        }
                      }
                    ),
                  });
                }
              })}
            </div>,
            rootElement
          )}
      </>
    );
  }
);

Dropdown.displayName = "Dropdown";
export { DropdownProvider } from "./DropdownProvider";
