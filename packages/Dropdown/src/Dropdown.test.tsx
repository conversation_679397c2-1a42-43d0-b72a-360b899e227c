import { expect, it, vi } from "vitest";
import { render, screen, waitFor } from "../../../test/utilities";
import { Dropdown } from "./Dropdown";

vi.mock("@gx-design/use-forked-refs", async () => {
  const actual = await vi.importActual<any>("@gx-design/use-forked-refs");
  return actual.default;
});

it("renders button content", () => {
  render(
    <Dropdown buttonContent="Click me" showCaret>
      <div>Dropdown content</div>
    </Dropdown>
  );

  const dropdownElement = screen.getByRole("button", { name: "Click me" });

  expect(dropdownElement).toBeInTheDocument();
});

it("renders the dropdown with caret icon", () => {
  render(
    <Dropdown data-testid="test" buttonContent="Click me" showCaret>
      <div>Dropdown content</div>
    </Dropdown>
  );

  expect(screen.getByTestId("test_dropdown_caret")).toBeInTheDocument();
});

it("opens dropdown on button click", async () => {
  const { user } = render(
    <Dropdown buttonContent="Click me">
      <div>
        <button>Dropdown content 1</button>
        <button>Dropdown content 2</button>
      </div>
      <div>
        <button>Dropdown content 3</button>
        <button>Dropdown content 4</button>
      </div>
    </Dropdown>
  );

  const buttonElement = screen.getByRole("button", { name: "Click me" });

  await user.click(buttonElement);

  const hiddenElement = await screen.findByRole("button", {
    name: "Dropdown content 1",
  });

  expect(hiddenElement).toBeVisible();
});

it("crashes when no wrapper is provided", async () => {
  const { user } = render(
    <Dropdown buttonContent="Click me">
      <button>Dropdown content 1</button>
      <button>Dropdown content 2</button>
    </Dropdown>
  );

  const buttonElement = screen.getByRole("button", { name: "Click me" });

  await expect(user.click(buttonElement)).rejects.toThrowError(
    "Element type is invalid: expected a string (for built-in components) or a class/function (for composite components) but got: undefined. You likely forgot to export your component from the file it's defined in, or you might have mixed up default and named imports."
  );
});

it("closes dropdown on outside click", async () => {
  const { user } = render(
    <Dropdown buttonContent="Click me">
      <div>
        <button>Dropdown content 1</button>
        <button>Dropdown content 2</button>
      </div>
    </Dropdown>
  );

  const buttonElement = screen.getByRole("button", { name: "Click me" });

  await user.click(buttonElement);

  const hiddenElement = await screen.findByRole("button", {
    name: "Dropdown content 1",
  });

  expect(hiddenElement).toBeInTheDocument();

  await user.click(document.body);

  await waitFor(() => expect(hiddenElement).not.toBeInTheDocument());
});

it("closes dropdown on button click when already open", async () => {
  const { user } = render(
    <Dropdown buttonContent="Click me">
      <div>
        <div data-testid="dropdown-content">Dropdown content</div>
      </div>
    </Dropdown>
  );

  await user.click(screen.getByText("Click me"));

  const dropdownElement = await screen.findByTestId("dropdown-content");

  expect(dropdownElement).toBeInTheDocument();

  await user.click(screen.getByText("Click me"));
  await waitFor(() => expect(dropdownElement).not.toBeInTheDocument());
});

it("calls onClick handler on action list item click", async () => {
  const onClick = vi.fn();
  const { user } = render(
    <Dropdown buttonContent="Click me">
      <div>
        <button onClick={onClick}>Action 1</button>
      </div>
    </Dropdown>
  );

  await user.click(screen.getByRole("button", { name: "Click me" }));
  await user.click(screen.getByRole("button", { name: "Action 1" }));

  expect(onClick).toHaveBeenCalled();
});

it("closes dropdown on action list item click", async () => {
  const { user } = render(
    <Dropdown buttonContent="Click me">
      <div>
        <button>Action 1</button>
      </div>
    </Dropdown>
  );
  await user.click(screen.getByText("Click me"));

  await user.click(screen.getByText("Action 1"));

  expect(screen.queryByText("Action 1")).not.toBeInTheDocument();
});

it("positions dropdown works properly - don't crash, this test might be unaccurate", async () => {
  const { user } = render(
    <div>
      <Dropdown buttonContent="Top right" position="topRight">
        <div>
          <div data-testid="dropdown-content">Dropdown content</div>
        </div>
      </Dropdown>
      <Dropdown buttonContent="Top left" position="topLeft">
        <div>
          <div data-testid="dropdown-content">Dropdown content</div>
        </div>
      </Dropdown>
      <Dropdown buttonContent="Bottom right" position="bottomRight">
        <div>
          <div data-testid="dropdown-content">Dropdown content</div>
        </div>
      </Dropdown>
      <Dropdown buttonContent="Bottom left" position="bottomLeft">
        <div>
          <div data-testid="dropdown-content">Dropdown content</div>
        </div>
      </Dropdown>
    </div>
  );

  const ddTopLeftElement = screen.getByRole("button", { name: "Top left" });
  const ddTopRightElement = screen.getByRole("button", { name: "Top right" });
  const ddBottomRightElement = screen.getByRole("button", {
    name: "Bottom right",
  });
  const ddBottomLeftElement = screen.getByRole("button", {
    name: "Bottom left",
  });

  expect(ddTopLeftElement).toBeInTheDocument();
  await user.click(ddTopLeftElement);

  expect(ddTopRightElement).toBeInTheDocument();
  await user.click(ddTopRightElement);

  expect(ddBottomRightElement).toBeInTheDocument();
  await user.click(ddBottomRightElement);

  expect(ddBottomLeftElement).toBeInTheDocument();
  await user.click(ddBottomLeftElement);
});

it("disables the button if the dropdown is disabled", async () => {
  const onClick = vi.fn();

  const { user } = render(
    <Dropdown disabled buttonContent="Click me">
      <div>
        <button onClick={onClick}>Action 1</button>
      </div>
    </Dropdown>
  );

  expect(screen.getByRole("button", { name: "Click me" })).toBeDisabled();
  await user.click(screen.getByRole("button", { name: "Click me" }));

  expect(onClick).toBeCalledTimes(0);
});

it("applies the correct class when buttonSize is 'small'", () => {
  render(
    <Dropdown buttonContent="Click me" buttonSize="small">
      <div>Dropdown content</div>
    </Dropdown>
  );
  const button = screen.getByRole("button", { name: "Click me" });
  expect(button.className).toMatch(/gx-button--small/);
});

it("applies the correct class when buttonSize is 'fullWidth'", () => {
  render(
    <Dropdown buttonContent="Click me" buttonSize="fullWidth">
      <div>Dropdown content</div>
    </Dropdown>
  );
  const button = screen.getByRole("button", { name: "Click me" });
  expect(button.className).toMatch(/gx-button--fullWidth/);
});
