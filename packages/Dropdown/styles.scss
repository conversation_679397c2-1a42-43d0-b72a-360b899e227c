// ==========================================================================
// Dropdown
// ==========================================================================
@use "@gx-design/theme/styles" as *;

$gx-dropdown: "gx-dropdown";

$dropdown-position: calc(100% + (#{space(xs)}));

// TODO: rimuovere quando sarà tolto il componente bootstrap
.dropdown-menu {
  min-width: 22.4rem;
}

.open > .gx-dropdown {
  display: block;
}

.is-open .gx-dropdown {
  display: block;
}

.#{gx-dropdown} {
  display: none;
  position: absolute;
  @include z-index(dropdown);
  min-width: 22.4rem;
  max-width: 28rem;
  border: 0.1rem solid color(border-main);
  border-radius: radius(sm);
  background-color: color(background-main);
  overflow: auto;
  box-shadow: elevation(raised);

  &--maxHeight {
    max-height: 28rem;
  }

  &--fullWidth {
    min-width: 100%;
    max-width: 100%;
  }

  &--topLeft {
    left: 0;
    bottom: $dropdown-position;
  }

  &--topRight {
    right: 0;
    bottom: $dropdown-position;
  }

  &--bottomLeft {
    top: $dropdown-position;
    left: 0;
  }

  &--bottomRight {
    top: $dropdown-position;
    right: 0;
  }

  &:has(.gx-range-input) {
    min-width: 30rem;
    max-width: 32rem;
  }

  // TODO: this is only to not contaminate the actual css, remove this when we complete the gx design transition
  .gx-action-list {
    &__item {
      a {
        white-space: normal;
      }
    }
  }
}
