{"name": "@gx-design/dropdown", "version": "5.3.0", "description": "Gx Design Dropdown component", "source": "src/Dropdown.tsx", "main": "dist/index.js", "module": "dist/index.mjs", "peerDependencies": {"react": "*", "react-dom": "*"}, "devDependencies": {"tsup": "^7.2.0"}, "dependencies": {"@gx-design/action-list": "^3.2.2", "@gx-design/button": "^5.3.13", "@gx-design/theme": "^1.4.0", "@gx-design/use-forked-refs": "^1.1.1", "@gx-design/use-on-click-outside": "workspace:^", "clsx": "1.1.1"}, "scripts": {"build": "tsup", "watch": "tsup --watch"}, "repository": {"type": "git", "url": "********************:getrix/gx-design.git", "directory": "packages/Dropdown"}, "typings": "dist/index.d.ts", "volta": {"extends": "../../package.json"}}