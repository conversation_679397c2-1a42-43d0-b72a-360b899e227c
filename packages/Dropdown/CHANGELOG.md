# Change Log

All notable changes to this project will be documented in this file.
See [Conventional Commits](https://conventionalcommits.org) for commit guidelines.

# [5.3.0](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/dropdown@5.2.19...@gx-design/dropdown@5.3.0) (2025-09-08)

### Features

- **dropdown:** DropdownProvider to change portal container ([8c529ae](https://gitlab.pepita.io/getrix/gx-design/commit/8c529ae8db12d7b6d83e506429777014a96449fb))

## [5.2.19](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/dropdown@5.2.18...@gx-design/dropdown@5.2.19) (2025-07-22)

### Bug Fixes

- **dropdown:** onClick action does not forward event ([b690a27](https://gitlab.pepita.io/getrix/gx-design/commit/b690a2705fb79225de3dfefc0874820b2622e890))

## [5.2.18](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/dropdown@5.2.17...@gx-design/dropdown@5.2.18) (2025-07-17)

**Note:** Version bump only for package @gx-design/dropdown

## [5.2.17](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/dropdown@5.2.16...@gx-design/dropdown@5.2.17) (2025-07-09)

**Note:** Version bump only for package @gx-design/dropdown

## [5.2.16](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/dropdown@5.2.15...@gx-design/dropdown@5.2.16) (2025-07-07)

**Note:** Version bump only for package @gx-design/dropdown

## [5.2.15](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/dropdown@5.2.14...@gx-design/dropdown@5.2.15) (2025-07-04)

**Note:** Version bump only for package @gx-design/dropdown

## [5.2.14](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/dropdown@5.2.13...@gx-design/dropdown@5.2.14) (2025-06-09)

**Note:** Version bump only for package @gx-design/dropdown

## [5.2.13](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/dropdown@5.2.12...@gx-design/dropdown@5.2.13) (2025-05-30)

**Note:** Version bump only for package @gx-design/dropdown

## [5.2.12](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/dropdown@5.2.11...@gx-design/dropdown@5.2.12) (2025-05-23)

**Note:** Version bump only for package @gx-design/dropdown

## [5.2.11](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/dropdown@5.2.10...@gx-design/dropdown@5.2.11) (2025-05-16)

**Note:** Version bump only for package @gx-design/dropdown

## [5.2.10](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/dropdown@5.2.9...@gx-design/dropdown@5.2.10) (2025-05-14)

**Note:** Version bump only for package @gx-design/dropdown

## [5.2.9](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/dropdown@5.2.8...@gx-design/dropdown@5.2.9) (2025-04-16)

**Note:** Version bump only for package @gx-design/dropdown

## [5.2.8](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/dropdown@5.2.7...@gx-design/dropdown@5.2.8) (2025-04-02)

**Note:** Version bump only for package @gx-design/dropdown

## [5.2.7](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/dropdown@5.2.6...@gx-design/dropdown@5.2.7) (2025-03-21)

**Note:** Version bump only for package @gx-design/dropdown

## [5.2.6](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/dropdown@5.2.5...@gx-design/dropdown@5.2.6) (2025-02-14)

**Note:** Version bump only for package @gx-design/dropdown

## [5.2.5](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/dropdown@5.2.4...@gx-design/dropdown@5.2.5) (2025-02-11)

**Note:** Version bump only for package @gx-design/dropdown

## [5.2.4](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/dropdown@5.2.3...@gx-design/dropdown@5.2.4) (2025-01-15)

**Note:** Version bump only for package @gx-design/dropdown

## [5.2.3](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/dropdown@5.2.2...@gx-design/dropdown@5.2.3) (2024-11-29)

**Note:** Version bump only for package @gx-design/dropdown

## [5.2.2](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/dropdown@5.2.1...@gx-design/dropdown@5.2.2) (2024-10-31)

**Note:** Version bump only for package @gx-design/dropdown

## [5.2.1](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/dropdown@5.2.0...@gx-design/dropdown@5.2.1) (2024-10-21)

**Note:** Version bump only for package @gx-design/dropdown

# [5.2.0](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/dropdown@5.1.0...@gx-design/dropdown@5.2.0) (2024-09-23)

### Features

- **css:** custom properties ([cbc56c0](https://gitlab.pepita.io/getrix/gx-design/commit/cbc56c0e3e675d85f92ad25ac135cce270f02b2b))

## [5.1.1-alpha.0](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/dropdown@5.1.0...@gx-design/dropdown@5.1.1-alpha.0) (2024-09-16)

**Note:** Version bump only for package @gx-design/dropdown

# [5.1.0](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/dropdown@5.0.8...@gx-design/dropdown@5.1.0) (2024-08-29)

### Features

- **colors:** pro new colors ([2103ea7](https://gitlab.pepita.io/getrix/gx-design/commit/2103ea76597b5ae75018d2d62947c6b01d613e82))

# [5.1.0-alpha.0](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/dropdown@5.0.7...@gx-design/dropdown@5.1.0-alpha.0) (2024-07-26)

### Features

- **colors:** pro new colors ([d88639f](https://gitlab.pepita.io/getrix/gx-design/commit/d88639f983895257b905dfad2fb65db3e75efee0))

## [5.0.8-alpha.0](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/dropdown@5.0.7...@gx-design/dropdown@5.0.8-alpha.0) (2024-07-22)

**Note:** Version bump only for package @gx-design/dropdown

## [5.0.7](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/dropdown@5.0.6...@gx-design/dropdown@5.0.7) (2024-07-15)

**Note:** Version bump only for package @gx-design/dropdown

## [5.0.6](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/dropdown@5.0.5...@gx-design/dropdown@5.0.6) (2024-06-14)

**Note:** Version bump only for package @gx-design/dropdown

## [5.0.5](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/dropdown@5.0.4...@gx-design/dropdown@5.0.5) (2024-06-05)

**Note:** Version bump only for package @gx-design/dropdown

## [5.0.4](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/dropdown@5.0.3...@gx-design/dropdown@5.0.4) (2024-06-03)

**Note:** Version bump only for package @gx-design/dropdown

## [5.0.3](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/dropdown@5.0.2...@gx-design/dropdown@5.0.3) (2024-05-28)

**Note:** Version bump only for package @gx-design/dropdown

## [5.0.2](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/dropdown@5.0.1...@gx-design/dropdown@5.0.2) (2024-05-16)

**Note:** Version bump only for package @gx-design/dropdown

## [5.0.1](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/dropdown@5.0.0...@gx-design/dropdown@5.0.1) (2024-05-06)

**Note:** Version bump only for package @gx-design/dropdown

# [5.0.0](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/dropdown@4.0.1...@gx-design/dropdown@5.0.0) (2024-03-26)

### Features

- **button:** style and variant changes ([1fbe519](https://gitlab.pepita.io/getrix/gx-design/commit/1fbe519808701c8a752b6ec8b36fff8a0bd82a9b))

### BREAKING CHANGES

- **button:** button style to variant

## [4.0.2-alpha.1](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/dropdown@4.0.2-alpha.0...@gx-design/dropdown@4.0.2-alpha.1) (2024-03-25)

**Note:** Version bump only for package @gx-design/dropdown

## [4.0.2-alpha.0](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/dropdown@4.0.1...@gx-design/dropdown@4.0.2-alpha.0) (2024-03-25)

**Note:** Version bump only for package @gx-design/dropdown

## [4.0.1](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/dropdown@4.0.0...@gx-design/dropdown@4.0.1) (2024-03-20)

**Note:** Version bump only for package @gx-design/dropdown

# [4.0.0](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/dropdown@3.4.0...@gx-design/dropdown@4.0.0) (2024-03-18)

- feat!: new architecture ([5572bfc](https://gitlab.pepita.io/getrix/gx-design/commit/5572bfc9c05f1cb4aa5a0e134d2771ac5d7243f0))

### BREAKING CHANGES

- new architecture

# [3.4.0](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/dropdown@3.3.3...@gx-design/dropdown@3.4.0) (2024-03-18)

**Note:** Version bump only for package @gx-design/dropdown

# [3.4.0-alpha.3](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/dropdown@3.4.0-alpha.2...@gx-design/dropdown@3.4.0-alpha.3) (2024-03-11)

**Note:** Version bump only for package @gx-design/dropdown

# [3.4.0-alpha.2](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/dropdown@3.4.0-alpha.1...@gx-design/dropdown@3.4.0-alpha.2) (2024-03-11)

**Note:** Version bump only for package @gx-design/dropdown

# [3.4.0-alpha.1](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/dropdown@3.4.0-alpha.0...@gx-design/dropdown@3.4.0-alpha.1) (2024-03-08)

**Note:** Version bump only for package @gx-design/dropdown

# [3.4.0-alpha.0](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/dropdown@3.3.3...@gx-design/dropdown@3.4.0-alpha.0) (2024-03-07)

### Features

- **scss:** alert and theme styles ([7c0102d](https://gitlab.pepita.io/getrix/gx-design/commit/7c0102d309fce10724944b1e2e355d391d84ebe7))

## [3.3.4-alpha.1](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/dropdown@3.3.4-alpha.0...@gx-design/dropdown@3.3.4-alpha.1) (2024-02-29)

**Note:** Version bump only for package @gx-design/dropdown

## [3.3.4-alpha.0](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/dropdown@3.3.3...@gx-design/dropdown@3.3.4-alpha.0) (2024-02-28)

**Note:** Version bump only for package @gx-design/dropdown

## [3.3.3](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/dropdown@3.3.2...@gx-design/dropdown@3.3.3) (2024-02-27)

**Note:** Version bump only for package @gx-design/dropdown

## [3.3.2](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/dropdown@3.3.1...@gx-design/dropdown@3.3.2) (2024-01-31)

**Note:** Version bump only for package @gx-design/dropdown

## [3.3.1](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/dropdown@3.3.0...@gx-design/dropdown@3.3.1) (2024-01-12)

**Note:** Version bump only for package @gx-design/dropdown

# [3.3.0](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/dropdown@3.3.0-alpha.0...@gx-design/dropdown@3.3.0) (2023-12-18)

**Note:** Version bump only for package @gx-design/dropdown

# [3.3.0-alpha.0](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/dropdown@3.2.2...@gx-design/dropdown@3.3.0-alpha.0) (2023-12-18)

### Features

- **pagination-bar:** add tests ([bf1ade3](https://gitlab.pepita.io/getrix/gx-design/commit/bf1ade3d106fff8228b7dfcb215791e9449492d4))

## [3.2.2](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/dropdown@3.2.1...@gx-design/dropdown@3.2.2) (2023-12-15)

**Note:** Version bump only for package @gx-design/dropdown

## [3.2.1](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/dropdown@3.2.0...@gx-design/dropdown@3.2.1) (2023-12-01)

**Note:** Version bump only for package @gx-design/dropdown

# [3.2.0](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/dropdown@3.1.4...@gx-design/dropdown@3.2.0) (2023-11-27)

**Note:** Version bump only for package @gx-design/dropdown

# [3.2.0-alpha.0](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/dropdown@3.1.4...@gx-design/dropdown@3.2.0-alpha.0) (2023-11-27)

### Features

- **dropdown:** add tests ([8fe6d8c](https://gitlab.pepita.io/getrix/gx-design/commit/8fe6d8c766c112d1fa0f01b7f70531b7bb6a096c))

## [3.1.4](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/dropdown@3.1.3...@gx-design/dropdown@3.1.4) (2023-11-06)

**Note:** Version bump only for package @gx-design/dropdown

## [3.1.3](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/dropdown@3.1.2...@gx-design/dropdown@3.1.3) (2023-10-27)

**Note:** Version bump only for package @gx-design/dropdown

## [3.1.2](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/dropdown@3.1.1...@gx-design/dropdown@3.1.2) (2023-10-25)

**Note:** Version bump only for package @gx-design/dropdown

## [3.1.2-alpha.0](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/dropdown@3.1.1...@gx-design/dropdown@3.1.2-alpha.0) (2023-10-25)

**Note:** Version bump only for package @gx-design/dropdown

## [3.1.1](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/dropdown@3.1.0...@gx-design/dropdown@3.1.1) (2023-10-23)

**Note:** Version bump only for package @gx-design/dropdown

# [3.1.0](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/dropdown@3.0.15...@gx-design/dropdown@3.1.0) (2023-09-27)

### Features

- **storybook:** upgrade to 7 ([1c0e5e9](https://gitlab.pepita.io/getrix/gx-design/commit/1c0e5e941dcf7b841d1b5d2a2825f66f7921276e))

## [3.0.15](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/dropdown@3.0.14...@gx-design/dropdown@3.0.15) (2023-09-08)

**Note:** Version bump only for package @gx-design/dropdown

## [3.0.14](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/dropdown@3.0.13...@gx-design/dropdown@3.0.14) (2023-08-28)

**Note:** Version bump only for package @gx-design/dropdown

## [3.0.13](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/dropdown@3.0.12...@gx-design/dropdown@3.0.13) (2023-08-23)

**Note:** Version bump only for package @gx-design/dropdown

## [3.0.12](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/dropdown@3.0.11...@gx-design/dropdown@3.0.12) (2023-07-27)

**Note:** Version bump only for package @gx-design/dropdown

## [3.0.11](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/dropdown@3.0.10...@gx-design/dropdown@3.0.11) (2023-07-26)

**Note:** Version bump only for package @gx-design/dropdown

## [3.0.10](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/dropdown@3.0.9...@gx-design/dropdown@3.0.10) (2023-07-17)

**Note:** Version bump only for package @gx-design/dropdown

## [3.0.10-alpha.1](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/dropdown@3.0.10-alpha.0...@gx-design/dropdown@3.0.10-alpha.1) (2023-07-12)

**Note:** Version bump only for package @gx-design/dropdown

## [3.0.10-alpha.0](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/dropdown@3.0.9...@gx-design/dropdown@3.0.10-alpha.0) (2023-07-12)

**Note:** Version bump only for package @gx-design/dropdown

## [3.0.9](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/dropdown@3.0.8...@gx-design/dropdown@3.0.9) (2023-06-22)

**Note:** Version bump only for package @gx-design/dropdown

## [3.0.8](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/dropdown@3.0.7...@gx-design/dropdown@3.0.8) (2023-06-19)

### Bug Fixes

- **dropdown:** accept single actionlistItem ([b5fd207](https://gitlab.pepita.io/getrix/gx-design/commit/b5fd2072448787c13c137f19c5cf55768a18dd2a))

## [3.0.7](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/dropdown@3.0.6...@gx-design/dropdown@3.0.7) (2023-05-30)

**Note:** Version bump only for package @gx-design/dropdown

## [3.0.6](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/dropdown@3.0.5...@gx-design/dropdown@3.0.6) (2023-05-16)

**Note:** Version bump only for package @gx-design/dropdown

## [3.0.5](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/dropdown@3.0.4...@gx-design/dropdown@3.0.5) (2023-05-15)

**Note:** Version bump only for package @gx-design/dropdown

## [3.0.4](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/dropdown@3.0.3...@gx-design/dropdown@3.0.4) (2023-05-15)

**Note:** Version bump only for package @gx-design/dropdown

## [3.0.3](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/dropdown@3.0.2...@gx-design/dropdown@3.0.3) (2023-05-11)

**Note:** Version bump only for package @gx-design/dropdown

## [3.0.2](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/dropdown@3.0.1...@gx-design/dropdown@3.0.2) (2023-05-10)

**Note:** Version bump only for package @gx-design/dropdown

## [3.0.2-alpha.0](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/dropdown@3.0.1...@gx-design/dropdown@3.0.2-alpha.0) (2023-05-10)

**Note:** Version bump only for package @gx-design/dropdown

## [3.0.1](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/dropdown@3.0.0...@gx-design/dropdown@3.0.1) (2023-05-03)

**Note:** Version bump only for package @gx-design/dropdown

# [3.0.0](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/dropdown@2.2.13...@gx-design/dropdown@3.0.0) (2023-04-13)

### Features

- **icons:** nuove icone ([f444215](https://gitlab.pepita.io/getrix/gx-design/commit/f44421598355b8d9de903a81f2ca6232887f780c))

### BREAKING CHANGES

- **icons:** changed icon sprite

# [3.0.0-alpha.0](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/dropdown@2.2.13...@gx-design/dropdown@3.0.0-alpha.0) (2023-04-05)

### Features

- **icons:** nuove icone ([6528540](https://gitlab.pepita.io/getrix/gx-design/commit/65285405393efb34dfdcc4329d99dd1ef41d70c1))

### BREAKING CHANGES

- **icons:** changed icon sprite

## [2.2.13](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/dropdown@2.2.12...@gx-design/dropdown@2.2.13) (2023-03-31)

## [2.2.13-alpha.1](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/dropdown@2.2.13-alpha.0...@gx-design/dropdown@2.2.13-alpha.1) (2023-03-29)

**Note:** Version bump only for package @gx-design/dropdown

## [2.2.13-alpha.0](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/dropdown@2.2.12...@gx-design/dropdown@2.2.13-alpha.0) (2023-03-29)

**Note:** Version bump only for package @gx-design/dropdown

## [2.2.12](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/dropdown@2.2.11...@gx-design/dropdown@2.2.12) (2023-03-28)

**Note:** Version bump only for package @gx-design/dropdown

## [2.2.11](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/dropdown@2.2.11-alpha.0...@gx-design/dropdown@2.2.11) (2023-03-24)

**Note:** Version bump only for package @gx-design/dropdown

## [2.2.11-alpha.0](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/dropdown@2.2.10...@gx-design/dropdown@2.2.11-alpha.0) (2023-03-23)

**Note:** Version bump only for package @gx-design/dropdown

## [2.2.10](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/dropdown@2.2.9...@gx-design/dropdown@2.2.10) (2023-03-20)

**Note:** Version bump only for package @gx-design/dropdown

## [2.2.9](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/dropdown@2.2.8...@gx-design/dropdown@2.2.9) (2023-03-20)

### Bug Fixes

- **components:** select className and dropdown disabled ([7838fc0](https://gitlab.pepita.io/getrix/gx-design/commit/7838fc0e222a890e1aeb6db96376115b62c28cac))
- **components:** select className and dropdown disabled ([7678b3f](https://gitlab.pepita.io/getrix/gx-design/commit/7678b3f3996240f3e7eed49af8b853db85289355))

## [2.2.8](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/dropdown@2.2.7...@gx-design/dropdown@2.2.8) (2023-03-07)

**Note:** Version bump only for package @gx-design/dropdown

## [2.2.7](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/dropdown@2.2.6...@gx-design/dropdown@2.2.7) (2023-03-06)

**Note:** Version bump only for package @gx-design/dropdown

## [2.2.6](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/dropdown@2.2.5...@gx-design/dropdown@2.2.6) (2023-02-27)

**Note:** Version bump only for package @gx-design/dropdown

## [2.2.5](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/dropdown@2.2.4...@gx-design/dropdown@2.2.5) (2023-02-21)

**Note:** Version bump only for package @gx-design/dropdown

## [2.2.4](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/dropdown@2.2.3...@gx-design/dropdown@2.2.4) (2023-02-07)

**Note:** Version bump only for package @gx-design/dropdown

## [2.2.3](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/dropdown@2.2.2...@gx-design/dropdown@2.2.3) (2023-01-30)

**Note:** Version bump only for package @gx-design/dropdown

## [2.2.2](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/dropdown@2.2.1...@gx-design/dropdown@2.2.2) (2023-01-18)

**Note:** Version bump only for package @gx-design/dropdown

## [2.2.1](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/dropdown@2.2.0...@gx-design/dropdown@2.2.1) (2022-12-21)

**Note:** Version bump only for package @gx-design/dropdown

# [2.2.0](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/dropdown@2.1.9...@gx-design/dropdown@2.2.0) (2022-11-22)

**Note:** Version bump only for package @gx-design/dropdown

# [2.2.0-alpha.1](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/dropdown@2.2.0-alpha.0...@gx-design/dropdown@2.2.0-alpha.1) (2022-11-21)

**Note:** Version bump only for package @gx-design/dropdown

# [2.2.0-alpha.0](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/dropdown@2.1.9...@gx-design/dropdown@2.2.0-alpha.0) (2022-11-21)

### Features

- **toolbar:** toolbar progress ([506a44f](https://gitlab.pepita.io/getrix/gx-design/commit/506a44f1be8e93b468af98f554094217bc3ebc4b))

## [2.1.9](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/dropdown@2.1.9-alpha.0...@gx-design/dropdown@2.1.9) (2022-10-19)

**Note:** Version bump only for package @gx-design/dropdown

## [2.1.9-alpha.0](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/dropdown@2.1.8...@gx-design/dropdown@2.1.9-alpha.0) (2022-10-18)

### Bug Fixes

- **dropdown:** dropdown on scroll positioning ([63b5fa4](https://gitlab.pepita.io/getrix/gx-design/commit/63b5fa4571beaf996c048925abcac44a89a1486f))

## [2.1.8](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/dropdown@2.1.8-alpha.0...@gx-design/dropdown@2.1.8) (2022-10-12)

### Bug Fixes

- **dropdown:** dropdown ref fix ([297e1a5](https://gitlab.pepita.io/getrix/gx-design/commit/297e1a581b7e2e1b5496aeba9010b118f1cd5814))
- **notification:** fix notify ([213000c](https://gitlab.pepita.io/getrix/gx-design/commit/213000c8bc11b83eb5d9ff401e51d57d76411548))

## [2.1.8-alpha.3](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/dropdown@2.1.8-alpha.2...@gx-design/dropdown@2.1.8-alpha.3) (2022-10-12)

### Bug Fixes

- **notification:** fix notify ([fc85bb7](https://gitlab.pepita.io/getrix/gx-design/commit/fc85bb7d9a15abb9715faba3f8b57418d90fee74))

## [2.1.8-alpha.2](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/dropdown@2.1.8-alpha.0...@gx-design/dropdown@2.1.8-alpha.2) (2022-10-12)

### Bug Fixes

- **notification:** fix notify ([789498a](https://gitlab.pepita.io/getrix/gx-design/commit/789498ae821b217be4d078c71bd053221e529648))
- **notification:** fix notify ([766047c](https://gitlab.pepita.io/getrix/gx-design/commit/766047c45b4ebff6e9aa8985fc926e76a0fd59c0))
- **notification:** fix notify ([5d949cd](https://gitlab.pepita.io/getrix/gx-design/commit/5d949cd42a3e78c3c63ba78aa1d1f4595e6e3397))

## [2.1.8-alpha.0](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/dropdown@2.1.7...@gx-design/dropdown@2.1.8-alpha.0) (2022-10-10)

**Note:** Version bump only for package @gx-design/dropdown

## [2.1.7](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/dropdown@2.1.7-alpha.0...@gx-design/dropdown@2.1.7) (2022-10-06)

**Note:** Version bump only for package @gx-design/dropdown

## [2.1.7-alpha.0](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/dropdown@2.1.6...@gx-design/dropdown@2.1.7-alpha.0) (2022-10-06)

### Bug Fixes

- **dropdown:** dropdown actionlistitem ([b2672bc](https://gitlab.pepita.io/getrix/gx-design/commit/b2672bc5e9ac5778b4f050909bc628f620d4fdb0))

## [2.1.6](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/dropdown@2.1.5...@gx-design/dropdown@2.1.6) (2022-10-06)

### Bug Fixes

- **dropdown:** actionlistitemclick ([04024d2](https://gitlab.pepita.io/getrix/gx-design/commit/04024d2b3c0d7488eb57fbf86b81ec99baeb5bce))
- **dropdown:** actionlistitemclick ([3870b36](https://gitlab.pepita.io/getrix/gx-design/commit/3870b36df6bec97d614dc30ddcc0de5ce8c95ce5))
- **dropdown:** actionlistitemclick ([28e4c7c](https://gitlab.pepita.io/getrix/gx-design/commit/28e4c7cd1deba6c1deca70e847494696e6369f62))

## [2.1.5-alpha.2](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/dropdown@2.1.5-alpha.1...@gx-design/dropdown@2.1.5-alpha.2) (2022-09-26)

**Note:** Version bump only for package @gx-design/dropdown

## [2.1.5-alpha.1](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/dropdown@2.1.5-alpha.0...@gx-design/dropdown@2.1.5-alpha.1) (2022-09-21)

### Bug Fixes

- **dropdown:** actionlistitemclick ([f102ca8](https://gitlab.pepita.io/getrix/gx-design/commit/f102ca8e65e953708463331b4a5a203c49383216))
- **dropdown:** actionlistitemclick ([5941c31](https://gitlab.pepita.io/getrix/gx-design/commit/5941c318039b02951163f7b4aeb277442e16cf76))

## [2.1.5-alpha.0](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/dropdown@2.1.4...@gx-design/dropdown@2.1.5-alpha.0) (2022-09-21)

### Bug Fixes

- **dropdown:** actionlistitemclick ([4ed6f89](https://gitlab.pepita.io/getrix/gx-design/commit/4ed6f890c17b817935af4dc0aa299b40a947e28c))

## [2.1.4](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/dropdown@2.1.3...@gx-design/dropdown@2.1.4) (2022-09-19)

**Note:** Version bump only for package @gx-design/dropdown

## [2.1.3](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/dropdown@2.1.3-alpha.0...@gx-design/dropdown@2.1.3) (2022-09-19)

**Note:** Version bump only for package @gx-design/dropdown

## [2.1.3-alpha.0](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/dropdown@2.1.2...@gx-design/dropdown@2.1.3-alpha.0) (2022-09-16)

### Bug Fixes

- **dropdown:** dropdown additional props ([8482c40](https://gitlab.pepita.io/getrix/gx-design/commit/8482c4006b19681ae805d7f585e9e5aba8446f9d))

## [2.1.2](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/dropdown@2.1.1...@gx-design/dropdown@2.1.2) (2022-09-15)

**Note:** Version bump only for package @gx-design/dropdown

## [2.1.1](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/dropdown@2.1.0...@gx-design/dropdown@2.1.1) (2022-09-15)

**Note:** Version bump only for package @gx-design/dropdown

# 2.1.0 (2022-09-14)

### Bug Fixes

- **dropdown:** dependencies ([6e8164e](https://gitlab.pepita.io/getrix/gx-design/commit/6e8164e5db8b9850d65f5b2089a891acb3f4b502))
- **dropdown:** dependencies ([261a1b3](https://gitlab.pepita.io/getrix/gx-design/commit/261a1b3eea52a5c91ee547383c3fb517da70ad89))
- **lock:** lock file ([a679820](https://gitlab.pepita.io/getrix/gx-design/commit/a67982013c09d3afdef2e577a1b4e7bbc27e5c3f))

### Features

- **dropdown:** dropdown component ([f0e7f7f](https://gitlab.pepita.io/getrix/gx-design/commit/f0e7f7f71785de5e34db378f536549e6ed69bc89))
- **dropdown:** dropdown component ([8c69d1b](https://gitlab.pepita.io/getrix/gx-design/commit/8c69d1bada01fc197e65415f7ae5d8144e174057))
- **dropdown:** version ([70e37e6](https://gitlab.pepita.io/getrix/gx-design/commit/70e37e623ad39c5fc43dcc1faa677824050fb642))
