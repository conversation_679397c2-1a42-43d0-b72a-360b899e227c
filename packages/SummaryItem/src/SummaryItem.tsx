import React, { ReactElement } from "react";

export type SummaryItemProps = {
  /**
     The label of the Summary Item
    */
  label: string;
  /**
    The content of the Summary Item
    */
  children: ReactElement | string;
};

export const SummaryItem: React.FC<SummaryItemProps> = ({
  label,
  children,
}) => {
  return (
    <div className="gx-summary-item">
      <div className="gx-summary-item__label">{label}</div>
      <div className="gx-summary-item__value">{children}</div>
    </div>
  );
};
