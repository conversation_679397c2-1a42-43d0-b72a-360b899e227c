// ==========================================================================
// Summary Item - Components
// ==========================================================================
@use "@gx-design/theme/styles" as *;

.gx-summary-item {
  &__label {
    color: color(content-medium);
    @include typography(overline);
  }

  &__value {
    margin-top: space(xs);
    @include typography(body-small);
    word-break: break-word;
  }
}
