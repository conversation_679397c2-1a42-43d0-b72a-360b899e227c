import React from "react";
import { render, screen } from "../../../test/utilities";
import { Button } from "./Button";
import { it, expect, vi } from "vitest";

it("should renders with the correct variant class name", () => {
  render(<Button variant="accent">Text</Button>);

  expect(screen.getByRole("button", { name: "Text" })).toHaveClass(
    "gx-button--accent"
  );
});

it("should renders with the correct size class name", () => {
  render(<Button size="fullWidth">Text</Button>);
  expect(screen.getByRole("button", { name: "Text" })).toHaveClass(
    "gx-button--fullWidth"
  );
});

it("should renders with the correct additional class name", () => {
  render(<Button className="custom-class">Text</Button>);

  expect(screen.getByRole("button", { name: "Text" })).toHaveClass(
    "custom-class"
  );
});

it("should renders with the dropdown caret class name when dropdown is true", () => {
  render(
    <Button data-testid="button-test" dropdown>
      Text
    </Button>
  );
  expect(screen.getByTestId("button-test_dropdown")).toBeInTheDocument();
});

it("should renders with the icon only class name when iconOnly is true", () => {
  render(
    <Button disabled iconOnly>
      Text
    </Button>
  );

  expect(screen.getByRole("button", { name: "Text" })).toHaveClass(
    "gx-button--iconOnly"
  );
});

it("should calls the onClick function when clicked", async () => {
  const handleClick = vi.fn();
  const { user } = render(<Button onClick={handleClick} />);

  await user.click(screen.getByRole("button"));

  expect(handleClick).toHaveBeenCalledTimes(1);
});

it("should renders with the correct tag name when as is set", async () => {
  render(<Button as="a" href="https://hemansings.com/" />);

  const link = await screen.findByRole("link");
  expect(link).toBeInTheDocument();
});

it("render the button as disabled when disabled is true", () => {
  render(<Button disabled>Text</Button>);

  expect(screen.getByRole("button", { name: "Text" })).toBeDisabled();
});

it.todo(
  "should support only known html elements (plus: components) as type when as is set to 'button'",
  () => {
    // TODO: type could be improved to support only known html element as type when as is set to 'button'
    // see: https://twitter.com/mattpocockuk/status/1713856343478542626
    // For instance, this should not be allowed:
    // <Button as="whatever it is: object, array, jsx-element" type="submit" />
  }
);
