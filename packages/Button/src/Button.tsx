import React, { AnchorHTMLAttributes, ButtonHTMLAttributes } from "react";
import { Icon } from "@gx-design/icon";
import clsx from "clsx";

export type ButtonProps = {
  /**
   The Button variant
  */
  variant?: "default" | "accent" | "ghost" | "chip";
  /**
    Custom class names
  */
  className?: string;
  /**
    The Button content
  */
  children?: React.ReactNode;
  /**
    Determine if the button is disabled
  */
  disabled?: boolean;
  /**
    Determine if the content of the button is only an icon
  */
  iconOnly?: boolean;
  /**
    Determine if the button is a dropdown
  */
  dropdown?: boolean;
  /**
    Determine the size of the button
  */
  size?: "small" | "fullWidth";
  /**
    An alternative Tag to use to render the button.
    This could be a simple tag name (like 'div') or a component function.

    By default the button component will render a "button" element
  */
  as?: any;
} & (
  | Omit<ButtonHTMLAttributes<HTMLButtonElement>, "className" | "variant">
  | Omit<AnchorHTMLAttributes<HTMLAnchorElement>, "className" | "variant">
);

const Button = React.forwardRef<HTMLElement, ButtonProps>(
  (
    {
      variant,
      iconOnly = false,
      className,
      dropdown = false,
      disabled = false,
      children,
      size,
      as: As = "button",
      type,
      ...props
    },
    ref
  ) => {
    return (
      <As
        ref={ref}
        disabled={disabled}
        type={As === "button" && !type ? "button" : type}
        className={clsx(
          "gx-button",
          variant && `gx-button--${variant}`,
          size && `gx-button--${size}`,
          className,
          {
            "gx-button--dropdown gx-button--dropdownCaret": dropdown,
            "gx-button--iconOnly": iconOnly,
          }
        )}
        {...props}
      >
        {dropdown ? (
          <>
            {children}
            <Icon
              data-testid={
                "data-testid" in props
                  ? `${props["data-testid"]}_dropdown`
                  : undefined
              }
              className="gx-button__caret"
              name="arrow-down"
            />
          </>
        ) : (
          children
        )}
      </As>
    );
  }
);

Button.displayName = "Button";

export { Button };
