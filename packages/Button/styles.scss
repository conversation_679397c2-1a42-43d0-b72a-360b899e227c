// ==========================================================================
// Buttons - Components
// ==========================================================================
@use "sass:map";
@use "@gx-design/theme/styles" as *;
@use "@gx-design/tools/styles" as *;

// Mixin button hover
@mixin gx-states-bg-image($status: hover, $color: black, $opacity: 0.06) {
  background-image: linear-gradient(
    to top,
    rgba($color, $opacity),
    rgba($color, $opacity)
  );
  background-origin: border-box;
}

// Mixin button disabled
@mixin gx-states-disabled($color: white) {
  &:disabled,
  &.is-disabled,
  // riga aggiunta, per sicurezza, per supportare la vecchia classe 
  // rimuovere una volta che sarà stata tolta ovunque
  &.disabled {
    opacity: 0.6;
    cursor: not-allowed;

    &:hover {
      background-image: none;
    }
  }
}

// Button variables

//Colori per le varie tipologie di bottoni
$gx-btn-color: (
  default: (
    color: "content-action",
    background-color: "background-action",
    border-color: "border-action",
  ),
  accent: (
    color: "content-accent",
    background-color: "background-brand",
  ),
  ghost: (
    color: "content-action",
    background-color: transparent,
  ),
  chip: (
    color: "content-selectable",
    background-color: "background-main",
    border-color: "border-selectable",
  ),
);

$gx-button: "gx-button";

// Button component
.gx-button + .gx-button,
.gx-button + .gx-multiButton,
.gx-multiButton + .gx-multiButton,
.gx-multiButton + .gx-button {
  margin-left: space(md);
}

.#{$gx-button} {
  position: relative;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  box-sizing: border-box;
  min-width: 4rem;
  height: 4rem;
  padding: 0 space(md);
  margin: 0;
  border-radius: radius(md);
  border-width: 0.1rem;
  border-style: solid;
  @include typography(button);
  text-decoration: none;
  cursor: pointer;
  vertical-align: middle;

  .gx-icon {
    @include icon-size(md);
    fill: currentColor;
  }

  &:hover,
  &:focus {
    @include gx-states-bg-image;
    text-decoration: none;
  }

  &:focus:not(:focus-visible) {
    outline: none;
    box-shadow: none;
  }

  &:focus-visible {
    outline: none;
    box-shadow: 0 0 0 0.3rem color(background-brand-alt);
    @include z-index(base, 2);
  }

  &:active,
  &.is-active {
    @include gx-states-bg-image(active, black, 0.12);
  }

  @include gx-states-disabled;

  @each $btn-typology, $colors in $gx-btn-color {
    @if ($btn-typology == "default") {
      color: color(map.get($colors, "color"));
      background-color: color(map.get($colors, "background-color"));
      border-color: color(map.get($colors, "border-color"));

      &:hover {
        color: color(background-brand);
      }
    } @else {
      &--#{$btn-typology} {
        color: color(map.get($colors, "color"));
        background-color: color(map.get($colors, "background-color"));

        @if ($btn-typology == "chip") {
          min-width: 3.2rem;
          height: 3.2rem;
          padding-left: space(sm);
          padding-right: space(sm);
          text-transform: initial;
          font-weight: normal;

          .gx-icon {
            @include icon-size(sm);
          }

          &.#{$gx-button}--dropdown {
            padding-right: space(xs);
          }

          &.is-selected {
            background-color: color(background-selected);
            color: color(content-selected);
            border-color: color(border-selected);

            &:hover {
              background-color: color(background-selected);
              color: color(content-selected);
              border-color: color(border-selected);
            }
          }
        }

        @if (map.get($colors, "border-color")) {
          border: 0.1rem solid color(map.get($colors, "border-color"));
        } @else {
          border: none;
        }

        &:hover,
        &:focus {
          color: color(map.get($colors, "color"));
        }
      }
    }
  }

  &.is-selected {
    background-color: color(background-selected);
    color: color(content-selected);
    border-color: color(border-selected);

    &:hover {
      background-color: color(background-selected);
      color: color(content-selected);
      border-color: color(border-selected);
    }
  }

  * + * {
    margin-left: space(sm);
  }

  &--dropdown {
    &Caret {
      padding-right: space(md);
    }
  }

  &--select {
    color: color(content-medium);
    text-transform: none;

    &:hover {
      color: color(content-medium);
    }
  }

  &--fullWidth {
    width: 100%;

    & + & {
      margin-left: 0;
    }
  }

  &--small {
    min-width: 3.2rem;
    height: 3.2rem;
    padding: 0 space(sm);
  }

  &--small#{&}--iconOnly,
  &--chip#{&}--iconOnly {
    min-width: 3.2rem !important;
  }

  &--iconOnly {
    min-width: 4rem !important;
    padding: 0;
    flex-grow: 0 !important;
  }

  @include media("screen", "<#{breakpoint(sm)}") {
    &--iconOnly-xs {
      padding: 0;

      span {
        display: none;
      }
    }
  }

  @include media("screen", "<#{breakpoint(md)}") {
    &--iconOnly-sm-down {
      padding: 0;

      span {
        display: none;
      }
    }
  }

  &__caret {
    margin-left: space(xs);
    fill: currentColor;
    flex-shrink: 0;
    transition: transform 0.3s ease-in-out;
  }
}

.gx-dropupButton {
  display: inline-flex;
  position: relative;

  //TODO: una volta sostituito ovunque rimuovere 'open'
  &.open,
  &.is-open {
    @include z-index(base, 4);

    .gx-button__caret {
      transform: rotate(180deg);
    }
  }
}

// gx-multiButton
// Blocco di bottoni senza margini tra uno e l'altro
.gx-multiButton {
  display: inline-flex;
  vertical-align: middle;

  > .gx-button {
    &:focus {
      @include z-index(base, 1);
    }

    + .gx-button,
    + .gx-dropupButton {
      margin-left: -0.1rem;
    }

    &:first-child:not(:last-child) {
      border-bottom-right-radius: 0;
      border-top-right-radius: 0;
    }

    &:last-child:not(:first-child) {
      border-bottom-left-radius: 0;
      border-top-left-radius: 0;
    }

    &:not(:first-child):not(:last-child) {
      border-radius: 0;
    }
  }

  &--minWidth {
    .gx-button {
      min-width: 5.6rem;
    }
  }

  .gx-dropupButton {
    + .gx-dropupButton,
    + .gx-button {
      margin-left: -0.1rem;
    }

    &:first-child:not(:last-child) {
      .gx-button {
        border-bottom-right-radius: 0;
        border-top-right-radius: 0;
      }
    }

    &:last-child:not(:first-child) {
      @include media("screen", ">=#{breakpoint(md)}") {
        .gx-button {
          border-bottom-left-radius: 0;
          border-top-left-radius: 0;
        }
      }
    }

    &:not(:first-child):not(:last-child) {
      .gx-button {
        border-radius: 0;
      }
    }
  }

  @include media("screen", "<#{breakpoint(md)}") {
    &--separeted-xs {
      width: 100%;

      > .gx-button,
      > .gx-dropupButton {
        &:not(:last-child) {
          flex-grow: 1 !important;
        }

        + .gx-button,
        + .gx-dropupButton {
          margin-left: space(md);
        }

        &:first-child:not(:last-child) {
          border-bottom-right-radius: radius(sm);
          border-top-right-radius: radius(sm);
        }

        &:last-child:not(:first-child) {
          border-bottom-left-radius: radius(sm);
          border-top-left-radius: radius(sm);
        }

        &:not(:first-child):not(:last-child) {
          border-radius: radius(sm);
        }
      }

      > .gx-dropupButton {
        .gx-button {
          flex-grow: 1;
          border-radius: radius(sm);
        }
      }
    }
  }

  &--radio {
    .gx-button {
      input[type="radio"] {
        display: none;
      }
    }
  }
}

// gx-buttonGroup
// Un gruppo di bottoni (es. nel footer delle modali)
.gx-buttonGroup {
  display: inline-flex;
  vertical-align: middle;

  .gx-button {
    min-width: 9.6rem;

    &--iconOnly-xs {
      min-width: 4rem;
    }
  }

  > * + * {
    margin-left: space(md);
  }
}
