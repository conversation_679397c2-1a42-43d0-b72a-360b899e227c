import React, {
  TextareaHTMLAttributes,
  forwardRef,
  ForwardRefRenderFunction,
} from "react";
import { HelperText } from "@gx-design/helper-text";
import { Icon } from "@gx-design/icon";
import { Tooltip } from "@gx-design/tooltip";
import { CharacterCounter } from "@gx-design/character-counter";
import clsx from "clsx";

export type TextareaProps = {
  /**
   * The label title
   */
  label: string;
  /**
   * The tooltip helper text
   */
  tooltipHelper?: string;
  /**
   * Determine if label is visible to user
   */
  isLabelVisible?: boolean;
  /**
   * The error variable
   * This will also determine the variant of the CharacterCounter
   */
  error?: string;
} & TextareaHTMLAttributes<HTMLTextAreaElement>;

const TextareaComponent: ForwardRefRenderFunction<
  HTMLTextAreaElement,
  TextareaProps
> = (
  { label, tooltipHelper, isLabelVisible = true, error, maxLength, ...props },
  ref
) => {
  const computedVariant =
    maxLength &&
    typeof props.value === "string" &&
    props.value.length > maxLength
      ? "error"
      : undefined;

  return (
    <div className="gx-textarea-wrapper">
      <label
        htmlFor={props.id || undefined}
        className={clsx("gx-label", {
          "gx-sr-only": !isLabelVisible,
        })}
      >
        {label}
        {props.required && <span className="gx-label__required">*</span>}
        {tooltipHelper && (
          <span className="gx-tip">
            <Tooltip position="top" text={tooltipHelper}>
              <Icon className="gx-icon--info" name="info-circle" />
            </Tooltip>
          </span>
        )}
      </label>
      <textarea
        ref={ref}
        className={clsx("gx-textarea", {
          "gx-textarea--negative": error,
        })}
        maxLength={maxLength}
        {...props}
      />
      <div className="gx-textarea-wrapper__footer">
        {error && <HelperText text={error} style="error" />}
        {maxLength && (
          <CharacterCounter
            characterCount={
              typeof props.value === "string" ? props.value.length : 0
            }
            maxLength={maxLength}
            variant={computedVariant}
          />
        )}
      </div>
    </div>
  );
};

export const Textarea = forwardRef(TextareaComponent);
