// ==========================================================================
// Textarea - Form/Components
// ==========================================================================
@use "@gx-design/theme/styles" as *;
@use "@gx-design/tools/styles" as *;

.gx-textarea {
  width: 100%;
  @include typography(body);
  padding: space(sm);
  border: 0.1rem solid color(border-main);
  border-radius: radius(md);
  display: flex;
  resize: vertical;
  color: color(content-high);
  // This is needed to remove safari inner shadow on mobile
  -webkit-appearance: none;

  @include media("screen", ">=#{breakpoint(sm)}") {
    @include typography(body);
  }

  &:focus {
    outline: none;
    border: 0.1rem solid color(border-selected);
    box-shadow: 0 0 0 0.3rem color(background-brand-alt);
  }

  &--negative {
    border-color: color(border-error);
    background-color: color(background-error);
  }

  &--warning {
    border-color: color(border-warning);
  }

  &:disabled {
    background-color: color(background-alt);
    cursor: not-allowed;
  }

  &-wrapper {
    &__footer {
      display: flex;
      justify-content: space-between;
      gap: space(xl);
    }
    .gx-character-counter {
      margin-left: auto;
    }
  }
}
