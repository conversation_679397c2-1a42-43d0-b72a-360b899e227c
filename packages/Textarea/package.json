{"name": "@gx-design/textarea", "version": "5.3.0", "description": "Gx Design Textarea component", "source": "src/Textarea.tsx", "main": "dist/index.js", "module": "dist/index.mjs", "peerDependencies": {"react": "*"}, "devDependencies": {"tsup": "^7.2.0"}, "dependencies": {"@gx-design/helper-text": "^5.2.14", "@gx-design/icon": "^5.6.3", "@gx-design/theme": "^1.4.0", "@gx-design/tooltip": "^3.2.4", "clsx": "1.1.1"}, "scripts": {"build": "tsup", "watch": "tsup --watch"}, "repository": {"type": "git", "url": "********************:getrix/gx-design.git", "directory": "packages/Textarea"}, "typings": "dist/index.d.ts", "volta": {"extends": "../../package.json"}}