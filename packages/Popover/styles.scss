// ==========================================================================
// Popover - Popover/Components
// ==========================================================================
@use "@gx-design/theme/styles" as *;

$popoverPositions: (
  top,
  topLeft,
  topRight,
  left,
  leftTop,
  leftBottom,
  right,
  rightTop,
  rightBottom,
  bottom,
  bottomLeft,
  bottomRight
);

.gx-popover {
  position: absolute;
  background: color(background-main);
  border-radius: radius(sm);
  @include z-index(popover);
  min-width: 22.4rem;
  width: 100%;
  max-width: 28rem;
  padding: space(sm);
  border: 0.1rem solid color(border-main);
  box-shadow: elevation(raised);

  &--onEdge {
    padding: 0;
  }

  &--large {
    max-width: 42rem;
  }

  &__content {
    position: relative;
    background: color(background-main);
    border-radius: radius(sm);
  }

  &__header {
    padding: space(sm) space(md);
    border-bottom: 0.1rem solid color(border-main);
    @include typography(gx-title-2);
    color: color(content-high);
  }

  &__body {
    padding: space(sm) space(md);
    color: color(content-medium);

    > * + * {
      margin-top: space(sm);
    }

    ul {
      list-style: disc;
      list-style-position: outside;
      margin-left: space(lg);

      li + li {
        margin-top: space(xs);
      }
    }
  }

  // Arrow
  &:after {
    content: "";
    width: 1.2rem;
    height: 1.2rem;
    border-right: 0.1rem solid color(border-main);
    border-bottom: 0.1rem solid color(border-main);
    position: absolute;
    background: color(background-main);
  }

  @each $popover, $popoverPosition in $popoverPositions {
    &--#{$popover} {
      @if (str-index($popover, "right")) {
        &:after {
          right: 100%;
        }
      }

      @if (str-index($popover, "left")) {
        &:after {
          left: 100%;
        }
      }

      @if (str-index($popover, "bottom")) {
        &:after {
          bottom: 100%;
        }
      }

      @if (str-index($popover, "top")) {
        &:after {
          top: 100%;
        }
      }

      @if ($popover == "top") {
        &:after {
          left: 50%;
          transform: translate(-50%, -50%) rotate(45deg);
        }
      }

      @if ($popover == "topLeft") {
        &:after {
          left: space(sm);
          transform: translateY(-50%) rotate(45deg);
        }
      }

      @if ($popover == "topRight") {
        &:after {
          right: space(sm);
          transform: translateY(-50%) rotate(45deg);
        }
      }

      @if ($popover == "bottom") {
        &:after {
          left: 50%;
          transform: translate(-50%, 50%) rotate(225deg);
        }
      }

      @if ($popover == "bottomLeft") {
        &:after {
          left: space(sm);
          transform: translateY(50%) rotate(225deg);
        }
      }

      @if ($popover == "bottomRight") {
        &:after {
          right: space(sm);
          transform: translateY(50%) rotate(225deg);
        }
      }

      @if ($popover == "right") {
        &:after {
          top: 50%;
          transform: translate(50%, -50%) rotate(135deg);
        }
      }

      @if ($popover == "rightTop") {
        &:after {
          bottom: space(sm);
          transform: translateX(50%) rotate(135deg);
        }
      }

      @if ($popover == "rightBottom") {
        &:after {
          top: space(sm);
          transform: translateX(50%) rotate(135deg);
        }
      }

      @if ($popover == "left") {
        &:after {
          top: 50%;
          transform: translate(-50%, -50%) rotate(-45deg);
        }
      }

      @if ($popover == "leftTop") {
        &:after {
          bottom: space(sm);
          transform: translateX(-50%) rotate(-45deg);
        }
      }

      @if ($popover == "leftBottom") {
        &:after {
          top: space(sm);
          transform: translateX(-50%) rotate(-45deg);
        }
      }
    }
  }
}
