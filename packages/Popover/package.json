{"name": "@gx-design/popover", "version": "3.2.2", "description": "Gx Design Popover component", "source": "src/Popover.tsx", "main": "dist/index.js", "module": "dist/index.mjs", "peerDependencies": {"react": "*", "react-dom": "*"}, "devDependencies": {"tsup": "^7.2.0"}, "dependencies": {"@gx-design/theme": "^1.4.0", "clsx": "1.1.1"}, "scripts": {"build": "tsup", "watch": "tsup --watch"}, "repository": {"type": "git", "url": "********************:getrix/gx-design.git", "directory": "packages/Popover"}, "typings": "dist/index.d.ts", "volta": {"extends": "../../package.json"}}