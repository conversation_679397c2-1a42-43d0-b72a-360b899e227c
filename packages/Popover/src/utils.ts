type WindowDimensions = {
  scrollY: number;
};

type Coords = {
  top: number;
  left: number;
  transform: string;
};

// Position type as you defined in the original component
type Position =
  | "top"
  | "topLeft"
  | "topRight"
  | "bottom"
  | "left"
  | "right"
  | "bottomLeft"
  | "bottomRight"
  | "rightBottom"
  | "rightTop"
  | "leftBottom"
  | "leftTop";

export const calculatePopoverPosition = (
  position: Position,
  childrenRect: DOMRect,
  popoverRect: DOMRect,
  windowDimensions: WindowDimensions
): Coords => {
  const { scrollY } = windowDimensions;
  const gxSpacing = 12; // This value can be adjusted based on your design

  let top = 0;
  let left = 0;
  let transform = "0";

  switch (position) {
    case "top":
      left = childrenRect.x + childrenRect.width / 2;
      top = childrenRect.y - popoverRect.height - gxSpacing + scrollY;
      transform = "translateX(-50%)";

      break;
    case "topLeft":
      left = childrenRect.x;
      top = childrenRect.y - popoverRect.height - gxSpacing + scrollY;
      transform = "";
      break;
    case "topRight":
      left = childrenRect.x + childrenRect.width;
      top = childrenRect.y - popoverRect.height - gxSpacing + scrollY;
      transform = "translateX(-100%)";

      break;
    case "left":
      left = childrenRect.x - gxSpacing;
      top = childrenRect.y + childrenRect.height / 2 + scrollY;
      transform = "translate(-100%, -50%)";

      break;
    case "right":
      left = childrenRect.x + childrenRect.width + gxSpacing;
      top = childrenRect.y + childrenRect.height / 2 + scrollY;
      transform = "translateY(-50%)";

      break;
    case "bottom":
      left = childrenRect.x + childrenRect.width / 2;
      top = childrenRect.y + childrenRect.height + gxSpacing + scrollY;
      transform = "translateX(-50%)";

      break;
    case "bottomLeft":
      left = childrenRect.x;
      top = childrenRect.y + childrenRect.height + gxSpacing + scrollY;
      transform = "0";

      break;
    case "bottomRight":
      left = childrenRect.x + childrenRect.width;
      top = childrenRect.y + childrenRect.height + gxSpacing + scrollY;
      transform = "translateX(-100%)";

      break;
    case "rightBottom":
      left = childrenRect.x + childrenRect.width + gxSpacing;
      top = childrenRect.y + scrollY;
      transform = "0";

      break;
    case "rightTop":
      left = childrenRect.x + childrenRect.width + gxSpacing;
      top = childrenRect.y + childrenRect.height + scrollY;
      transform = "translateY(-100%)";

      break;
    case "leftBottom":
      left = childrenRect.x - gxSpacing;
      top = childrenRect.y + scrollY;
      transform = "translateX(-100%)";

      break;
    case "leftTop":
      left = childrenRect.x - gxSpacing;
      top = childrenRect.y + childrenRect.height + scrollY;
      transform = "translate(-100%, -100%)";

      break;
    default:
      left = childrenRect.x + childrenRect.width / 2;
      top = childrenRect.y - childrenRect.height - gxSpacing + scrollY;
      transform = "0";
  }

  return { top, left, transform };
};
