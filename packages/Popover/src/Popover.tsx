import React, {
  useState,
  useRef,
  useLayoutEffect,
  useEffect,
  useCallback,
} from "react";
import { createPortal } from "react-dom";
import clsx from "clsx";
import { calculatePopoverPosition } from "./utils";

export type PopoverProps = {
  /**
    The position where the Popover renders (relative to the children)
  */
  position?:
    | "top"
    | "topLeft"
    | "topRight"
    | "bottom"
    | "left"
    | "right"
    | "bottomLeft"
    | "bottomRight"
    | "rightBottom"
    | "rightTop"
    | "leftBottom"
    | "leftTop";
  /**
    The title of the Popover
  */
  title?: string;
  /**
    The element that triggers the Popover on mouseover
  */
  children: React.ReactElement;
  /**
    The content of the Popover
  */
  content: React.ReactElement;
  /**
    Determine if the Popover has no lateral padding
  */
  onEdge: boolean;
  /**
    Determine if the Popover is larger than default version
  */
  large: boolean;
};

export const Popover: React.FC<PopoverProps> = ({
  children,
  title,
  content,
  position = "top",
  onEdge,
  large,
}) => {
  const [isOpen, setIsOpen] = useState<boolean>(false);
  const [showPopover, setShowPopover] = useState<boolean>(false);
  const [isExiting, setIsExiting] = useState<boolean>(false);
  const [coords, setCoords] = useState<React.CSSProperties>({
    left: 0,
    top: 0,
    transform: "0",
  });
  const childrenRef = useRef<Element>(null);
  const popoverRef = useRef<HTMLDivElement>(null);

  const handleMouseEnter = useCallback(() => {
    setIsOpen(true);
    setShowPopover(true);
    setIsExiting(false);
  }, []);

  const handleMouseLeave = useCallback(() => {
    setIsOpen(false);
    setIsExiting(true);
  }, []);

  const updatePopoverPosition = useCallback(() => {
    if (!childrenRef.current || !popoverRef.current || !isOpen) {
      return;
    }
    const childrenRect = childrenRef.current.getBoundingClientRect();
    const popoverRect = popoverRef.current.getBoundingClientRect();
    const newCoords = calculatePopoverPosition(
      position,
      childrenRect,
      popoverRect,
      window
    );
    setCoords(newCoords);
  }, [isOpen, position]);

  useLayoutEffect(() => {
    if (isOpen) {
      updatePopoverPosition();
    }
  }, [isOpen, updatePopoverPosition]);

  useEffect(() => {
    if (!isOpen) return;
    window.addEventListener("resize", updatePopoverPosition);
    window.addEventListener("scroll", updatePopoverPosition);
    return () => {
      window.removeEventListener("resize", updatePopoverPosition);
      window.removeEventListener("scroll", updatePopoverPosition);
    };
  }, [isOpen, updatePopoverPosition]);

  // Unmount popover only at the end of the exit animation
  const handleAnimationEnd = () => {
    if (!isOpen && isExiting) {
      setShowPopover(false);
      setIsExiting(false);
    }
  };

  const childWithProps = React.cloneElement(
    children as React.ReactElement<any>,
    {
      onMouseEnter: handleMouseEnter,
      onMouseLeave: handleMouseLeave,
      ref: childrenRef,
    }
  );

  return (
    <>
      {childWithProps}
      {showPopover &&
        createPortal(
          <div
            ref={popoverRef}
            className={clsx(`gx-popover gx-popover--${position}`, {
              "gx-popover--large": large,
              "gx-popover--onEdge": onEdge,
              "gx-fade-in": isOpen && !isExiting,
              "gx-fade-out": !isOpen && isExiting,
            })}
            style={coords}
            onAnimationEnd={handleAnimationEnd}
            data-testid={`gx-popover--${position}`}
          >
            <div className="gx-popover__content">
              {title && <div className="gx-popover__header">{title}</div>}
              <div className="gx-popover__body">{content}</div>
            </div>
          </div>,
          document.body
        )}
    </>
  );
};
