import { expect, it, describe } from "vitest";
import { render, screen } from "../../../test/utilities";
import { Popover, PopoverProps } from "./Popover";
import { Button } from "@gx-design/button";
import React from "react";

describe("Popover", () => {
  it("should render the children", () => {
    render(
      <Popover
        title="Popover title"
        content={<div>Popover content</div>}
        position="top"
        onEdge={false}
        large={false}
      >
        <Button>Button</Button>
      </Popover>
    );
    expect(screen.getByText("Button")).toBeInTheDocument();
  });

  it("should show and hide the popover on mouseenter and mouseleave", async () => {
    const { user } = render(
      <Popover
        title="Popover title"
        content={<div>Popover content</div>}
        position="top"
        onEdge={false}
        large={false}
      >
        <Button>Button</Button>
      </Popover>
    );

    await user.hover(screen.getByText("Button"));
    expect(screen.getByText("Popover title")).toBeInTheDocument();
    expect(screen.getByText("Popover content")).toBeInTheDocument();

    await user.unhover(screen.getByText("Button"));
    setTimeout(() => {
      expect(screen.queryByText("Popover title")).not.toBeInTheDocument();
      expect(screen.queryByText("Popover content")).not.toBeInTheDocument();
    });
  });

  it("can accept different positions", async () => {
    const positions: PopoverProps["position"][] = [
      "top",
      "topLeft",
      "topRight",
      "bottom",
      "left",
      "right",
      "bottomLeft",
      "bottomRight",
      "rightBottom",
      "rightTop",
      "leftBottom",
      "leftTop",
    ];

    for (const position of positions) {
      const { user, unmount } = render(
        <Popover
          title="Popover title"
          content={<div>Popover content</div>}
          position={position}
          onEdge={false}
          large={false}
        >
          <button>Button</button>
        </Popover>
      );

      await user.hover(screen.getByText("Button"));
      const popoverPosition = screen.getByTestId(`gx-popover--${position}`);
      expect(popoverPosition).toBeInTheDocument();

      await user.unhover(screen.getByText("Button"));
      unmount();
    }
  });

  it("applies large and onEdge classes", async () => {
    const { user } = render(
      <Popover
        title="Popover title"
        content={<div>Popover content</div>}
        position="top"
        onEdge={true}
        large={true}
      >
        <button>Button</button>
      </Popover>
    );

    await user.hover(screen.getByText("Button"));
    const popoverPosition = screen.getByTestId(`gx-popover--top`);

    expect(popoverPosition).toHaveClass("gx-popover--large");
    expect(popoverPosition).toHaveClass("gx-popover--onEdge");
  });

  it("adjusts position when the window is resized", async () => {
    const { user } = render(
      <Popover
        title="Popover title"
        content={<div>Popover content</div>}
        position="top"
        onEdge={false}
        large={false}
      >
        <button>Button</button>
      </Popover>
    );

    global.window.innerWidth = 500;
    global.window.dispatchEvent(new Event("resize"));

    await user.hover(screen.getByText("Button"));
    expect(await screen.findByText("Popover title")).toBeVisible();
  });
});
