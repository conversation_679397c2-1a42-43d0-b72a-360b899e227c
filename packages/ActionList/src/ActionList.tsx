import React, { HTMLAttributeAnchorTarget, ReactElement } from "react";
import clsx from "clsx";

export type ActionListProps = {
  /**
   * The ActionListItem elements to render
   */
  children:
    | ReactElement<ActionListItemProps>
    | Array<ReactElement<ActionListItemProps>>;
  /**
   * Determine if the ActionList has a title
   */
  title?: string;
};

export type ActionListItemProps = {
  /**
   * Determine if the ActionListItem is currently selected
   */
  active?: boolean;
  /**
   * An element to show before the text
   */
  startElement?: React.ReactNode;
  /**
   * An element to show after the text
   */
  endElement?: React.ReactNode;
  /**
   * The text of the ActionListItem
   */
  text: string;
  /**
   * Determine if the ActionListItem is disabled
   */
  isDisabled?: boolean;
  /**
   * The function that fires at click of the ActionListItem
   */
  onClick?: any;
  /**
   * The href attribute of an actionListItem
   */
  href?: string;
  /**
   * The target attribute of an actionListItem
   */
  target?: HTMLAttributeAnchorTarget;
};

export const ActionList: React.FC<ActionListProps> = ({ children, title }) => {
  return (
    <div className="gx-action-list">
      {title && <div className="gx-action-list__title">{title}</div>}
      {React.Children.map(children, (child: any) => {
        if (child) {
          return React.cloneElement(child);
        }
      })}
    </div>
  );
};

export const ActionListItem: React.FC<ActionListItemProps> = ({
  startElement = null,
  endElement = null,
  active = false,
  text,
  isDisabled = false,
  onClick,
  target,
  href,
}) => {
  return (
    <div
      className={clsx("gx-action-list__item", {
        disabled: isDisabled,
        "gx-action-list__item--current": active,
        "gx-action-list__item--withEndElement": endElement,
      })}
    >
      <a
        href={href && href}
        target={target && target}
        onClick={onClick && onClick}
      >
        {startElement}
        <span>{text}</span>
        {endElement}
      </a>
    </div>
  );
};
