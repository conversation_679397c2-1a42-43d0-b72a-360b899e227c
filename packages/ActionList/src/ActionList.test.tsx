import React from "react";
import { expect, it, vi } from "vitest";
import { render, screen } from "../../../test/utilities";
import { ActionList, ActionListItem, ActionListItemProps } from "./ActionList";

const item1Text = "Item 1";
const item2Text = "Item 2";
const item3Text = "Item 3";

const renderActionList = (props?: Partial<ActionListItemProps>) => {
  const onClickMock = vi.fn();
  return {
    onClickMock,
    ...render(
      <ActionListItem text={item1Text} onClick={onClickMock} {...props} />
    ),
  };
};

it("should renders the text", () => {
  renderActionList();
  expect(screen.getByText(item1Text)).toBeInTheDocument();
});

it("should calls onClick when clicked", async () => {
  const { user, onClickMock } = renderActionList();
  await user.click(screen.getByText(item1Text));

  expect(onClickMock).toHaveBeenCalled();
});

it("should renders startElement and endElement", () => {
  const startElement = <span>Start</span>;
  const endElement = <span>End</span>;
  renderActionList({
    startElement,
    endElement,
  });
  expect(screen.getByText("Start")).toBeInTheDocument();
  expect(screen.getByText("End")).toBeInTheDocument();
});

it("should renders as disabled", () => {
  const { baseElement } = renderActionList({ isDisabled: true });
  const element = baseElement.querySelector(".disabled");

  expect(element).toBeInTheDocument();
  expect(element).toHaveTextContent(item1Text);
});

it("should renders as active", () => {
  const { baseElement } = renderActionList({ active: true });
  const element = baseElement.querySelector(".gx-action-list__item--current");

  expect(element).toBeInTheDocument();
  expect(element).toHaveTextContent(item1Text);
});

it("should renders as a link", () => {
  renderActionList({ href: "#" });

  expect(screen.getByRole("link", { name: item1Text })).toHaveAttribute(
    "href",
    "#"
  );
});

it("should renders ActionList with title", () => {
  const title = "Title";

  render(
    <ActionList title={title}>
      <ActionListItem text={item1Text} />
      <ActionListItem text={item2Text} />
      <ActionListItem text={item3Text} target="_blank" href="#" />
    </ActionList>
  );

  expect(screen.getByText(title)).toBeInTheDocument();
  expect(screen.getByText(item1Text)).toBeInTheDocument();
  expect(screen.getByText(item2Text)).toBeInTheDocument();
  expect(screen.getByText(item3Text)).toBeInTheDocument();
});

it("should render ActionList with a single item and without title", () => {
  render(
    <ActionList>
      <ActionListItem text={item1Text} />
    </ActionList>
  );
  expect(screen.getByText(item1Text)).toBeInTheDocument();
});
