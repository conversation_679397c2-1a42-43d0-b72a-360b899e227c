// ==========================================================================
// Action list - Components
// ==========================================================================
@use "@gx-design/theme/styles" as *;

%action-list-element {
  display: flex;
  align-items: center;
}

.gx-action-list {
  padding: space(sm) 0;

  & + .gx-action-list {
    border-top: 0.1rem solid color(border-main);
  }

  .dropdown-menu & {
    padding: space(xs) 0; //TO-DO: togliere quando sarà rifatto il componente dropdown-menu

    & + .gx-action-list {
      border-top: 0.1rem solid color(border-main);
    }
  }

  &__title {
    @extend %action-list-element;
    padding: space(sm) space(md);
    color: color(content-low);
    white-space: nowrap;
    @include typography(overline);
  }

  &__item {
    @extend %action-list-element;
    @include typography(body-small);
    cursor: pointer;

    .gx-icon {
      flex-shrink: 0;
    }

    &.disabled {
      cursor: not-allowed;

      a {
        pointer-events: none;
        color: color(content-low);
      }
    }

    .gx-checkbox {
      display: flex;
    }

    // TODO: remove this when we change all components
    .styled-checkbox {
      width: 100%;

      label {
        display: flex;
      }
    }

    .styled-check {
      flex-shrink: 0;
    }

    > a,
    .styled-checkbox label {
      width: 100%;
      padding: space(sm) space(xl) space(sm) space(md);
      color: color(content-selectable);
      white-space: nowrap;

      &:hover,
      &:focus {
        background-color: color(background-alt);
        text-decoration: none;
      }

      &.is-selected {
        background-color: color(background-selected);
        color: color(content-selected);
      }

      &.is-disabled {
        cursor: not-allowed;

        .gx-checkbox {
          cursor: not-allowed;
        }

        &:hover,
        &:focus {
          background-color: transparent;
        }
      }
      //TODO: rimuovere quando finiamo di implementare le action list --> lo gestisco sotto con la class --withElement
      > .gx-icon {
        flex-shrink: 0;
        min-width: 2.4rem;
        @include icon-size(md);

        &::not([class^="gx-text-"]) {
          color: color(content-action);
        }

        & + span {
          margin-left: space(sm);
        }
      }

      .styled-check + span {
        white-space: normal;
        margin-left: space(xs); //rivedere quando ci sarà il componente gx-check
      }
    }

    & > a {
      display: flex;
      align-items: center;
      white-space: normal;

      > * + * {
        margin-left: space(md);
      }
    }

    &--withEndElement {
      & > a {
        justify-content: space-between;
        padding-right: space(md);
      }
    }
  }

  &__item--current {
    background-color: color(background-alt);
    color: color(content-high);
    text-decoration: none;
  }
}
