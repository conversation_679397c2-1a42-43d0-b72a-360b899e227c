// ==========================================================================
// Notification Badge - Components
// ==========================================================================
@use "@gx-design/theme/styles" as *;

$notification-badge-size: 2rem;
$notification-badge-size-dot: 1rem;

.gx-notification-badge {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  height: $notification-badge-size;
  min-width: $notification-badge-size;
  padding: space(xs);
  border-radius: radius(rounded);
  background-color: color(background-notification);
  box-shadow: 0 0 0 0.1rem color(border-reversed);
  color: color(content-reversed);
  @include typography(body-tiny);

  &--dot {
    width: $notification-badge-size-dot;
    height: $notification-badge-size-dot;
    min-width: auto;
  }
}
