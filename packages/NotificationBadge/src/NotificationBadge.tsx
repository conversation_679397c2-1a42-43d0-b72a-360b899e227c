import React from "react";
import clsx from "clsx";

export interface NotificationBadgeProps {
  /**
   * The text inside the NotificationBadge, which should contain only numbers.
   */
  number?: string;
}

export const NotificationBadge: React.FC<NotificationBadgeProps> = ({
  number,
}) => {
  return (
    <div
      className={clsx(
        "gx-notification-badge",
        number === undefined && "gx-notification-badge--dot"
      )}
    >
      {number && <span>{number}</span>}
    </div>
  );
};
