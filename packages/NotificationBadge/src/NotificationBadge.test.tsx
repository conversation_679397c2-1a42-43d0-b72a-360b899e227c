import { expect, it } from "vitest";
import { render, screen } from "../../../test/utilities";
import { NotificationBadge } from "./NotificationBadge";
import React from "react";

// Check if the component renders with a number
it("renders with a number", () => {
  render(<NotificationBadge number="5" />);
  expect(screen.getByText("5")).toBeInTheDocument();
});

// Test case for dot style when number is undefined
it("renders as a dot when number is undefined", () => {
  render(<NotificationBadge />);
  const badge = document.querySelector(".gx-notification-badge");
  expect(badge).toBeInTheDocument();
  expect(badge).toHaveClass("gx-notification-badge--dot");
});
