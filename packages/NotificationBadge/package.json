{"name": "@gx-design/notification-badge", "version": "4.2.2", "description": "Gx Design NotificationBadge component", "source": "src/NotificationBadge.tsx", "main": "dist/index.js", "module": "dist/index.mjs", "type": "module", "peerDependencies": {"react": "*"}, "devDependencies": {"tsup": "^7.2.0"}, "dependencies": {"@gx-design/theme": "^1.4.0", "clsx": "1.1.1"}, "scripts": {"build": "tsup", "watch": "tsup --watch"}, "repository": {"type": "git", "url": "********************:getrix/gx-design.git", "directory": "packages/NotificationBadge"}, "typings": "dist/index.d.ts", "volta": {"extends": "../../package.json"}}