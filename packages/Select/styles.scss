// ==========================================================================
// Select - Form/Components
// ==========================================================================
@use "@gx-design/theme/styles" as *;
@use "@gx-design/tools/styles" as *;

$gx-select: "gx-select";

// -------------------------------
// Wrapper for input with select
// -------------------------------
.gx-input-select-wrapper {
  display: flex;
  align-items: center;
  position: relative;

  .gx-input {
    border-left: 0.1rem solid transparent;
    border-top-left-radius: 0;
    border-bottom-left-radius: 0;

    &:focus {
      @include z-index(base, 2);
    }
  }

  .gx-select {
    flex-shrink: 0;
    min-width: 10.4rem;
    max-width: 40%;
    border-radius: 0;
    border-top-left-radius: radius(md);
    border-bottom-left-radius: radius(md);

    &__control {
      border-radius: 0;
      border-top-left-radius: radius(md);
      border-bottom-left-radius: radius(md);
      @include typography(body);

      & > span {
        display: flex;
        align-items: center;
        margin-right: space(sm);
        white-space: nowrap;
        text-overflow: ellipsis;
        overflow: hidden;
        @include typography(body);

        @include media("screen", ">=#{breakpoint(sm)}") {
          @include typography(body);
        }

        * + * {
          margin-left: space(sm);
        }
      }
    }
  }

  &--negative {
    .#{$gx-select} {
      &__control {
        border-color: color(border-error);
      }
    }

    .gx-input {
      border-color: color(border-error);
      border-left-color: transparent;
    }

    .#{$gx-select}--native {
      border-color: color(border-error);
    }
  }

  &--warning {
    .#{$gx-select} {
      &__control {
        border-color: color(border-warning);
      }
    }

    .gx-input {
      border-color: color(border-warning);
      border-left-color: transparent;
    }

    .#{$gx-select}--native {
      border-color: color(border-warning);
    }
  }

  &.is-disabled,
  &.disabled {
    cursor: not-allowed;
  }
}

.#{$gx-select} {
  position: relative;

  &__nativeControl {
    position: absolute;
    width: 0.1rem;
    height: 0.1rem;
    padding: 0;
    margin: -0.1rem;
    overflow: hidden;
    visibility: hidden;
    clip: rect(0, 0, 0, 0);
    color: color(content-high);
    border: 0;
    border-radius: radius(md);

    &:disabled + .#{$gx-select}__control {
      background-color: color(background-alt);
      color: color(content-low);
      cursor: not-allowed;

      .gx-icon {
        color: color(content-low);
      }
    }
  }

  &__control {
    display: flex;
    justify-content: space-between;
    align-items: center;
    background-color: color(background-main);
    height: 4rem;
    border: 0.1rem solid color(border-main);
    border-radius: radius(md);
    padding: space(sm) space(xs) space(sm) space(sm);
    color: color(content-high);
    cursor: pointer;

    .gx-icon {
      @include icon-size(md);
      color: color(content-high);
      transition: 0.25s;
      flex-shrink: 0;
    }
  }

  &__dropdown {
    display: none;
    position: absolute;
    @include z-index(dropdown);
    width: 100%;
    top: calc(4rem + #{space(xs)});
    left: 0;
    border: 0.1rem solid color(border-main);
    border-radius: radius(md);
    padding: space(sm) 0;
    background-color: color(background-main);
    margin-bottom: 0;
    list-style: none;

    &__item {
      padding: space(sm) space(md);
      cursor: pointer;

      &:hover {
        background-color: color(background-alt);
      }
    }
  }

  &--isOpen {
    .gx-select__control {
      .gx-icon {
        transform: rotate(180deg);
        transition: 0.25s;
      }
    }
  }

  &--native {
    display: flex;
    justify-content: space-between;
    align-items: center;
    border: 0.1rem solid color(border-main);
    height: 4rem;
    background-color: color(background-main);
    border-radius: radius(md);
    color: color(content-high);
    @include typography(body);

    @include media("screen", ">=#{breakpoint(sm)}") {
      @include typography(body);
    }

    .gx-icon {
      position: absolute;
      flex-shrink: 0;
      color: color(content-high);
      right: 0.4rem;
      @include icon-size(md);
    }

    .#{$gx-select}__nativeControl {
      cursor: pointer;
      position: static;
      width: 100%;
      height: 100%;
      padding: 0 space(xl) 0 space(sm);
      margin: 0;
      overflow: visible;
      visibility: visible;
      clip: 0;
      -webkit-appearance: none;
      border: 0;
      border-radius: radius(md);
      text-overflow: ellipsis;
      background-color: transparent;
      @include z-index(base, 1);

      // IE11 fix to remove system arrow
      &::-ms-expand {
        display: none;
      }

      &:disabled + .#{$gx-select}__control {
        background-color: color(background-alt);
        color: color(content-low);
      }

      &:focus {
        outline: none;
      }
    }

    &:focus-within {
      border: 0.1rem solid color(border-action);
      box-shadow: 0 0 0 0.3rem color(border-selected);
    }

    &.is-disabled,
    // This is needed cause the core-utils select module use .disabled class
    &.disabled {
      background-color: color(background-alt);
      color: color(content-low);
      cursor: not-allowed;

      .#{$gx-select}__nativeControl {
        background-color: color(background-alt);
        cursor: not-allowed;
      }

      .gx-icon {
        color: color(content-low);
        @include z-index(base, 1);
      }
    }

    &.#{$gx-select}--negative {
      border-color: color(border-error);
      background-color: color(background-error);

      .gx-icon {
        color: color(content-error);
      }
    }
  }

  &:focus-within {
    .#{$gx-select}__control {
      border: 0.1rem solid color(border-selected);
      box-shadow: 0 0 0 0.3rem color(border-selected);
      @include z-index(base, 4);
      position: relative;
    }
  }

  &--chip {
    height: 3.2rem;
    @include typography(body-small);
    background-color: color(bakground-selectable);
    color: color(content-selectable);
    border-color: color(border-selectable);

    &:focus-within {
      box-shadow: none;
    }

    &:has(option:checked:not(.placeholder)) {
      background: color(background-selected);
      color: color(content-selected);
      border-color: color(border-selected);
    }
  }

  &--negative {
    .#{$gx-select}__control {
      border-color: color(border-error);
    }
  }

  &--warning {
    .#{$gx-select}__control {
      border-color: color(border-warning);
    }
  }
}
