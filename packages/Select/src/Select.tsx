import { HelperText } from "@gx-design/helper-text";
import { Icon } from "@gx-design/icon";
import { Tooltip } from "@gx-design/tooltip";
import clsx from "clsx";
import React, {
  forwardRef,
  ForwardRefRenderFunction,
  SelectHTMLAttributes,
} from "react";

export type SelectProps = {
  /**
   A custom class name for the wrapper
  */
  className?: string;
  /**
   The label title
  */
  label: string;
  /**
   An array of options
  */
  options: {
    value: string | number;
    label: string;
    disabled?: boolean;
    selected?: boolean;
  }[];
  /**
   The loading status
  */
  isLoading?: boolean;
  /**
   Variant for Select element
  */
  variant?: "chip";
  /**
   The tooltip helper text
  */
  tooltipHelper?: string;
  /**
   Determine if label is visible to user
  */
  isLabelVisible?: boolean;
  /**
   The error message
  */
  error?: string;
  /**
   The select placeholder
  */
  placeholder?: string;
} & SelectHTMLAttributes<HTMLSelectElement>;

const SelectComponent: ForwardRefRenderFunction<
  HTMLSelectElement,
  SelectProps
> = (
  {
    label,
    isLabelVisible = true,
    error = "",
    options,
    placeholder = null,
    isLoading,
    variant,
    className,
    tooltipHelper,
    ...props
  },
  ref
) => (
  <div className={clsx("gx-input-wrapper", className)}>
    <label
      htmlFor={props.id && props.id}
      className={clsx("gx-label", {
        "gx-sr-only": !isLabelVisible,
      })}
    >
      {label}
      {props.required && <span className="gx-label__required">*</span>}
      {tooltipHelper && (
        <span className="gx-tip">
          <Tooltip position="top" text={tooltipHelper}>
            <Icon className="gx-icon--info" name="info-circle" />
          </Tooltip>
        </span>
      )}
    </label>
    <div
      className={clsx("gx-select", "gx-select--native", {
        "is-disabled": props.disabled || isLoading,
        "gx-select--negative": error,
        [`gx-select--${variant}`]: variant,
      })}
    >
      <select
        ref={ref}
        className="gx-select__nativeControl"
        disabled={props.disabled || isLoading}
        {...props}
      >
        {placeholder && (
          <option className="placeholder" value="">
            {placeholder}
          </option>
        )}
        {options.map((option, index) => {
          return (
            <option disabled={option.disabled} value={option.value} key={index}>
              {option.label}
            </option>
          );
        })}
      </select>
      {isLoading ? (
        <Icon className="gx-spin" name="loader" />
      ) : (
        <Icon name="arrow-down" />
      )}
    </div>
    {error && <HelperText text={error} style="error" />}
  </div>
);

export const Select = forwardRef(SelectComponent);
