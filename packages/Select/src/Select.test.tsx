import { expect, it, vi } from "vitest";
import { render, screen } from "../../../test/utilities";
import { Select } from "./Select";

const options = [
  { value: "1", label: "Option 1" },
  { value: "2", label: "Option 2" },
  { value: "3", label: "Option 3" },
];

it("renders the label", () => {
  render(<Select label="Test label" options={options} />);
  expect(screen.getByText("Test label")).toBeInTheDocument();
});

it("renders the options", () => {
  render(<Select label="Test label" options={options} />);
  expect(screen.getByText("Option 1")).toBeInTheDocument();
  expect(screen.getByText("Option 2")).toBeInTheDocument();
  expect(screen.getByText("Option 3")).toBeInTheDocument();
});

it("calls onChange when an option is selected", async () => {
  const handleChange = vi.fn();
  const { user } = render(
    <Select
      label="Test label"
      id="test-1"
      options={options}
      onChange={handleChange}
    />
  );

  const selectElement = screen.getByLabelText("Test label");
  await user.selectOptions(selectElement, "3");

  expect(handleChange).toHaveBeenCalled();

  expect(selectElement).toHaveValue("3");
});

it("displays the loading spinner when isLoading is true", () => {
  const { baseElement } = render(
    <Select label="Test label" options={options} isLoading />
  );

  expect(baseElement.querySelector(".gx-spin")).toBeInTheDocument();
});

it("displays the error message when error is present", () => {
  render(<Select label="Test label" options={options} error="Test error" />);
  expect(screen.getByText("Test error")).toBeInTheDocument();
});

it("displays the placeholder when no option is selected", () => {
  render(
    <Select
      label="Test label"
      options={options}
      placeholder="Select an option"
    />
  );
  expect(screen.getByText("Select an option")).toBeInTheDocument();
});

it("displays the asterisc when the field is required", () => {
  render(
    <Select
      label="Test label"
      options={options}
      placeholder="Select an option"
      required
    />
  );
  expect(screen.getByText("Test label")).toHaveTextContent("Test label*");
});

it("adds additional classname", () => {
  const { baseElement } = render(
    <Select
      label="Test label"
      className="custom-classname"
      options={options}
      placeholder="Select an option"
      required
    />
  );

  expect(baseElement.querySelector(".custom-classname")).toBeInTheDocument();
});

it('adds the "disabled" attribute when disabled is true', () => {
  render(
    <Select
      id="test-1"
      label="Test label"
      options={options}
      placeholder="Select an option"
      disabled
    />
  );

  expect(screen.getByLabelText("Test label")).toHaveAttribute("disabled");
});

it("disable the select when is loading is true", () => {
  render(
    <Select
      id="test-1"
      label="Test label"
      options={options}
      placeholder="Select an option"
      isLoading
    />
  );

  expect(screen.getByLabelText("Test label")).toHaveAttribute("disabled");
});

it("renders tooltip helper text", async () => {
  const { baseElement, user } = render(
    <Select
      id="test-1"
      label="Test label"
      options={options}
      placeholder="Select an option"
      tooltipHelper="This is the tooltip"
    />
  );

  const labelElement = screen.getByLabelText("Test label");
  expect(labelElement).toBeInTheDocument();

  const iconElement = baseElement.querySelector(".gx-icon--info");

  expect(iconElement).toBeInTheDocument();

  if (iconElement) {
    await user.hover(iconElement);
    const tooltipElement = await screen.findByText("This is the tooltip");

    expect(tooltipElement).toBeInTheDocument();
  } else {
    expect.fail("Icon element not found");
  }
});
