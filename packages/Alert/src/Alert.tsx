import React, { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from "react";
import { Icon } from "@gx-design/icon";
import clsx from "clsx";

// Props
export interface AlertProps {
  /**
   The Alert's content
  */
  children: React.ReactNode;
  /**
   Determine if the Alert is dismissable
  */
  dismissable?: boolean;
  /**
   Hand<PERSON> that fires on dismiss click
  */
  onDismiss?: MouseEventHandler<HTMLButtonElement>;
  /**
   The style of the alert
  */
  style: "warning" | "error" | "success" | "info";
  /**
   Determine if the Alert has top and bottom margin
  */
  withMargin?: boolean;
  /**
   Determine if the Alert has bottom margin
  */
  withMarginBottom?: boolean;
}

export const Alert: React.FC<AlertProps> = ({
  children,
  dismissable,
  style = "info",
  withMargin,
  withMarginBottom,
  onDismiss,
}) => {
  const getIcons = (style: AlertProps["style"]) => {
    switch (style) {
      case "warning":
        return "warning";
      case "error":
        return "cross-circle";
      case "success":
        return "check-circle";
      default:
        return "info-circle";
    }
  };

  return (
    <div
      className={clsx("gx-alert", style && `gx-alert--${style}`, {
        "gx-alert--margin": withMargin,
        "gx-alert--marginBottom": withMarginBottom,
        "gx-alert--dismissable": dismissable,
      })}
    >
      {dismissable && (
        <button
          onClick={onDismiss && ((e) => onDismiss(e))}
          className="gx-alert__close"
        >
          <Icon name="cross" />
        </button>
      )}
      <Icon name={getIcons(style)} />
      <span>{children}</span>
    </div>
  );
};
