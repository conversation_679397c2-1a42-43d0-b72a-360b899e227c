import React from "react";
import { render, screen } from "../../../test/utilities";
import { Alert } from "./Alert";
import { it, expect, vi } from "vitest";

it("should render the alert with children", () => {
  render(<Alert style="info">Test Alert</Alert>);
  expect(screen.getByText("Test Alert")).toBeInTheDocument();
});

it("should apply the correct style class", () => {
  const styles = ["info", "warning", "success", "error"] as const;
  styles.forEach((style) => {
    render(
      <Alert style={style}>{`${
        style.charAt(0).toUpperCase() + style.slice(1)
      } Alert`}</Alert>
    );
    expect(
      screen.getByText(
        `${style.charAt(0).toUpperCase() + style.slice(1)} Alert`
      ).parentElement
    ).toHaveClass(`gx-alert--${style}`);
  });
});

it("should render the dismiss button if dismissable", () => {
  render(
    <Alert style="warning" dismissable>
      Warning Alert
    </Alert>
  );
  expect(screen.getByRole("button")).toBeInTheDocument();
});

it("should call onDismiss when dismiss button is clicked", async () => {
  const onDismissMock = vi.fn();
  const { user } = render(
    <Alert style="info" dismissable onDismiss={onDismissMock}>
      Dismissable Alert
    </Alert>
  );
  const button = screen.getByRole("button");
  await user.click(button);
  expect(onDismissMock).toHaveBeenCalledTimes(1);
});

it("should apply margin classes when withMargin or withMarginBottom is true", () => {
  const { rerender } = render(
    <Alert style="info" withMargin>
      Alert with Margin
    </Alert>
  );
  expect(screen.getByText("Alert with Margin").parentElement).toHaveClass(
    "gx-alert--margin"
  );

  rerender(
    <Alert style="info" withMarginBottom>
      Alert with Bottom Margin
    </Alert>
  );
  expect(
    screen.getByText("Alert with Bottom Margin").parentElement
  ).toHaveClass("gx-alert--marginBottom");
});
