// ==========================================================================
// Alerts - Components/Alerts
// ==========================================================================
@use "@gx-design/theme/styles" as *;

// Colors map
$gx-alert-color: (
  info: "info",
  success: "success",
  error: "error",
  warning: "warning",
);

.gx-alert {
  padding: space(md);
  border-width: 0.1rem;
  border-style: solid;
  border-radius: radius(md);
  position: relative;
  display: flex;
  align-items: center;
  color: color(content-high);
  background-color: color(background-main);
  border-color: color(border-main);
  @include typography(body-small);

  @each $alert-typology, $role in $gx-alert-color {
    &--#{$alert-typology} {
      background-color: color(background-#{$role});
      border-color: color(border-#{$role});

      > .gx-icon {
        color: color(content-#{$role});
      }
    }
  }

  > .gx-icon {
    flex-shrink: 0;
    align-self: flex-start;
    @include icon-size(md);

    + span {
      margin-left: space(md);
      @include typography(body-small);
    }
  }

  &__close {
    position: absolute;
    right: space(sm);
    top: space(sm);
    display: flex;
    align-items: center;
    justify-content: center;
    width: 1.6rem;
    height: 1.6rem;
    padding: 0;
    cursor: pointer;
    border-radius: radius(sm);
    border: 0;
    background-color: transparent;
    color: color(content-action);

    &:hover,
    &:focus {
      //TO-DO: sostituire con un mixin
      background-image: linear-gradient(
        to top,
        rgba(#000000, 0.1),
        rgba(#000000, 0.1)
      );
      background-origin: border-box;
    }

    .gx-icon {
      @include icon-size(xs);
    }
  }

  &--dismissable {
    padding-right: space(xl);
  }

  &--margin {
    margin: space(md) 0;
  }

  &--marginBottom {
    margin-bottom: space(md);
  }

  &--marginBottomLarge {
    margin-bottom: space(xl);
  }

  &--marginTop {
    margin-top: space(md);
  }
}
