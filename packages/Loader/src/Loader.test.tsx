import { expect, it } from "vitest";
import { render, screen } from "../../../test/utilities";
import { Loader, LoaderProps } from "./Loader";
import React from "react";

const variants: LoaderProps["variant"][] = ["fixed", "inline"];

variants.forEach((style) => {
  it(`renders the Loader with variant ${style}`, () => {
    render(<Loader variant={style} />);
    expect(screen.getByRole("loader")).toBeInTheDocument();
    expect(
      (screen.getByRole("loader")?.firstChild as Element)?.classList.contains(
        "gx-loader"
      )
    ).toBe(true);
  });
});
