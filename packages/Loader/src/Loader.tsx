import React from "react";
import clsx from "clsx";

export interface LoaderProps {
  variant?: "fixed" | "inline";
}

export const Loader: React.FC<LoaderProps> = ({ variant }) => {
  return (
    <div
      role="loader"
      className={clsx("gx-loader-overlay", {
        "gx-loader-overlay--fixed": variant === "fixed",
        "gx-loader-overlay--inline": variant === "inline",
      })}
    >
      <div className={clsx("gx-loader")} />
    </div>
  );
};
