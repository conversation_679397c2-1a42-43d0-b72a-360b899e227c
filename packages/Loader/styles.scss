// ==========================================================================
// Loader - Components/Loader
// ==========================================================================
@use "@gx-design/theme/styles" as *;
@use "@gx-design/tools/styles" as *;

$gx-loader-size: 5.2rem;

// Overlay styling
.gx-loader-overlay {
  position: absolute;
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
  background-color: rgba(color(background-main, true), 0.8);
  inset: 0;
  @include z-index(loader-overlay);

  &--inline {
    position: relative;
    margin: space(md) 0;
    min-height: $gx-loader-size;
    @include z-index(0);
    padding: 2rem 0;
  }

  &--fixed {
    inset: 0;
    width: 100%;
    height: 100%;
    margin: 0;
    padding: 0;
    position: fixed;
    min-height: $gx-loader-size;
    @include z-index(loader-overlay);

    .gx-loader {
      position: absolute;
      top: 50%;
      left: 50%;
      margin-left: $gx-loader-size / -2;
      margin-top: $gx-loader-size / -2;
    }
  }
}

// Spinner styling
.gx-loader {
  animation: gx-spin 1.25s infinite linear;
  width: $gx-loader-size;
  height: $gx-loader-size;
  // border: 0.6rem rgba(color(background-brand), 0.25) solid;
  border: 0.6rem rgba(color(background-brand, true), 0.25) solid;
  border-top-color: color(background-brand);
  border-radius: radius(rounded);
  color: color(background-brand);
  margin: 0;
}
