{"name": "@gx-design/loader", "version": "1.4.2", "description": "Gx Design Loader component", "source": "src/Loader.tsx", "main": "dist/index.js", "module": "dist/index.mjs", "peerDependencies": {"react": "*"}, "devDependencies": {"tsup": "^7.2.0"}, "dependencies": {"@gx-design/theme": "^1.4.0", "clsx": "1.1.1"}, "scripts": {"build": "tsup", "watch": "tsup --watch"}, "repository": {"type": "git", "url": "********************:getrix/gx-design.git", "directory": "packages/Loader"}, "typings": "dist/index.d.ts", "volta": {"extends": "../../package.json"}}