# Change Log

All notable changes to this project will be documented in this file.
See [Conventional Commits](https://conventionalcommits.org) for commit guidelines.

## [1.4.2](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/loader@1.4.1...@gx-design/loader@1.4.2) (2025-07-04)

**Note:** Version bump only for package @gx-design/loader

## [1.4.1](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/loader@1.4.0...@gx-design/loader@1.4.1) (2024-10-31)

**Note:** Version bump only for package @gx-design/loader

# [1.4.0](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/loader@1.3.0...@gx-design/loader@1.4.0) (2024-09-23)

### Features

- **css:** custom properties ([cbc56c0](https://gitlab.pepita.io/getrix/gx-design/commit/cbc56c0e3e675d85f92ad25ac135cce270f02b2b))

## [1.3.1-alpha.1](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/loader@1.3.1-alpha.0...@gx-design/loader@1.3.1-alpha.1) (2024-09-16)

**Note:** Version bump only for package @gx-design/loader

## [1.3.1-alpha.0](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/loader@1.3.0...@gx-design/loader@1.3.1-alpha.0) (2024-09-16)

**Note:** Version bump only for package @gx-design/loader

# [1.3.0](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/loader@1.2.1...@gx-design/loader@1.3.0) (2024-08-29)

### Features

- **colors:** pro new colors ([2103ea7](https://gitlab.pepita.io/getrix/gx-design/commit/2103ea76597b5ae75018d2d62947c6b01d613e82))

# [1.3.0-alpha.0](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/loader@1.2.1...@gx-design/loader@1.3.0-alpha.0) (2024-07-26)

### Features

- **colors:** pro new colors ([d88639f](https://gitlab.pepita.io/getrix/gx-design/commit/d88639f983895257b905dfad2fb65db3e75efee0))

## [1.2.2-alpha.0](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/loader@1.2.1...@gx-design/loader@1.2.2-alpha.0) (2024-07-22)

**Note:** Version bump only for package @gx-design/loader

## [1.2.1](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/loader@1.2.0...@gx-design/loader@1.2.1) (2024-05-09)

### Bug Fixes

- **loader:** default style ([61114ff](https://gitlab.pepita.io/getrix/gx-design/commit/61114ff9e494a60c1e4d69e63ab849ae8a4aca7e))

# [1.2.0](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/loader@1.1.0...@gx-design/loader@1.2.0) (2024-04-08)

### Features

- **loader:** inline variant ([714d545](https://gitlab.pepita.io/getrix/gx-design/commit/714d545bd7b6ad480b69ba736ab5d588f3a11072))

# 1.1.0 (2024-04-05)

**Note:** Version bump only for package @gx-design/loader

# 1.1.0-alpha.0 (2024-04-02)

### Features

- **loader:** remove product css ([26ae0a9](https://gitlab.pepita.io/getrix/gx-design/commit/26ae0a948058314cda26f0be7b088d36d1c91188))
- **loader:** remove product css ([06a0e26](https://gitlab.pepita.io/getrix/gx-design/commit/06a0e26345d6d4be86097f56eb281f45a2ab2c60))
