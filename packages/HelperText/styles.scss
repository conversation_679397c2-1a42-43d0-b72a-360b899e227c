// ==========================================================================
// Helper text - Form/Components
// ==========================================================================
@use "@gx-design/theme/styles" as *;

.gx-helper {
  display: flex;
  align-items: flex-start;
  margin-top: space(sm);

  &--isAbsolute {
    position: absolute;
    left: 0;
    top: 100%;
  }

  .gx-icon {
    @include icon-size(xs);
    flex-shrink: 0;
  }

  &__text {
    @include typography(body-tiny);
    margin-left: space(xs);
    color: color(content-high);
  }
}
