import React from "react";
import { Icon } from "@gx-design/icon";

// Props
export type HelperTextProps = {
  /**
   The text showed by the HelperText
  */
  text: string;
  /**
   The style of the HelperText
  */
  style: "info" | "error" | "success" | "warning";
};

export const HelperText: React.FC<HelperTextProps> = ({ text, style }) => {
  const getIcons = (style: HelperTextProps["style"]) => {
    switch (style) {
      case "warning":
        return "exclamation-mark-circle";
      case "error":
        return "cross-circle";
      case "success":
        return "check-circle";
      default:
        return "info-circle";
    }
  };

  return (
    <div className="gx-helper">
      <Icon className={`gx-icon--${style}`} name={getIcons(style)} />
      <span className="gx-helper__text">{text}</span>
    </div>
  );
};
