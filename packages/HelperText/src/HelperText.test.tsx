import React from "react";
import { render, screen } from "../../../test/utilities";
import { HelperText } from "./HelperText";
import { it, expect } from "vitest";

const styles = ["info", "warning", "success", "error"];

styles.forEach((style) => {
  it("renders the helperText with both icon and text", () => {
    render(<HelperText text={"text for " + style} style={style} />);
    const helperText = screen.getByText("text for " + style);
    const icon = document.querySelector(".gx-icon");
    expect(helperText).toBeInTheDocument();
    expect(icon).toHaveClass(`gx-icon--${style}`);
  });
});
