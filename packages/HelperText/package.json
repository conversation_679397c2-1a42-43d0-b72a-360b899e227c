{"name": "@gx-design/helper-text", "version": "5.2.14", "description": "Gx Design Helper Text component", "source": "src/HelperText.tsx", "main": "dist/index.js", "module": "dist/index.mjs", "peerDependencies": {"react": "*"}, "devDependencies": {"tsup": "^7.2.0"}, "dependencies": {"@gx-design/icon": "^5.6.3", "@gx-design/theme": "^1.4.0", "clsx": "1.1.1"}, "scripts": {"build": "tsup", "watch": "tsup --watch"}, "repository": {"type": "git", "url": "********************:getrix/gx-design.git", "directory": "packages/HelperText"}, "typings": "dist/index.d.ts", "volta": {"extends": "../../package.json"}}