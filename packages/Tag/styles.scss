// ==========================================================================
// Tags - Components
// ==========================================================================
@use "@gx-design/theme/styles" as *;

.gx-tag {
  position: relative;
  display: inline-flex;
  justify-content: center;
  align-items: center;
  flex-shrink: 0;
  align-items: center;
  vertical-align: middle;
  min-width: 2.4rem;
  height: 3.2rem;
  padding: 0 space(sm);
  white-space: nowrap;
  border-radius: radius(sm);
  border-width: 0.1rem;
  background-color: color(background-selected);
  border: 1px solid color(border-selected);
  color: color(content-selected);
  @include typography(body-tiny);
  font-weight: 500;

  & + & {
    margin-left: space(xs);
  }

  .gx-icon {
    @include icon-size(xs);
    fill: currentColor;
  }

  * + * {
    margin-left: space(xs);
    flex-shrink: 0;
  }

  .gx-tag__icon + * {
    margin-left: space(xs);
  }

  &__close {
    display: flex;
    align-items: center;
    justify-content: center;
    margin-left: space(sm);
    cursor: pointer;
    flex-shrink: 0;
    opacity: 0.6;

    &:hover {
      opacity: 1;
    }
  }

  &--iconOnly {
    min-width: 2.4rem;
    padding: 0;
  }
}

// tag performance
.performance-tag-positive {
  background-color: color(content-success);
  border-color: color(content-success);
  color: color(content-accent);
}

.performance-tag-warning {
  background-color: #ffb801;
  border-color: #ffb801;
  color: color(content-accent);
}

.performance-tag-negative {
  background-color: color(content-error);
  border-color: color(content-error);
  color: color(content-accent);
}
