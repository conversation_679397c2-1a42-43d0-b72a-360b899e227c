import React from "react";
import { Icon, IconProps } from "@gx-design/icon";
import clsx from "clsx";

export type TagProps = {
  /**
   The Tag's text
  */
  text?: string;
  /**
    Determinate if the Tag is dismissable
  */
  dismissable?: boolean;
  /**
    Onclick close function
  */
  onCloseClick?: React.MouseEventHandler<HTMLDivElement>;
  /**
    Icon
  */
  icon?: IconProps["name"];
  /**
    Icon class name
  */
  iconClassName?: string;
  /**
    Custom class names
  */
  className?: any;
};

export const Tag: React.FC<TagProps> = React.forwardRef<
  HTMLDivElement,
  TagProps
>(
  (
    { text, dismissable, icon, className, iconClassName, onCloseClick },
    ref
  ) => {
    return (
      <div
        ref={ref}
        className={clsx("gx-tag", className, {
          "gx-tag--iconOnly": !text,
        })}
      >
        {!text && icon ? (
          <Icon className={iconClassName && iconClassName} name={icon} />
        ) : (
          <>
            {icon && (
              <Icon
                className={`gx-tag__icon ${iconClassName && iconClassName}`}
                name={icon}
              />
            )}
            <span>{text}</span>
            {dismissable && (
              <div className="gx-tag__close" onClick={onCloseClick}>
                <Icon name="cross-circle--active" />
              </div>
            )}
          </>
        )}
      </div>
    );
  }
);
