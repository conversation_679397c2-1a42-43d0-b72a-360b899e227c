// ==========================================================================
// Typography - Utilities
// ==========================================================================

@use "@gx-design/theme/styles" as theme;

// ==========================================================================
// Typography - Theme
// ==========================================================================

$gx-weight-light: 300;
$gx-weight-regular: 400;
$gx-weight-bold: 600;

$gx-font-family-base: "Inter", Helvetica Neue, Helvetica, Arial, sans-serif !default;

b,
strong {
  font-weight: 600;
}

// TODO: Successivamente da spostare
html {
  font-family: $gx-font-family-base;
}

body {
  @include theme.typography(body);
  color: theme.color(content-high);
}

a {
  color: theme.color(content-action);
  text-decoration: none;

  &:hover,
  &:focus {
    color: theme.color(content-action);
    text-decoration: underline;
  }
}

h1,
h2,
h3,
h4,
h5,
h6 {
  font-weight: 500;
}

h4,
h5,
h6 {
  margin-top: 1rem;
  margin-bottom: 1rem;
  line-height: 1.1;
}

h5 {
  font-size: 1.4rem;
}

p {
  margin-bottom: 1rem;
}

@each $style, $value in theme.$typography {
  .gx-#{$style} {
    @include theme.typography($style);
  }
}

.gx-uppercase {
  text-transform: uppercase;
}

.gx-copy-label-light {
  @include theme.typography(overline);
  color: theme.color(content-medium);
}

.gx-text-ellipsis {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.gx-no-wrap {
  white-space: nowrap;
}

.gx-text-light {
  color: theme.color(content-medium);
}

.gx-subtitle {
  margin-bottom: theme.space(xl);
}

.gx-pointer {
  cursor: pointer;
}

.gx-text-info {
  color: theme.color(content-info);
}
.gx-text-success,
.gx-text-positive {
  color: theme.color(content-success);
}
.gx-text-error,
.gx-text-negative {
  color: theme.color(content-error);
}
.gx-text-warning {
  color: theme.color(content-warning);
}

////
.gx-uppercase {
  text-transform: uppercase;
}

.gx-copy-label-light {
  @include theme.typography(overline);
  color: theme.color(content-medium);
}

.gx-text-ellipsis {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.gx-no-wrap {
  white-space: nowrap;
}

.gx-text-light {
  color: theme.color(content-medium);
}

.gx-subtitle {
  margin-bottom: space(xl);
}

.gx-pointer {
  cursor: pointer;
}

.gx-text-info {
  color: theme.color(content-info);
}
.gx-text-success,
.gx-text-positive {
  color: theme.color(content-success);
}
.gx-text-error,
.gx-text-negative {
  color: theme.color(content-error);
}
.gx-text-warning {
  color: theme.color(content-warning);
}
