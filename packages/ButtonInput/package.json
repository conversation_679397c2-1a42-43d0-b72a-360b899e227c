{"name": "@gx-design/button-input", "version": "5.2.19", "description": "Gx Design ButtonInput component", "source": "src/ButtonInput.tsx", "main": "dist/index.js", "module": "dist/index.mjs", "peerDependencies": {"react": "*"}, "devDependencies": {"tsup": "^7.2.0"}, "dependencies": {"@gx-design/button": "^5.3.13", "@gx-design/helper-text": "^5.2.14", "@gx-design/icon": "^5.6.3", "@gx-design/tooltip": "^3.2.4", "clsx": "1.1.1"}, "scripts": {"build": "tsup", "watch": "tsup --watch"}, "repository": {"type": "git", "url": "********************:getrix/gx-design.git", "directory": "packages/ButtonInput"}, "typings": "dist/index.d.ts", "volta": {"extends": "../../package.json"}}