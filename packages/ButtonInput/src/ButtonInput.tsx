import React, { InputHTMLAttributes } from "react";
import { HelperText } from "@gx-design/helper-text";
import { <PERSON><PERSON> } from "@gx-design/button";
import { Icon } from "@gx-design/icon";
import { Tooltip } from "@gx-design/tooltip";
import clsx from "clsx";

export type ButtonInputProps = {
  /**
   Custom wrapper classname
  */
  className?: string;
  /**
   The label title
  */
  label: string;
  /**
   The content of the button (label or label and icon)
  */
  buttonContent: React.ReactNode;
  /**
  The tooltip helper text
  */
  tooltipHelper?: string;
  /**
  Determine if label is visible to user
  */
  isLabelVisible?: boolean;
  /**
  The error message
  */
  /**
   The button handler
  */
  onButtonClick: () => void;
  error?: string;
} & InputHTMLAttributes<HTMLInputElement>;

export const ButtonInput = React.forwardRef<HTMLInputElement, ButtonInputProps>(
  (
    {
      label,
      tooltipHelper,
      error = "",
      isLabelVisible = true,
      buttonContent = "Invia",
      className,
      onButtonClick,
      ...props
    },
    ref
  ) => {
    const buttonContentArray = React.Children.toArray(buttonContent);
    const containsOnlyIcon =
      buttonContentArray.length === 1 &&
      React.isValidElement(buttonContentArray[0]) &&
      buttonContentArray[0].type === Icon;

    return (
      <div className={`gx-input-wrapper ${className}`}>
        <label
          htmlFor={props.id && props.id}
          className={clsx("gx-label", {
            "gx-sr-only": !isLabelVisible,
          })}
        >
          {label}
          {props.required && <span className="gx-label__required">*</span>}
          {tooltipHelper && (
            <span className="gx-tip">
              <Tooltip position="top" text={tooltipHelper}>
                <Icon className="gx-icon--info" name="info-circle--active" />
              </Tooltip>
            </span>
          )}
        </label>
        <div
          className={clsx("gx-input-button-wrapper", {
            "is-disabled": props.disabled,
          })}
        >
          <input
            ref={ref}
            className={clsx("gx-input", {
              "gx-input--negative": error,
            })}
            {...props}
          />
          <Button
            iconOnly={containsOnlyIcon}
            onClick={onButtonClick}
            variant="accent"
          >
            {buttonContent}
          </Button>
        </div>
        {error && <HelperText text={error} style="error" />}
      </div>
    );
  }
);
