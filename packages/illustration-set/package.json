{"name": "@gx-design/illustration-set", "version": "3.1.2", "description": "Gx Design Illustration Set", "source": "src/illustrationSet.ts", "main": "dist/index.js", "module": "dist/index.mjs", "peerDependencies": {"react": "*"}, "devDependencies": {"tsup": "^7.2.0"}, "scripts": {"build": "tsup", "watch": "tsup --watch"}, "repository": {"type": "git", "url": "********************:getrix/gx-design.git", "directory": "packages/illustration-set"}, "typings": "dist/index.d.ts", "volta": {"extends": "../../package.json"}}