# Change Log

All notable changes to this project will be documented in this file.
See [Conventional Commits](https://conventionalcommits.org) for commit guidelines.

## [3.2.4](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/tooltip@3.2.3...@gx-design/tooltip@3.2.4) (2025-07-24)

**Note:** Version bump only for package @gx-design/tooltip

## [3.2.3](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/tooltip@3.2.2...@gx-design/tooltip@3.2.3) (2025-07-08)

**Note:** Version bump only for package @gx-design/tooltip

## [3.2.2](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/tooltip@3.2.1...@gx-design/tooltip@3.2.2) (2025-04-02)

**Note:** Version bump only for package @gx-design/tooltip

## [3.2.1](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/tooltip@3.2.0...@gx-design/tooltip@3.2.1) (2024-10-31)

**Note:** Version bump only for package @gx-design/tooltip

# [3.2.0](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/tooltip@3.1.0...@gx-design/tooltip@3.2.0) (2024-09-23)

### Features

- **css:** custom properties ([cbc56c0](https://gitlab.pepita.io/getrix/gx-design/commit/cbc56c0e3e675d85f92ad25ac135cce270f02b2b))

## [3.1.1-alpha.0](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/tooltip@3.1.0...@gx-design/tooltip@3.1.1-alpha.0) (2024-09-16)

**Note:** Version bump only for package @gx-design/tooltip

# [3.1.0](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/tooltip@3.0.4...@gx-design/tooltip@3.1.0) (2024-08-29)

### Features

- **colors:** pro new colors ([2103ea7](https://gitlab.pepita.io/getrix/gx-design/commit/2103ea76597b5ae75018d2d62947c6b01d613e82))

# [3.1.0-alpha.0](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/tooltip@3.0.4...@gx-design/tooltip@3.1.0-alpha.0) (2024-07-26)

### Features

- **colors:** pro new colors ([d88639f](https://gitlab.pepita.io/getrix/gx-design/commit/d88639f983895257b905dfad2fb65db3e75efee0))

## [3.0.5-alpha.0](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/tooltip@3.0.4...@gx-design/tooltip@3.0.5-alpha.0) (2024-07-22)

**Note:** Version bump only for package @gx-design/tooltip

## [3.0.4](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/tooltip@3.0.3...@gx-design/tooltip@3.0.4) (2024-04-18)

### Bug Fixes

- **out-animation:** on exit handler ([c0c71d7](https://gitlab.pepita.io/getrix/gx-design/commit/c0c71d75cf5becac1278fa97aae9eec11000de5d))

## [3.0.4-alpha.1](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/tooltip@3.0.4-alpha.0...@gx-design/tooltip@3.0.4-alpha.1) (2024-04-17)

### Bug Fixes

- **out-animation:** on exit handler ([4d8489d](https://gitlab.pepita.io/getrix/gx-design/commit/4d8489d3557b6d5f0501859b573d502cd55d8c87))

## [3.0.4-alpha.0](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/tooltip@3.0.3...@gx-design/tooltip@3.0.4-alpha.0) (2024-04-16)

### Bug Fixes

- **out-animation:** on exit handler ([bc7e282](https://gitlab.pepita.io/getrix/gx-design/commit/bc7e282dd6036ba2d4d6a8223f920f2a49b891bd))

## [3.0.3](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/tooltip@3.0.2...@gx-design/tooltip@3.0.3) (2024-03-26)

**Note:** Version bump only for package @gx-design/tooltip

## [3.0.2](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/tooltip@3.0.1...@gx-design/tooltip@3.0.2) (2024-03-25)

**Note:** Version bump only for package @gx-design/tooltip

## [3.0.1](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/tooltip@3.0.0...@gx-design/tooltip@3.0.1) (2024-03-20)

**Note:** Version bump only for package @gx-design/tooltip

# [3.0.0](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/tooltip@2.2.0...@gx-design/tooltip@3.0.0) (2024-03-18)

- feat!: new architecture ([5572bfc](https://gitlab.pepita.io/getrix/gx-design/commit/5572bfc9c05f1cb4aa5a0e134d2771ac5d7243f0))

### BREAKING CHANGES

- new architecture

# [2.2.0](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/tooltip@2.1.4...@gx-design/tooltip@2.2.0) (2024-03-18)

**Note:** Version bump only for package @gx-design/tooltip

# [2.2.0-alpha.1](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/tooltip@2.2.0-alpha.0...@gx-design/tooltip@2.2.0-alpha.1) (2024-03-11)

**Note:** Version bump only for package @gx-design/tooltip

# [2.2.0-alpha.0](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/tooltip@2.1.3...@gx-design/tooltip@2.2.0-alpha.0) (2024-03-07)

### Features

- **scss:** alert and theme styles ([7c0102d](https://gitlab.pepita.io/getrix/gx-design/commit/7c0102d309fce10724944b1e2e355d391d84ebe7))

## [2.1.3](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/tooltip@2.1.2...@gx-design/tooltip@2.1.3) (2023-12-20)

### Bug Fixes

- **effect:** use layout effect ([d1083f2](https://gitlab.pepita.io/getrix/gx-design/commit/d1083f21421066f796f1864758226155a2f6da21))

## [2.1.2](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/tooltip@2.1.1...@gx-design/tooltip@2.1.2) (2023-12-01)

**Note:** Version bump only for package @gx-design/tooltip

## [2.1.1](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/tooltip@2.1.0...@gx-design/tooltip@2.1.1) (2023-11-27)

**Note:** Version bump only for package @gx-design/tooltip

## [2.1.1-alpha.0](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/tooltip@2.1.0...@gx-design/tooltip@2.1.1-alpha.0) (2023-11-27)

**Note:** Version bump only for package @gx-design/tooltip

# [2.1.0](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/tooltip@2.0.10...@gx-design/tooltip@2.1.0) (2023-09-27)

### Features

- **storybook:** upgrade to 7 ([1c0e5e9](https://gitlab.pepita.io/getrix/gx-design/commit/1c0e5e941dcf7b841d1b5d2a2825f66f7921276e))

## [2.0.10](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/tooltip@2.0.9...@gx-design/tooltip@2.0.10) (2023-09-08)

**Note:** Version bump only for package @gx-design/tooltip

## [2.0.9](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/tooltip@2.0.8...@gx-design/tooltip@2.0.9) (2023-07-17)

**Note:** Version bump only for package @gx-design/tooltip

## [2.0.9-alpha.1](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/tooltip@2.0.9-alpha.0...@gx-design/tooltip@2.0.9-alpha.1) (2023-07-12)

### Bug Fixes

- **tooltip:** on scroll reposition ([93624d5](https://gitlab.pepita.io/getrix/gx-design/commit/93624d568d569affa22b250c6c187d27a9f0ecd4))

## [2.0.9-alpha.0](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/tooltip@2.0.8...@gx-design/tooltip@2.0.9-alpha.0) (2023-07-12)

**Note:** Version bump only for package @gx-design/tooltip

## [2.0.8](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/tooltip@2.0.5...@gx-design/tooltip@2.0.8) (2023-05-03)

### Bug Fixes

- **tooltip:** version ([eda260b](https://gitlab.pepita.io/getrix/gx-design/commit/eda260b48bc163cdc0fe6fbe4a2affb895cd48cf))

## [2.0.5](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/tooltip@2.0.5-alpha.0...@gx-design/tooltip@2.0.5) (2023-01-23)

**Note:** Version bump only for package @gx-design/tooltip

## [2.0.5-alpha.0](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/tooltip@2.0.4...@gx-design/tooltip@2.0.5-alpha.0) (2023-01-23)

### Bug Fixes

- **tooltip:** removed react class ([f420fc0](https://gitlab.pepita.io/getrix/gx-design/commit/f420fc0e1508c62a914828bfd8c4bdc113d42132))

## [2.0.4](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/tooltip@2.0.3...@gx-design/tooltip@2.0.4) (2022-11-22)

**Note:** Version bump only for package @gx-design/tooltip

## [2.0.4-alpha.1](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/tooltip@2.0.4-alpha.0...@gx-design/tooltip@2.0.4-alpha.1) (2022-11-21)

**Note:** Version bump only for package @gx-design/tooltip

## [2.0.4-alpha.0](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/tooltip@2.0.3...@gx-design/tooltip@2.0.4-alpha.0) (2022-11-21)

**Note:** Version bump only for package @gx-design/tooltip

## [2.0.3](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/tooltip@2.0.2...@gx-design/tooltip@2.0.3) (2022-10-12)

### Bug Fixes

- **notification:** fix notify ([213000c](https://gitlab.pepita.io/getrix/gx-design/commit/213000c8bc11b83eb5d9ff401e51d57d76411548))

## [2.0.3-alpha.2](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/tooltip@2.0.3-alpha.1...@gx-design/tooltip@2.0.3-alpha.2) (2022-10-12)

### Bug Fixes

- **notification:** fix notify ([fc85bb7](https://gitlab.pepita.io/getrix/gx-design/commit/fc85bb7d9a15abb9715faba3f8b57418d90fee74))

## [2.0.3-alpha.1](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/tooltip@2.0.2...@gx-design/tooltip@2.0.3-alpha.1) (2022-10-12)

### Bug Fixes

- **notification:** fix notify ([3dc25a9](https://gitlab.pepita.io/getrix/gx-design/commit/3dc25a9fb1907eb0f0a69e4c375cb61993c9edf2))
- **notification:** fix notify ([0632db9](https://gitlab.pepita.io/getrix/gx-design/commit/0632db9bdd8734d20d868ea98d38a07bf61df123))
- **notification:** fix notify ([5d949cd](https://gitlab.pepita.io/getrix/gx-design/commit/5d949cd42a3e78c3c63ba78aa1d1f4595e6e3397))

## [2.0.2](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/tooltip@2.0.1...@gx-design/tooltip@2.0.2) (2022-08-24)

### Bug Fixes

- **ci:** pipeline ([4844b50](https://gitlab.pepita.io/getrix/gx-design/commit/4844b50e391c8e3f05e63e63cc7059c890974a04))

## [2.0.1](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/tooltip@2.0.1-alpha.4...@gx-design/tooltip@2.0.1) (2022-08-24)

### Bug Fixes

- **icon-tooltip:** version bump ([ee32e3e](https://gitlab.pepita.io/getrix/gx-design/commit/ee32e3ed6174fd91f540f1a977a40e78f682ccb6))

## [2.0.1-alpha.6](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/tooltip@2.0.1-alpha.5...@gx-design/tooltip@2.0.1-alpha.6) (2022-08-22)

**Note:** Version bump only for package @gx-design/tooltip

## [2.0.1-alpha.5](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/tooltip@2.0.1-alpha.4...@gx-design/tooltip@2.0.1-alpha.5) (2022-08-22)

**Note:** Version bump only for package @gx-design/tooltip

## [2.0.1-alpha.4](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/tooltip@2.0.1-alpha.3...@gx-design/tooltip@2.0.1-alpha.4) (2022-08-02)

### Bug Fixes

- **release:** pepita-command ([759db81](https://gitlab.pepita.io/getrix/gx-design/commit/759db814e4a8b790526eaaba5633cbfeee6fddc4))
- **release:** pepita-command ([0c2903a](https://gitlab.pepita.io/getrix/gx-design/commit/0c2903a346dad3f793e3091c741d415e1d02fb00))

## [2.0.1-alpha.3](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/tooltip@2.0.1-alpha.2...@gx-design/tooltip@2.0.1-alpha.3) (2022-08-02)

### Bug Fixes

- **release:** pepita-command ([6654ba2](https://gitlab.pepita.io/getrix/gx-design/commit/6654ba27f362a439bdcf991079596ccc8a4264f5))

## [2.0.1-alpha.2](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/tooltip@2.0.1-alpha.1...@gx-design/tooltip@2.0.1-alpha.2) (2022-08-01)

**Note:** Version bump only for package @gx-design/tooltip

## [2.0.1-alpha.1](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/tooltip@2.0.1-alpha.0...@gx-design/tooltip@2.0.1-alpha.1) (2022-08-01)

**Note:** Version bump only for package @gx-design/tooltip

## [2.0.1-alpha.0](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/tooltip@2.0.0...@gx-design/tooltip@2.0.1-alpha.0) (2022-07-27)

### Bug Fixes

- **ci:** ci fix ([2077d96](https://gitlab.pepita.io/getrix/gx-design/commit/2077d964c372bf1e517b9431716db357119ebfc8))

# 2.0.0 (2022-07-18)

# 2.0.0-alpha.15 (2022-07-12)

# 2.0.0-alpha.14 (2022-07-08)

# 2.0.0-alpha.13 (2022-07-06)

# 2.0.0-alpha.12 (2022-07-06)

**Note:** Version bump only for package @gx-design/tooltip

# [2.0.0-alpha.17](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/tooltip@2.0.0-alpha.16...@gx-design/tooltip@2.0.0-alpha.17) (2022-07-18)

### Bug Fixes

- **tooltip:** tooltip resize ([f4a0294](https://gitlab.pepita.io/getrix/gx-design/commit/f4a02941f86fb892f8d6cc4089907e77dd5764aa))

# 2.0.0-alpha.16 (2022-07-15)

### Bug Fixes

- **tooltip:** fix typo ([d0668fc](https://gitlab.pepita.io/getrix/gx-design/commit/d0668fc66f20f36e6a19170e0cdaa32e0439eeb9))
- **tooltip:** tooltip position fix ([e644afa](https://gitlab.pepita.io/getrix/gx-design/commit/e644afab73b81529b35c8d9369863d17d6f83546))

# 2.0.0-alpha.15 (2022-07-12)

# 2.0.0-alpha.14 (2022-07-08)

# 2.0.0-alpha.13 (2022-07-06)

# 2.0.0-alpha.12 (2022-07-06)

# [2.0.0-alpha.15](https://gitlab.pepita.io/getrix/gx-design/compare/v2.0.0-alpha.14...v2.0.0-alpha.15) (2022-07-12)

**Note:** Version bump only for package @gx-design/tooltip

# [2.0.0-alpha.14](https://gitlab.pepita.io/getrix/gx-design/compare/v2.0.0-alpha.13...v2.0.0-alpha.14) (2022-07-08)

**Note:** Version bump only for package @gx-design/tooltip

# [2.0.0-alpha.13](https://gitlab.pepita.io/getrix/gx-design/compare/v2.0.0-alpha.12...v2.0.0-alpha.13) (2022-07-06)

**Note:** Version bump only for package @gx-design/tooltip

# [2.0.0-alpha.12](https://gitlab.pepita.io/getrix/gx-design/compare/v1.2.3...v2.0.0-alpha.12) (2022-07-06)

**Note:** Version bump only for package @gx-design/tooltip

# [2.0.0-alpha.11](https://gitlab.pepita.io/getrix/gx-design/compare/v2.0.0-alpha.10...v2.0.0-alpha.11) (2022-06-23)

**Note:** Version bump only for package @gx-design/tooltip

# [2.0.0-alpha.10](https://gitlab.pepita.io/getrix/gx-design/compare/v2.0.0-alpha.9...v2.0.0-alpha.10) (2022-06-23)

**Note:** Version bump only for package @gx-design/tooltip

# [2.0.0-alpha.7](https://gitlab.pepita.io/getrix/gx-design/compare/v2.0.0-alpha.6...v2.0.0-alpha.7) (2022-06-22)

**Note:** Version bump only for package @gx-design/tooltip

# [2.0.0-alpha.5](https://gitlab.pepita.io/getrix/gx-design/compare/v1.2.3...v2.0.0-alpha.5) (2022-06-22)

### Bug Fixes

- **descriptions:** fixed some descriptions ([f0e1cb6](https://gitlab.pepita.io/getrix/gx-design/commit/f0e1cb6c6fb41e799a74aed67844b944c5cba4df))

### Features

- **popover:** popover as package done ([2f47847](https://gitlab.pepita.io/getrix/gx-design/commit/2f47847caa5d2848ffd46b878f4889c006a44ac3))
