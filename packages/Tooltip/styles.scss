// ==========================================================================
// Tooltip - Tooltip/Components
// ==========================================================================
@use "@gx-design/theme/styles" as *;

@mixin generateTooltipArrow(
  $direction: "top",
  $color: color(background-reversed),
  $size: 0.8rem
) {
  @if ($direction == "top") {
    border-left: $size solid transparent;
    border-right: $size solid transparent;
    border-top: $size solid $color;
  }
  @if ($direction == "bottom") {
    border-left: $size solid transparent;
    border-right: $size solid transparent;
    border-bottom: $size solid $color;
  }
  @if ($direction == "left") {
    border-top: $size solid transparent;
    border-bottom: $size solid transparent;
    border-left: $size solid $color;
  }
  @if ($direction == "right") {
    border-top: $size solid transparent;
    border-bottom: $size solid transparent;
    border-right: $size solid $color;
  }
}

$tooltipPositions: (
  top,
  topLeft,
  topRight,
  left,
  right,
  bottom,
  bottomLeft,
  bottomRight
);

.gx-tooltip {
  position: absolute;
  width: auto;
  max-width: 16rem;
  padding: space(xs) space(sm);
  background: color(background-reversed);
  border-radius: radius(sm);
  color: color(content-accent);
  @include typography(body-tiny);
  @include z-index(tooltip);

  // Arrow
  &:after {
    content: "";
    width: 0;
    height: 0;
    position: absolute;
  }

  @each $tooltip, $tooltipPosition in $tooltipPositions {
    &--#{$tooltip} {
      @if (str-index($tooltip, "top")) {
        &:after {
          @include generateTooltipArrow("top");
          top: 100%;
        }
      }

      @if (str-index($tooltip, "bottom")) {
        &:after {
          @include generateTooltipArrow("bottom");
          bottom: 100%;
        }
      }

      @if (str-index($tooltip, "Left")) {
        &:after {
          left: space(sm);
        }
      }

      @if (str-index($tooltip, "Right")) {
        &:after {
          right: space(sm);
        }
      }

      @if ($tooltip == "top" or $tooltip == "bottom") {
        &:after {
          left: 50%;
          margin-left: -(space(sm));
        }
      }

      @if ($tooltip == "left") {
        &:after {
          @include generateTooltipArrow("left");
          top: 50%;
          left: 100%;
          margin-top: -(space(sm));
        }
      }

      @if ($tooltip == "right") {
        &:after {
          @include generateTooltipArrow("right");
          top: 50%;
          right: 100%;
          margin-top: -(space(sm));
        }
      }
    }
  }
}
