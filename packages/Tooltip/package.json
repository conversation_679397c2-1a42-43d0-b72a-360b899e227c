{"name": "@gx-design/tooltip", "version": "3.2.4", "description": "Gx Design Tooltip component", "source": "src/Tooltip.tsx", "main": "dist/index.js", "module": "dist/index.mjs", "peerDependencies": {"react": "*", "react-dom": "*"}, "devDependencies": {"tsup": "^7.2.0"}, "dependencies": {"@gx-design/theme": "^1.4.0", "clsx": "1.1.1"}, "scripts": {"build": "tsup", "watch": "tsup --watch"}, "repository": {"type": "git", "url": "********************:getrix/gx-design.git", "directory": "packages/Tooltip"}, "typings": "dist/index.d.ts", "volta": {"extends": "../../package.json"}}