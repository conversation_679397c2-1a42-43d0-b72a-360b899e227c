import { expect, it, describe } from "vitest";
import { render, screen } from "../../../test/utilities";
import { Tooltip, TooltipProps } from "./Tooltip";
import { Button } from "@gx-design/button";
import React from "react";

describe("Tooltip", () => {
  it("should render the children", () => {
    render(
      <Tooltip position="top" text="Tooltip text">
        <Button>Button</Button>
      </Tooltip>
    );

    expect(screen.getByText("Button")).toBeInTheDocument();
  });

  it("should toggle the tooltip on mouseenter and mouseleave", async () => {
    const { user } = render(
      <Tooltip position="top" text="Tooltip text">
        <Button>Button</Button>
      </Tooltip>
    );

    await user.hover(screen.getByText("Button"));
    expect(screen.getByText("Tooltip text")).toBeInTheDocument();
    await user.unhover(screen.getByText("Button"));
    setTimeout(() => {
      expect(screen.queryByText("Tooltip text")).not.toBeInTheDocument();
    });
  });

  it("can accept different positions", async () => {
    const tooltipText = "Tooltip text";
    const positions: TooltipProps["position"][] = [
      "top",
      "bottom",
      "left",
      "right",
      "topLeft",
      "topRight",
      "bottomLeft",
      "bottomRight",
    ];

    /*  const { unmount, user } = render(
      <Tooltip text={tooltipText} position={positions[0]}>
        <button>Button</button>
      </Tooltip>
    ); */

    for (const position of positions.slice(1)) {
      const { user, unmount } = render(
        <Tooltip text={tooltipText} position={position}>
          <button>Button</button>
        </Tooltip>
      );

      user.hover(screen.getByText("Button"));

      const tooltip = await screen.findByText(tooltipText);

      // Check if the correct class is applied correlating to the position
      expect(tooltip).toHaveClass(`gx-tooltip--${position}`);

      // Cleanup before the next test
      await user.unhover(screen.getByText("Button"));
      unmount();
    }
  });

  it("adjusts position when the window is resized", async () => {
    const { user } = render(
      <Tooltip text="Tooltip text">
        <button>Hover over me!</button>
      </Tooltip>
    );

    global.window.innerWidth = 500; // Change the window width
    global.window.dispatchEvent(new Event("resize"));

    await user.hover(screen.getByText(/Hover over me!/i));

    expect(await screen.findByText("Tooltip text")).toBeVisible();
  });
});
