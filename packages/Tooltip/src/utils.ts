type WindowDimensions = {
  scrollY: number;
};

type Coords = {
  top: number;
  left: number;
};

// Position type as you defined in the original component
type Position = "top" | "bottom" | "left" | "right" | "topLeft" | "topRight" | "bottomLeft" | "bottomRight";

export const calculateTooltipPosition = (
  position: Position,
  childrenRect: DOMRect,
  tooltipRect: DOMRect,
  windowDimensions: WindowDimensions
): Coords => {
  const { scrollY } = windowDimensions;
  const gxSpacing = 12; // This value can be adjusted based on your design

  let top = 0;
  let left = 0;

  switch (position) {
    case "top":
      left = childrenRect.x + childrenRect.width / 2 - tooltipRect.width / 2;
      top = childrenRect.y - tooltipRect.height - gxSpacing + scrollY;
      break;
    case "topLeft":
      left = childrenRect.x;
      top = childrenRect.y - tooltipRect.height - gxSpacing + scrollY;
      break;
    case "topRight":
      left = childrenRect.x + childrenRect.width - tooltipRect.width;
      top = childrenRect.y - tooltipRect.height - gxSpacing + scrollY;
      break;
    case "left":
      left = childrenRect.x - gxSpacing - tooltipRect.width;
      top = childrenRect.y + childrenRect.height / 2 - tooltipRect.height / 2 + scrollY;
      break;
    case "right":
      left = childrenRect.x + childrenRect.width + gxSpacing;
      top = childrenRect.y + childrenRect.height / 2 - tooltipRect.height / 2 + scrollY;
      break;
    case "bottom":
      left = childrenRect.x + childrenRect.width / 2 - tooltipRect.width / 2;
      top = childrenRect.y + childrenRect.height + gxSpacing + scrollY;
      break;
    case "bottomLeft":
      left = childrenRect.x;
      top = childrenRect.y + childrenRect.height + gxSpacing + scrollY;
      break;
    case "bottomRight":
      left = childrenRect.x + childrenRect.width - tooltipRect.width;
      top = childrenRect.y + childrenRect.height + gxSpacing + scrollY;
      break;
    default:
      left = childrenRect.x + childrenRect.width / 2 - tooltipRect.width / 2;
      top = childrenRect.y - tooltipRect.height - gxSpacing + scrollY;
      break;
  }

  return { top, left };
};
