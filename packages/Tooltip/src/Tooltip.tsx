import React, {
  useState,
  useRef,
  useEffect,
  useLayoutEffect,
  useCallback,
} from "react";
import { createPortal } from "react-dom";
import { calculateTooltipPosition } from "./utils";
import clsx from "clsx";

// Props type
export type TooltipProps = {
  position?:
    | "top"
    | "bottom"
    | "left"
    | "right"
    | "topLeft"
    | "topRight"
    | "bottomLeft"
    | "bottomRight";
  text: string;
  children: React.ReactElement;
};

export const Tooltip: React.FC<TooltipProps> = ({
  position = "top",
  text,
  children,
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const [showTooltip, setShowTooltip] = useState(false);
  const [isExiting, setIsExiting] = useState(false);
  const [coords, setCoords] = useState<React.CSSProperties>({
    top: 0,
    left: 0,
  });
  const childrenRef = useRef<Element>(null);
  const tooltipRef = useRef<HTMLDivElement>(null);

  const updateTooltipPosition = useCallback(() => {
    if (!childrenRef.current || !tooltipRef.current) return;
    const childrenRect = childrenRef.current.getBoundingClientRect();
    const tooltipRect = tooltipRef.current.getBoundingClientRect();
    const newCoords = calculateTooltipPosition(
      position,
      childrenRect,
      tooltipRect,
      window
    );
    setCoords(newCoords);
  }, [position]);

  useLayoutEffect(() => {
    if (isOpen) updateTooltipPosition();
  }, [isOpen, text, updateTooltipPosition]);

  // Show tooltip on hover
  const handleMouseEnter = useCallback(() => {
    setIsOpen(true);
    setShowTooltip(true);
    setIsExiting(false);
  }, []);

  // Hide tooltip on hover exit (animation part)
  const handleMouseLeave = useCallback(() => {
    setIsOpen(false);
    setIsExiting(true);
  }, []);

  // Unmount tooltip only at the end of exit animation
  const handleAnimationEnd = () => {
    if (!isOpen && isExiting) {
      setShowTooltip(false);
      setIsExiting(false);
    }
  };

  // Update position on resize/scroll
  useEffect(() => {
    if (!isOpen) return;
    window.addEventListener("resize", updateTooltipPosition);
    window.addEventListener("scroll", updateTooltipPosition);
    return () => {
      window.removeEventListener("resize", updateTooltipPosition);
      window.removeEventListener("scroll", updateTooltipPosition);
    };
  }, [isOpen, updateTooltipPosition]);

  const childWithProps = React.cloneElement(
    children as React.ReactElement<any>,
    {
      onMouseEnter: handleMouseEnter,
      onMouseLeave: handleMouseLeave,
      ref: childrenRef,
    }
  );

  return (
    <>
      {childWithProps}
      {showTooltip &&
        createPortal(
          <div
            ref={tooltipRef}
            className={clsx(`gx-tooltip gx-tooltip--${position}`, {
              "gx-fade-in": isOpen && !isExiting,
              "gx-fade-out": !isOpen && isExiting,
            })}
            style={coords}
            onAnimationEnd={handleAnimationEnd}
          >
            {text}
          </div>,
          document.body
        )}
    </>
  );
};
