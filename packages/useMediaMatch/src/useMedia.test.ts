import { describe, test, expect, beforeEach, vi, afterEach } from "vitest";
import { renderHook } from "@testing-library/react";
import { useMedia } from "./useMedia";

// Mock window if it doesn't exist in the test environment
if (typeof window === "undefined") {
  global.window = {} as any;
}

// Mock matchMedia
const mockMatchMedia = () => {
  // Track active queries
  const listeners = new Map<string, Array<(e: MediaQueryListEvent) => void>>();
  const matches = new Map<string, boolean>();

  // Default state for different breakpoints
  const defaultMatches = {
    "(min-width: 480px)": false, // xs
    "(min-width: 768px)": false, // sm
    "(min-width: 1024px)": false, // md
    "(min-width: 1280px)": false, // lg
    "(min-width: 1440px)": false, // xl
    "(min-width: 1600px)": false, // custom
  };

  // Initialize matches with defaults
  Object.entries(defaultMatches).forEach(([query, value]) => {
    matches.set(query, value);
  });

  // Create mock MediaQueryList
  const createMediaQueryList = (query: string): MediaQueryList => {
    return {
      matches: matches.get(query) ?? false,
      media: query,
      onchange: null,
      addListener: vi.fn((cb) => {
        if (!listeners.has(query)) {
          listeners.set(query, []);
        }
        listeners.get(query)?.push(cb);
      }),
      removeListener: vi.fn((cb) => {
        const callbacks = listeners.get(query) || [];
        const index = callbacks.indexOf(cb);
        if (index !== -1) {
          callbacks.splice(index, 1);
        }
      }),
      addEventListener: vi.fn((event, cb) => {
        if (event !== "change") return;
        if (!listeners.has(query)) {
          listeners.set(query, []);
        }
        listeners.get(query)?.push(cb as any);
      }),
      removeEventListener: vi.fn((event, cb) => {
        if (event !== "change") return;
        const callbacks = listeners.get(query) || [];
        const index = callbacks.indexOf(cb as any);
        if (index !== -1) {
          callbacks.splice(index, 1);
        }
      }),
      dispatchEvent: vi.fn(() => true),
    } as MediaQueryList;
  };

  // Create a spy we can check in tests
  const matchMediaSpy = vi.fn((query: string) => {
    // Make sure the query exists in matches
    if (!matches.has(query)) {
      matches.set(query, false);
    }
    return createMediaQueryList(query);
  });

  // Set the spy as window.matchMedia
  window.matchMedia = matchMediaSpy;

  // Helper to change screen size and trigger listeners
  const changeScreenSize = (width: number) => {
    matches.set("(min-width: 480px)", width >= 480); // xs
    matches.set("(min-width: 768px)", width >= 768); // sm
    matches.set("(min-width: 1024px)", width >= 1024); // md
    matches.set("(min-width: 1280px)", width >= 1280); // lg
    matches.set("(min-width: 1440px)", width >= 1440); // xl
    matches.set("(min-width: 1600px)", width >= 1600); // custom

    // Trigger all listeners with new matches
    listeners.forEach((handlers, query) => {
      const event = {
        matches: matches.get(query) ?? false,
        media: query,
      } as MediaQueryListEvent;

      handlers.forEach((handler) => handler(event));
    });
  };

  return { changeScreenSize, matchMediaSpy };
};

describe("useMedia hook", () => {
  const { changeScreenSize, matchMediaSpy } = mockMatchMedia();

  beforeEach(() => {
    vi.clearAllMocks();
    // Pre-set some initial screen size
    changeScreenSize(400);
  });

  afterEach(() => {
    vi.restoreAllMocks();
  });

  test("should call matchMedia with the right query for xs breakpoint", () => {
    renderHook(() => useMedia("xs"));
    expect(matchMediaSpy).toHaveBeenCalledWith("(min-width: 480px)");
  });

  test("should return false for xs breakpoint when screen is smaller than 480px", () => {
    changeScreenSize(400);
    const { result } = renderHook(() => useMedia("xs"));
    expect(result.current).toBe(false);
  });

  test("should return true for xs breakpoint when screen is 480px or larger", () => {
    changeScreenSize(480);
    const { result } = renderHook(() => useMedia("xs"));
    expect(result.current).toBe(true);
  });

  test("should return true for md breakpoint when screen is 1024px or larger", () => {
    changeScreenSize(1024);
    const { result } = renderHook(() => useMedia("md"));
    expect(result.current).toBe(true);
  });

  test("should return false for lg breakpoint when screen is smaller than 1280px", () => {
    changeScreenSize(1200);
    const { result } = renderHook(() => useMedia("lg"));
    expect(result.current).toBe(false);
  });

  test("should accept custom media query strings", () => {
    changeScreenSize(1600);
    const { result } = renderHook(() => useMedia("(min-width: 1600px)"));
    expect(result.current).toBe(true);
  });

  test("should update when screen size changes", async () => {
    // Initial size: smaller than sm
    changeScreenSize(700);
    const { result, rerender } = renderHook(() => useMedia("sm"));
    expect(result.current).toBe(false);

    // Resize to sm breakpoint
    changeScreenSize(768);
    rerender();
    expect(result.current).toBe(true);
  });

  test("should handle multiple hooks using the same query", () => {
    changeScreenSize(900);

    // eslint-disable-next-line testing-library/render-result-naming-convention
    const hook1 = renderHook(() => useMedia("md"));
    // eslint-disable-next-line testing-library/render-result-naming-convention
    const hook2 = renderHook(() => useMedia("md"));

    expect(hook1.result.current).toBe(false);
    expect(hook2.result.current).toBe(false);

    // Resize to md breakpoint
    changeScreenSize(1024);
    hook1.rerender();
    hook2.rerender();

    expect(hook1.result.current).toBe(true);
    expect(hook2.result.current).toBe(true);
  });

  test("should clean up listeners when component unmounts", () => {
    let removeEventListenerCalled = false;

    // Create a special version of matchMedia just for this test
    const originalMatchMedia = window.matchMedia;
    window.matchMedia = vi.fn().mockImplementation((query: string) => {
      const mediaQueryList = originalMatchMedia(query);

      // Wrap the removeEventListener method to track calls
      const originalRemoveEventListener = mediaQueryList.removeEventListener;
      mediaQueryList.removeEventListener = vi.fn((event, listener) => {
        removeEventListenerCalled = true;
        return originalRemoveEventListener.call(mediaQueryList, event, listener);
      });

      return mediaQueryList;
    });

    const { unmount } = renderHook(() => useMedia("lg"));

    unmount();

    expect(removeEventListenerCalled).toBe(true);

    // Restore original mock
    window.matchMedia = originalMatchMedia;
  });

  test("should return different values for different breakpoints at the same screen size", () => {
    changeScreenSize(1100);

    const { result: xsResult } = renderHook(() => useMedia("xs"));
    const { result: smResult } = renderHook(() => useMedia("sm"));
    const { result: mdResult } = renderHook(() => useMedia("md"));
    const { result: lgResult } = renderHook(() => useMedia("lg"));
    const { result: xlResult } = renderHook(() => useMedia("xl"));

    expect(xsResult.current).toBe(true); // >= 480px
    expect(smResult.current).toBe(true); // >= 768px
    expect(mdResult.current).toBe(true); // >= 1024px
    expect(lgResult.current).toBe(false); // Not >= 1280px
    expect(xlResult.current).toBe(false); // Not >= 1440px
  });
});
