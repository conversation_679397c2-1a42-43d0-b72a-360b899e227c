import { useMemo, useState, useEffect } from "react";

const config = {
  mobile: "(max-width: 479px)",
  "small-screen": "(max-width: 900px)",
  tablet: "(min-width: 480px) and (max-width: 767px)",
  desktop: "(min-width: 768px)",
  largeDesktop: "(min-width: 1024px)",
  extraLargeDesktop: "(max-width: 1280px)",
};

/**
 * Original useMediaMatch hook for backward compatibility
 *
 * @deprecated Use the new useMedia() hook instead for better performance and SSR support.
 * This function will be removed in a future version.
 *
 * @example
 * // Instead of:
 * // const isMobile = useMediaMatch('mobile');
 * // Use:
 * // const isMobile = useMedia('xs');
 *
 * @example
 * // Instead of:
 * // const isWideScreen = useMediaMatch('(min-width: 1600px)');
 * // Use:
 * // const isWideScreen = useMedia('(min-width: 1600px)');
 *
 * @example
 * // The useMedia hook is more efficient with shared listeners:
 * // const isMobile = useMedia('xs');
 * // const isTablet = useMedia('sm');
 * // const isDesktop = useMedia('md');
 *
 * @example
 * // useMedia supports these predefined breakpoints:
 * // - 'xs': (min-width: 480px)  - Extra small devices and up
 * // - 'sm': (min-width: 768px)  - Small devices and up
 * // - 'md': (min-width: 1024px) - Medium devices and up
 * // - 'lg': (min-width: 1280px) - Large devices and up
 * // - 'xl': (min-width: 1440px) - Extra large devices and up
 *
 * @param {string} type - Either a key from the default config or a media query string
 * @returns {boolean} True if the media query matches, false otherwise
 */
export function useMediaMatch(type: keyof typeof config | (string & {})): boolean {
  const mediaQuery =
    typeof config[type as keyof typeof config] === "string" ? config[type as keyof typeof config] : type;
  let media = useMemo(() => matchMedia(mediaQuery), [mediaQuery]);
  let [matches, setMatches] = useState(() => (media ? media.matches : false));

  useEffect(() => {
    if (!media) return;

    function handler() {
      setMatches(media.matches);
    }

    media.addEventListener("change", handler);
    return () => {
      media.removeEventListener("change", handler);
    };
  }, [media]);

  return matches;
}
