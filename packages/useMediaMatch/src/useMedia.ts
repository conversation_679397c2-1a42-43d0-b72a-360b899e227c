import { useSyncExternalStore } from "react";

export const BREAKPOINTS = {
  xs: 480,
  sm: 768,
  md: 1024,
  lg: 1280,
  xl: 1440,
} as const;

const queries = {
  xs: `(min-width: ${BREAKPOINTS.xs}px)`,
  sm: `(min-width: ${BREAKPOINTS.sm}px)`,
  md: `(min-width: ${BREAKPOINTS.md}px)`,
  lg: `(min-width: ${BREAKPOINTS.lg}px)`,
  xl: `(min-width: ${BREAKPOINTS.xl}px)`,
};

type BreakpointKey = keyof typeof queries;
type QueryKey = BreakpointKey | string;

// Global store for query string (both default and custom)
const listeners = new Map<string, Set<() => void>>();
const values = new Map<string, boolean>();
const mediaQueryLists = new Map<string, MediaQueryList>();

/**
 * Sets up a media query and its event listeners
 * Uses a global store to cache queries and avoid duplicate listeners
 *
 * @param query - The media query string to listen to
 * @returns The MediaQueryList object or null if window is undefined (SSR)
 * @internal
 */
function setupMediaQuery(query: string): MediaQueryList | null {
  if (typeof window === "undefined") return null;

  if (mediaQueryLists.has(query)) {
    return mediaQueryLists.get(query)!;
  }

  const mql = window.matchMedia(query);
  values.set(query, mql.matches);

  const handler = (e: MediaQueryListEvent) => {
    values.set(query, e.matches);
    listeners.get(query)?.forEach((cb) => cb());
  };

  mql.addEventListener("change", handler);
  listeners.set(query, new Set());
  mediaQueryLists.set(query, mql);

  return mql;
}

/**
 * React hook that tracks state of a CSS media query
 * Uses React's useSyncExternalStore for subscription management
 * Supports both predefined breakpoints and custom media queries
 * Shares media query listeners between components for better performance
 *
 * @param key - Either a predefined breakpoint key ('xs', 'sm', 'md', 'lg', 'xl') or a custom media query string
 * @returns Boolean indicating whether the media query matches
 *
 * @example
 * // Using predefined breakpoints
 * const isDesktop = useMedia('lg');
 *
 * @example
 * // Using custom media query
 * const isWideScreen = useMedia('(min-width: 1600px)');
 */
export function useMedia(key: QueryKey): boolean {
  // If key is a known breakpoint, use the predefined query, otherwise use the provided string
  const query = key in queries ? queries[key as BreakpointKey] : key;

  return useSyncExternalStore(
    (callback: () => void) => {
      if (typeof window === "undefined") return () => {};

      const mql = setupMediaQuery(query);
      listeners.get(query)?.add(callback);

      return () => {
        listeners.get(query)?.delete(callback);

        if (listeners.get(query)?.size === 0 && mql) {
          mql.removeEventListener("change", () => {});
          listeners.delete(query);
          values.delete(query);
          mediaQueryLists.delete(query);
        }
      };
    },
    () => values.get(query) ?? false,
    () => false // fallback for SSR
  );
}
