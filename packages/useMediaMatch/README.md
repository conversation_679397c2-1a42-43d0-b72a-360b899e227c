# Media Query Hooks

This package provides React hooks for responsive designs based on media queries.

## Hooks Overview

### useMedia

A modern React hook that uses `useSyncExternalStore` for efficient subscription management to media queries.

#### Features

- **Shared listeners**: Uses a global store to share media query listeners between components
- **SSR compatible**: Provides fallbacks for server-side rendering
- **Efficient**: Only creates one media query listener per unique query, regardless of how many components use it
- **Cleanup**: Proper cleanup of listeners when no components are using a specific query

#### Usage

```tsx
import { useMedia } from "@gx-design/use-media-match";

function ResponsiveComponent() {
  // Using predefined breakpoints
  const isDesktop = useMedia("lg"); // true if width >= 1280px
  const isTablet = useMedia("md"); // true if width >= 1024px

  // Using custom media query
  const isWideScreen = useMedia("(min-width: 1600px)");
  const isPortrait = useMedia("(orientation: portrait)");

  return (
    <div>
      {isDesktop && <p>Desktop content</p>}
      {isTablet && !isDesktop && <p>Tablet content</p>}
      {isWideScreen && <p>Wide screen content</p>}
      {isPortrait && <p>Portrait mode content</p>}
    </div>
  );
}
```

### useMediaMatch (Legacy)

The original media query hook for backward compatibility.

#### Usage

```tsx
import { useMediaMatch } from "@gx-design/use-media-match";

function LegacyComponent() {
  const isMobile = useMediaMatch("mobile");
  const isTablet = useMediaMatch("tablet");
  const isDesktop = useMediaMatch("desktop");
  const isCustomSize = useMediaMatch("(min-width: 1600px)");

  // Rest of your component
}
```

## Predefined Breakpoints

Both hooks use the following predefined breakpoints:

```typescript
// Desktop-first approach with min-width queries
{
  xs: '(min-width: 480px)',  // 480px and up
  sm: '(min-width: 768px)',  // 768px and up
  md: '(min-width: 1024px)', // 1024px and up
  lg: '(min-width: 1280px)', // 1280px and up
  xl: '(min-width: 1440px)', // 1440px and up
}

// Legacy breakpoints (useMediaMatch only)
{
  mobile: "(max-width: 479px)",
  "small-screen": "(max-width: 900px)",
  tablet: "(min-width: 480px) and (max-width: 767px)",
  desktop: "(min-width: 768px)",
  largeDesktop: "(min-width: 1024px)",
  extraLargeDesktop: "(max-width: 1280px)",
}
```

## Which Hook Should I Use?

- **For new code**: Use `useMedia` for the most efficient implementation with modern React features
- **For legacy code**: Use `useMediaMatch` to maintain compatibility with existing code

## Implementation Differences

| Feature             | useMedia                 | useMediaMatch                           |
| ------------------- | ------------------------ | --------------------------------------- |
| Subscription API    | useSyncExternalStore     | useEffect                               |
| Media Query Sharing | Yes (shared listeners)   | No (each instance creates own listener) |
| SSR Support         | Yes, with fallbacks      | Yes                                     |
| API Style           | Function returns boolean | Function returns boolean                |
| Breakpoint Naming   | Desktop-first approach   | Mixed approach                          |

## Example

```tsx
import { useMedia } from "@gx-design/use-media-match";

function ResponsiveLayout({ children }) {
  const isDesktop = useMedia("lg");
  const isTablet = useMedia("md") && !useMedia("lg");
  const isMobile = !useMedia("md");

  return (
    <div
      className={`layout ${isDesktop ? "desktop" : ""} ${
        isTablet ? "tablet" : ""
      } ${isMobile ? "mobile" : ""}`}
    >
      {isDesktop && <DesktopNavigation />}
      {isTablet && <TabletNavigation />}
      {isMobile && <MobileNavigation />}

      <main>{children}</main>
    </div>
  );
}
```
