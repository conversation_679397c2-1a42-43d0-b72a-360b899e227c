{"name": "@gx-design/use-media-match", "version": "3.0.2", "description": "Gx Design useMediaMatch hook", "source": "src/index.ts", "main": "dist/index.js", "module": "dist/index.mjs", "peerDependencies": {"react": "*"}, "devDependencies": {"tsup": "^7.2.0"}, "dependencies": {"clsx": "1.1.1"}, "scripts": {"build": "tsup", "watch": "tsup --watch"}, "repository": {"type": "git", "url": "********************:getrix/gx-design.git", "directory": "packages/useMediaMatch"}, "typings": "dist/index.d.ts", "volta": {"extends": "../../package.json"}}