# Change Log

All notable changes to this project will be documented in this file.
See [Conventional Commits](https://conventionalcommits.org) for commit guidelines.

## [3.0.2](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/use-media-match@3.0.1...@gx-design/use-media-match@3.0.2) (2025-07-17)

### Bug Fixes

- **useMediaMatch:** remove deprecated addListener and removeListener ([b7c2da8](https://gitlab.pepita.io/getrix/gx-design/commit/b7c2da8bdb8079015da557fec550e9090ef82351))

## [3.0.1](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/use-media-match@3.0.0...@gx-design/use-media-match@3.0.1) (2024-05-14)

**Note:** Version bump only for package @gx-design/use-media-match

# [3.0.0](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/use-media-match@2.3.1...@gx-design/use-media-match@3.0.0) (2024-03-18)

- feat!: new architecture ([5572bfc](https://gitlab.pepita.io/getrix/gx-design/commit/5572bfc9c05f1cb4aa5a0e134d2771ac5d7243f0))

### BREAKING CHANGES

- new architecture

## [2.3.1](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/use-media-match@2.3.0...@gx-design/use-media-match@2.3.1) (2023-12-01)

**Note:** Version bump only for package @gx-design/use-media-match

# [2.3.0](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/use-media-match@2.2.0...@gx-design/use-media-match@2.3.0) (2023-11-27)

**Note:** Version bump only for package @gx-design/use-media-match

# [2.3.0-alpha.0](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/use-media-match@2.2.0...@gx-design/use-media-match@2.3.0-alpha.0) (2023-11-27)

### Features

- **use-match-media:** custom value ([8bd4b43](https://gitlab.pepita.io/getrix/gx-design/commit/8bd4b43dedbbdef87584baa0f76b6b10844b0ae2))

# [2.2.0](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/use-media-match@2.1.5...@gx-design/use-media-match@2.2.0) (2023-09-27)

### Features

- **storybook:** upgrade to 7 ([1c0e5e9](https://gitlab.pepita.io/getrix/gx-design/commit/1c0e5e941dcf7b841d1b5d2a2825f66f7921276e))

## [2.1.5](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/use-media-match@2.1.4...@gx-design/use-media-match@2.1.5) (2023-09-08)

**Note:** Version bump only for package @gx-design/use-media-match

## [2.1.4](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/use-media-match@2.1.1...@gx-design/use-media-match@2.1.4) (2023-05-03)

### Bug Fixes

- **version:** update ([b86d122](https://gitlab.pepita.io/getrix/gx-design/commit/b86d122e4ebfae3c29b22379c844880c05c612b5))

## [2.1.1](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/use-media-match@2.1.0...@gx-design/use-media-match@2.1.1) (2023-04-18)

### Bug Fixes

- **media-match:** restored deprecated addListener to support Safari ([004c335](https://gitlab.pepita.io/getrix/gx-design/commit/004c33521869b227d8886673934a1b0f02828855))

# [2.1.0](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/use-media-match@2.1.0-alpha.1...@gx-design/use-media-match@2.1.0) (2023-04-03)

**Note:** Version bump only for package @gx-design/use-media-match

# [2.1.0-alpha.1](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/use-media-match@2.1.0-alpha.0...@gx-design/use-media-match@2.1.0-alpha.1) (2023-04-03)

**Note:** Version bump only for package @gx-design/use-media-match

# 2.1.0-alpha.0 (2023-04-03)

### Features

- **toolbar:** toolbar quick filters ([b688dcc](https://gitlab.pepita.io/getrix/gx-design/commit/b688dcc608ec84ea20e67da9e3c39b9fedec4822))
