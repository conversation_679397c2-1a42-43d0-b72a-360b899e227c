import { describe, it, expect } from "vitest";
import { render, screen } from "@testing-library/react";
import userEvent from "@testing-library/user-event";
import { Toggle } from "./Toggle";

describe("Toggle Component", () => {
  const args = {
    id: "check1",
    name: "check1",
    label: "Test label",
  };

  it("renders correctly with label", () => {
    render(<Toggle {...args} />);
    expect(screen.getByLabelText("Test label")).toBeInTheDocument();
  });

  it("renders with error message when error prop is provided", () => {
    const errorMessage = "This is an error message";
    render(<Toggle error={errorMessage} {...args} />);
    expect(screen.getByText(errorMessage)).toBeInTheDocument();
    const input = screen.getByLabelText("Test label");
    expect(input).toHaveClass("is-error");
  });

  it("toggles on click", async () => {
    render(<Toggle {...args} />);
    const checkbox = screen.getByLabelText("Test label");

    expect(checkbox).not.toBeChecked();

    await userEvent.click(checkbox);
    expect(checkbox).toBeChecked();

    await userEvent.click(checkbox);
    expect(checkbox).not.toBeChecked();
  });

  it("does not toggle when disabled", async () => {
    render(<Toggle disabled {...args} />);
    const checkbox = screen.getByLabelText("Test label");

    expect(checkbox).toBeDisabled();
    expect(checkbox).not.toBeChecked();

    await userEvent.click(checkbox);
    expect(checkbox).not.toBeChecked();
  });
});
