import { HelperText } from "@gx-design/helper-text";
import { Icon } from "@gx-design/icon";
import clsx from "clsx";
import React, {
  ForwardRefRenderFunction,
  InputHTMLAttributes,
  forwardRef,
} from "react";

export type ToggleProps = {
  /**
    Custom class names
  */
  className?: string;
  /**
   * The label title, `ReactNode` is accepted
   */
  label?: React.ReactNode;
  /**
   * The error message
   */
  error?: string;
} & InputHTMLAttributes<HTMLInputElement>;

const ToggleComponent: ForwardRefRenderFunction<
  HTMLInputElement,
  ToggleProps
> = ({ className, label, error = "", ...props }, ref) => {
  return (
    <>
      <div
        className={clsx("gx-toggle", {
          "is-disabled": props.disabled,
          className,
        })}
      >
        {label && (
          <label className="gx-toggle__text" htmlFor={props.id}>
            {label}
          </label>
        )}
        <div className="gx-toggle__element">
          <input
            type="checkbox"
            ref={ref}
            className={clsx({
              "is-error": error,
            })}
            {...props}
          />
          <span className="gx-toggle__iconStatus ">
            <Icon name="cross" className="is-off" />
            <Icon name="check" className="is-on" />
          </span>
        </div>
      </div>

      {error && <HelperText text={error} style="error" />}
    </>
  );
};

export const Toggle = forwardRef(ToggleComponent);
