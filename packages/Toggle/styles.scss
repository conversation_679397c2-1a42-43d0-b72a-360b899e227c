@use "@gx-design/theme/styles" as *;

$gx-toggle: "gx-toggle";
$gx-toggle-width: 4.8rem;
$gx-toggle-height: 2.4rem;

.#{$gx-toggle} {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  cursor: pointer;

  &__element {
    align-self: flex-start;
    position: relative;

    input {
      display: flex;
      align-items: center;
      align-self: flex-start;
      justify-content: center;
      flex-shrink: 0;
      appearance: none;
      border: 1px solid color(border-selectable);
      border-radius: radius(rounded);
      background-color: color(background-selectable);
      height: $gx-toggle-height;
      width: $gx-toggle-width;
      cursor: pointer;

      &.is-error {
        border-color: color(border-error);
        background-color: color(background-error);
      }

      & ~ .gx-toggle__iconStatus,
      &:before {
        bottom: -1px;
        height: $gx-toggle-height;
        left: 0;
        margin: auto 0;
        position: absolute;
        right: 0;
        top: -1px;
        transition-duration: 0.26s;
        transition-property: transform, background-color, color;
        transition-timing-function: ease;
        width: 2.4rem;
      }

      &::before {
        background-color: var(--background-selectable);
        border: 1px solid;
        border-color: inherit;
        border-radius: radius(rounded);
        content: "";
      }

      & ~ .gx-toggle__iconStatus {
        padding: 0.4rem;
        fill: var(--content-selectable);
        pointer-events: none;
      }

      &:checked {
        background-color: var(--background-selected-high);
        border-color: var(--background-selected-high);

        & ~ .gx-toggle__iconStatus,
        &::before {
          transform: translate3d(#{$gx-toggle-height}, 0, 0);
        }

        & ~ .gx-toggle__iconStatus {
          fill: var(--content-selected);

          .is-on {
            transform: scale(1);
          }
          .is-off {
            transform: scale(0);
          }
        }
      }
    }
  }

  &__iconStatus {
    display: flex;

    svg {
      @include icon-size(xs);
      position: absolute;
      transition: 0.26s;
    }

    .is-off {
      transform: scale(1);
    }

    .is-on {
      transform: scale(0);
      color: color(content-selected);
    }
  }

  &__text {
    flex: 1 1 percentage(1/3);
    padding-right: 0.8rem;
  }

  &.is-disabled {
    cursor: not-allowed;
    opacity: 0.5;

    input {
      cursor: not-allowed;
    }
  }
}
