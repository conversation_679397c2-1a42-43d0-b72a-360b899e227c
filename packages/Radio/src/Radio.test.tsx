import { describe, it, expect } from "vitest";
import { render, screen } from "@testing-library/react";
import userEvent from "@testing-library/user-event";
import { RadioGroup, Radio } from "./Radio";

describe("RadioGroup and Radio components", () => {
  const radioOptions = [
    { label: "Option 1", value: "1" },
    { label: "Option 2", value: "2" },
    { label: "Option 3", value: "3" },
  ];

  it("renders the RadioGroup with all child radios", () => {
    render(
      <RadioGroup label="Test Radio Group">
        {radioOptions.map((option) => (
          <Radio key={option.value} label={option.label} value={option.value} />
        ))}
      </RadioGroup>
    );

    expect(screen.getByText("Test Radio Group")).toBeInTheDocument();
    radioOptions.forEach((option) => {
      expect(screen.getByLabelText(option.label)).toBeInTheDocument();
    });
  });

  it("marks the RadioGroup as required and displays asterisk", () => {
    render(
      <RadioGroup label="Required Radio Group" required>
        {radioOptions.map((option) => (
          <Radio key={option.value} label={option.label} value={option.value} />
        ))}
      </RadioGroup>
    );

    expect(screen.getByText("Required Radio Group")).toBeInTheDocument();
    expect(screen.getByText("*")).toBeInTheDocument();
  });

  it("should display the tooltip when tooltipHelper is provided", async () => {
    const { baseElement } = render(
      <RadioGroup
        label="Test Label"
        tooltipHelper="This is a tooltip helper"
        isLabelVisible={true}
      >
        {radioOptions.map((option) => (
          <Radio
            key={option.value}
            label={option.label}
            value={option.value}
            name="radioGroupName"
          />
        ))}
      </RadioGroup>
    );

    const labelElement = screen.getByText("Test Label");
    expect(labelElement).toBeInTheDocument();

    const iconElement = baseElement.querySelector(".gx-icon--info");
    expect(iconElement).toBeInTheDocument();

    if (iconElement) {
      await userEvent.hover(iconElement);
      const tooltipElement = await screen.findByText(
        "This is a tooltip helper"
      );

      expect(tooltipElement).toBeInTheDocument();
    } else {
      expect.fail("Icon element not found");
    }
  });
  it("renders radios in column layout when column prop is true", () => {
    render(
      <RadioGroup label="Column Layout" column>
        {radioOptions.map((option) => (
          <Radio key={option.value} label={option.label} value={option.value} />
        ))}
      </RadioGroup>
    );

    const radioWrapper = screen
      .getByText("Column Layout")
      .parentElement?.querySelector(".gx-radio-wrapper");
    expect(radioWrapper).toBeInTheDocument();

    expect(radioWrapper).toHaveClass("gx-radio-wrapper--column");
  });

  it("allows selecting a radio option", async () => {
    render(
      <RadioGroup label="Selectable Radios">
        {radioOptions.map((option) => (
          <Radio
            key={option.value}
            label={option.label}
            value={option.value}
            name="radioGroupName"
          />
        ))}
      </RadioGroup>
    );

    const option1 = screen.getByLabelText("Option 1");
    const option2 = screen.getByLabelText("Option 2");
    const option3 = screen.getByLabelText("Option 3");

    expect(option1).not.toBeChecked();
    expect(option2).not.toBeChecked();
    expect(option3).not.toBeChecked();

    await userEvent.click(option1);
    expect(option1).toBeChecked();
    expect(option2).not.toBeChecked();
    expect(option3).not.toBeChecked();

    await userEvent.click(option2);
    expect(option1).not.toBeChecked();
    expect(option2).toBeChecked();
    expect(option3).not.toBeChecked();

    await userEvent.click(option3);
    expect(option1).not.toBeChecked();
    expect(option2).not.toBeChecked();
    expect(option3).toBeChecked();
  });

  it("does not allow selecting a disabled radio", async () => {
    render(
      <RadioGroup label="Disabled Radio">
        <Radio label="Option 1" value="1" disabled />
        <Radio label="Option 2" value="2" />
      </RadioGroup>
    );

    const disabledRadio = screen.getByLabelText("Option 1");
    const enabledRadio = screen.getByLabelText("Option 2");

    expect(disabledRadio).toBeDisabled();
    expect(enabledRadio).not.toBeDisabled();

    await userEvent.click(disabledRadio);
    expect(disabledRadio).not.toBeChecked();

    await userEvent.click(enabledRadio);
    expect(enabledRadio).toBeChecked();
  });

  it("renders radios with button variant", () => {
    render(
      <RadioGroup label="Button Variant">
        {radioOptions.map((option) => (
          <Radio
            key={option.value}
            label={option.label}
            value={option.value}
            variant="button"
          />
        ))}
      </RadioGroup>
    );

    const buttonRadio = screen.getByLabelText("Option 1");
    expect(buttonRadio.parentElement).toHaveClass("gx-radio-button");
  });

  it("renders radios with chip variant", () => {
    render(
      <RadioGroup label="Chip Variant">
        {radioOptions.map((option) => (
          <Radio
            key={option.value}
            label={option.label}
            value={option.value}
            variant="chip"
          />
        ))}
      </RadioGroup>
    );

    const chipRadio = screen.getByLabelText("Option 1");
    expect(chipRadio.parentElement).toHaveClass("gx-radio-chip");
  });

  it("renders an SVG when the icon prop is provided", () => {
    const { container } = render(
      <Radio label="Radio with Icon" value="1" icon="magic-wand" />
    );

    const svgElement = container.querySelector("svg");

    expect(svgElement).not.toBeNull();

    expect(svgElement!.childElementCount).toBeGreaterThan(0);
  });
});
