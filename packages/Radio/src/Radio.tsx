import React, {
  InputHTMLAttributes,
  forwardRef,
  ForwardRefRenderFunction,
} from "react";
import clsx from "clsx";
import { HelperText } from "@gx-design/helper-text";
import { Tooltip } from "@gx-design/tooltip";
import { Icon, IconProps } from "@gx-design/icon";

export type RadioGroupProps = {
  /**
   * The label title, `ReactNode` is accepted
   */
  label: React.ReactNode;
  /**
   * Variant of the radios
   */
  variant?: "button" | "chip";
  /**
   * Column alignment
   */
  column?: boolean;
  /**
   * The label title
   */
  required?: boolean;
  /**
   * The tooltip helper text
   */
  tooltipHelper?: string;
  /**
   * Determine if label is visible to user
   */
  isLabelVisible?: boolean;
  /**
   * Determine if radio is in button mode with 40px max-width
   */
  isSmall?: boolean;
  /**
   * The error message
   */
  error?: string;
  /**
   * Single Radio component
   */
  children: React.ReactNode;
};

export type RadioProps = {
  /**
   * The label of the single radio
   */
  label: string;
  /**
   * Variant of the radios
   */
  variant?: "button" | "chip";
  /**
   * Determine if radio is in button mode with 40px max-width
   */
  isSmall?: boolean;
  /**
   * The error message
   */
  error?: string;
  /**
   * The icon before the label
   */
  icon?: IconProps["name"];
} & InputHTMLAttributes<HTMLInputElement>;

export const RadioGroup: React.FC<RadioGroupProps> = ({
  label,
  tooltipHelper,
  isLabelVisible = true,
  variant,
  isSmall,
  error,
  required,
  column,
  children,
}) => {
  return (
    <div className="gx-input-wrapper">
      <label
        className={clsx("gx-label", {
          "gx-sr-only": !isLabelVisible,
        })}
      >
        {label}
        {required && <span className="gx-label__required">*</span>}
        {tooltipHelper && (
          <span className="gx-tip">
            <Tooltip position="top" text={tooltipHelper}>
              <Icon className="gx-icon--info" name="info-circle" />
            </Tooltip>
          </span>
        )}
      </label>
      <div
        className={clsx("gx-radio-wrapper", {
          "gx-radio-wrapper--column": column,
        })}
      >
        {React.Children.map(children, (child) => {
          if (!React.isValidElement(child)) return null;
          return React.cloneElement(child as React.ReactElement<any>, {
            variant: variant,
            isSmall: isSmall,
            error: error,
            ...(typeof child.props === "object" ? child.props : {}),
          });
        })}
      </div>
      {error && <HelperText text={error} style="error" />}
    </div>
  );
};

const RadioComponent: ForwardRefRenderFunction<HTMLInputElement, RadioProps> = (
  { label, icon, error, variant, isSmall, ...props },
  ref
) => {
  return (
    <label
      className={clsx({
        [`gx-radio-${variant}`]: variant,
        "gx-radio": !variant,
        "is-disabled": props.disabled,
        "gx-radio--negative": error,
      })}
    >
      <input
        ref={ref}
        className={clsx({
          "gx-radio-button__nativeControl": variant === "button",
          "gx-radio-chip__nativeControl": variant === "chip",
          "gx-radio__nativeControl": !variant,
        })}
        type="radio"
        {...props}
      />
      {variant && (
        <div
          className={clsx({
            "gx-radio-button__control": variant === "button",
            "gx-radio-button__control--small": variant === "button" && isSmall,
            "gx-radio-chip__control": variant === "chip",
          })}
        >
          {icon && <Icon name={icon} />}
          <span>{label}</span>
        </div>
      )}
      {!variant && (
        <span className="gx-radio__text">
          <>
            <span>{label}</span>
            {icon && <Icon name={icon} />}
          </>
        </span>
      )}
    </label>
  );
};

export const Radio = forwardRef(RadioComponent);
