// -------------------------------
// Radio - Components
// -------------------------------
@use "@gx-design/theme/styles" as *;

$gx-radio: "gx-radio";
$gx-radio-check-size: 2rem;

// Phantom class to add sr-only to custom styled inputs
%radio-check-nativeControl {
  position: absolute;
  clip: rect(0, 0, 0, 0);
  width: 0.1rem;
  height: 0.1rem;
  margin: -0.1rem;
  padding: 0;
}

.#{$gx-radio} {
  position: relative;
  display: inline-flex;
  align-items: center;
  margin-bottom: 0;
  cursor: pointer;

  &-wrapper {
    display: flex;
    gap: space(sm);

    .gx-label + & {
      margin-top: space(xs);
    }

    &--column {
      flex-direction: column;

      .#{$gx-radio} + .#{$gx-radio} {
        margin-left: 0;
        align-self: flex-start;
      }
    }
  }

  &__nativeControl {
    position: absolute;
    appearance: none;
    z-index: 0;
    left: -0.6rem;
    top: -0.4rem;
    width: 3.2rem;
    height: 3.2rem;
    display: block;
    margin: 0;
    border-radius: radius(rounded);
    background-color: rgba(black, 0.8);
    outline: none;
    opacity: 0;
    transform: scale(1);
    pointer-events: none;
    transition: opacity 0.3s, transform 0.2s;
  }

  > span {
    &::before {
      content: "";
      display: inline-block;
      margin: 0.2rem space(sm) 0.2rem 0;
      border: solid 2px color(border-selectable);
      border-radius: radius(rounded);
      width: 2rem;
      height: 2rem;
      vertical-align: top;
      background-color: color(background-main);
      transition: border-color 0.2s;
    }

    &::after {
      content: "";
      display: block;
      position: absolute;
      top: 0.2rem;
      left: 0;
      border-radius: 50%;
      width: 1rem;
      height: 1rem;
      background-color: color(background-brand);
      transform: translate(0.5rem, 0.5rem) scale(0);
      transition: transform 0.2s;
    }
  }

  input {
    &:checked {
      background-color: black;

      & + span::before {
        border-color: color(background-brand);
      }

      & + span::after {
        transform: translate(0.5rem, 0.5rem) scale(1);
      }
    }

    &:focus {
      opacity: 0.1;
    }

    &:active {
      opacity: 1;
      transform: scale(0);
      transition: transform 0s, opacity 0s;

      & + span::before {
        border-color: color(background-brand);
      }
    }
  }

  &:hover {
    > input {
      opacity: 0.1;

      &:focus {
        opacity: 0.1;
      }
    }
  }

  &__text {
    @include typography(body);
    z-index: 1;

    svg {
      @include icon-size(md);
      margin-left: space(xs);
    }
  }

  &-group {
    border-radius: radius(md);
    display: flex;

    &--negative {
      border-color: color(border-error);

      .#{$gx-radio}-button__control {
        border-color: color(border-error);
      }
    }

    &--warning {
      border-color: color(border-warning);

      .#{$gx-radio}-button__control {
        border-color: color(border-warning);
      }
    }
  }

  &.is-disabled {
    cursor: not-allowed;
    opacity: 0.5;

    input {
      display: none;
    }
  }

  &-button {
    margin-bottom: 0;

    .gx-radio-wrapper:has(&) {
      gap: 0;
    }

    &__nativeControl {
      @extend %radio-check-nativeControl;
    }

    &__control {
      align-items: center;
      background-color: color(background-main);
      border: 0.1rem solid color(border-main);
      border-left-width: 0;
      cursor: pointer;
      display: flex;
      gap: space(sm);
      @include typography(body-small);
      height: 4rem;
      justify-content: center;
      padding: 0 space(md);
      overflow: hidden;
      color: color(content-action);

      &--small {
        width: 4rem;
      }

      &:hover {
        background-color: color(background-alt);
      }

      svg {
        @include icon-size(md);
      }
    }

    &:first-of-type {
      .#{$gx-radio}-button__control {
        border-left: 0.1rem solid color(border-main);
        border-top-left-radius: radius(md);
        border-bottom-left-radius: radius(md);
      }
    }

    &:last-of-type {
      .#{$gx-radio}-button__control {
        border-top-right-radius: radius(md);
        border-bottom-right-radius: radius(md);
      }
    }

    input:focus + &__control {
      position: relative;
      @include z-index(base, 1);
    }

    input:checked + &__control {
      background: color(background-selected);
      color: color(content-selected);
      border-color: color(border-selected);
    }
  }

  &-chip {
    min-width: 4.8rem;
    margin-bottom: 0;
    border-radius: radius(md);

    &__nativeControl {
      @extend %radio-check-nativeControl;
    }

    &__control {
      align-items: center;
      background-color: color(background-main);
      border: 0.1rem solid color(border-main);
      color: color(content-selectable);
      cursor: pointer;
      display: flex;
      gap: space(sm);
      @include typography(body-small);
      height: 3.2rem;
      justify-content: center;
      padding: 0 space(sm);
      overflow: hidden;
      border-radius: radius(md);

      &:hover {
        background-color: color(background-alt);
      }

      svg {
        @include icon-size(sm);
      }
    }

    &:first-of-type {
      .#{$gx-radio}-button__control {
        border-left: 0.1rem solid color(border-main);
        border-top-left-radius: radius(md);
        border-bottom-left-radius: radius(md);
      }
    }

    &:last-of-type {
      .#{$gx-radio}-button__control {
        border-top-right-radius: radius(md);
        border-bottom-right-radius: radius(md);
      }
    }

    input:focus + &__control {
      position: relative;
      @include z-index(base, 1);
    }

    input:checked + &__control {
      background: color(background-selected);
      color: color(content-selected);
      border-color: color(border-selected);
    }
  }

  input:focus + .#{$gx-radio}__control {
    border: 0.1rem solid color(border-action);
  }

  &--negative {
    .#{$gx-radio}__control {
      border-color: color(border-error);
    }
  }

  &--warning {
    .#{$gx-radio}__control {
      border-color: color(border-warning);
    }
  }

  input:checked + &__control {
    &::after {
      display: block;
    }
  }

  input:disabled + &__control {
    background-color: color(background-alt);
    cursor: not-allowed;
  }

  input:disabled ~ &__text {
    color: color(content-low);
    cursor: not-allowed;
  }
}
