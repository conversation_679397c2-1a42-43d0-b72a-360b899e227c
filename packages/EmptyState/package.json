{"name": "@gx-design/empty-state", "version": "5.3.1", "description": "Gx Design EmptyState component", "source": "src/EmptyState.tsx", "main": "dist/index.js", "module": "dist/index.mjs", "peerDependencies": {"react": "*"}, "devDependencies": {"tsup": "^7.2.0"}, "dependencies": {"@gx-design/button": "^5.3.13", "@gx-design/theme": "^1.4.0", "clsx": "1.1.1"}, "scripts": {"build": "tsup", "watch": "tsup --watch"}, "repository": {"type": "git", "url": "********************:getrix/gx-design.git", "directory": "packages/EmptyState"}, "typings": "dist/index.d.ts", "volta": {"extends": "../../package.json"}}