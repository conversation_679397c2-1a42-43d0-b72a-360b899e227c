@use "@gx-design/theme/styles" as *;

$gx-empty-state : "gx-empty-state";

.#{$gx-empty-state} {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  width: 100%;
  padding-top: space(2xl);

  &__img {
    width: 6.4rem;
    height: auto;
    margin-bottom: space(md);
  }
  &__title {
    margin-bottom: space(lg);
  }
  &__subTitle {
    margin-bottom: space(lg);
    color: color(content-medium);
  }
  &:has(.#{$gx-empty-state}__subTitle) {
    .#{$gx-empty-state}__title {
      margin-bottom: space(xs);
    }
   
  }
}