import React from "react";

// Props
export interface EmptyStateProps {
  /**
   The image path
  */
  img?: string;
  /**
   The title
  */
  title: string;
  /**
   The description
  */
  description?: string;
  /**
   Determinate if the EmptyState has a children component.
  */
  children?: React.ReactNode;
}

// Component

export const EmptyState: React.FC<EmptyStateProps> = ({
  img,
  title,
  description,
  children,
}) => {
  return (
    <div className="gx-empty-state">
      {img ? (
        <img className="gx-empty-state__img" src={img} />
      ) : (
        <svg
          className="gx-empty-state__img"
          xmlns="http://www.w3.org/2000/svg"
          width="64"
          height="64"
          viewBox="0 0 64 64"
          fill="none"
          role="img"
        >
          <g>
            <path
              d="M59.5121 62.9244C57.6497 64.5493 54.8085 64.3023 53.2406 62.3923L46.76 54.4769L46.4939 54.1538L41.2676 47.7112L40.2129 46.4094L46.9025 40.86L48.2043 42.4089L53.4211 48.766L53.4781 48.8325L60.0252 56.8429C61.5266 58.6769 61.2986 61.3755 59.5121 62.9244Z"
              fill="#0074C1"
            />
            <path
              d="M53.4781 48.8325C51.5112 51.018 49.2496 52.928 46.76 54.4769L46.4939 54.1538L41.2676 47.7112L40.2129 46.4094L46.9025 40.86L48.2043 42.4089L53.4211 48.766L53.4781 48.8325Z"
              fill="#004979"
            />
            <path
              d="M30.7871 53.5741C45.5811 53.5741 57.5741 41.5812 57.5741 26.7871C57.5741 11.993 45.5811 0 30.7871 0C15.993 0 4 11.993 4 26.7871C4 41.5812 15.993 53.5741 30.7871 53.5741Z"
              fill="#0074C1"
            />
            <path
              d="M30.7488 47.7492C42.3417 47.7492 51.749 38.3419 51.749 26.749C51.749 15.1562 42.3322 5.7489 30.7488 5.7489C19.1655 5.7489 9.74869 15.1657 9.74869 26.749C9.74869 38.3324 19.156 47.7492 30.7488 47.7492Z"
              fill="#E5F6FE"
            />
            <path
              d="M30.7487 46.0388C41.3913 46.0388 50.0384 37.3917 50.0384 26.7491C50.0384 16.1065 41.3913 7.45934 30.7487 7.45934C20.1061 7.45934 11.459 16.1065 11.459 26.7491C11.459 37.3917 20.1061 46.0388 30.7487 46.0388Z"
              fill="#C6E8F8"
            />
            <path
              fillRule="evenodd"
              clipRule="evenodd"
              d="M18.6049 36.3749H42.9689V24.3545H18.6049V36.3749Z"
              fill="#F1D2B3"
            />
            <path
              fillRule="evenodd"
              clipRule="evenodd"
              d="M17.5024 24.3544H44.0709V22.7581H17.5024V24.3544Z"
              fill="#8A000D"
            />
            <path
              fillRule="evenodd"
              clipRule="evenodd"
              d="M18.6049 25.7988H42.9689V24.3545H18.6049V25.7988Z"
              fill="#D9BEA2"
            />
            <path
              fillRule="evenodd"
              clipRule="evenodd"
              d="M32.8582 34.921H39.2057V33.4767H32.8582V34.921Z"
              fill="#D9BEA2"
            />
            <path
              fillRule="evenodd"
              clipRule="evenodd"
              d="M40.9635 17.1327H20.6096L17.5023 22.7581H44.0708L40.9635 17.1327Z"
              fill="#C40011"
            />
            <path
              d="M28.7816 27.1291H22.6335V36.3749H28.7816V27.1291Z"
              fill="white"
            />
            <path
              d="M27.5463 28.3549H23.8594V36.3654H27.5463V28.3549Z"
              fill="#5A3820"
            />
            <path
              d="M39.2151 27.1291H32.8675V33.4767H39.2151V27.1291Z"
              fill="white"
            />
            <path
              d="M37.9419 28.4025H34.1315V32.2129H37.9419V28.4025Z"
              fill="#5A3820"
            />
            <path
              d="M23.8594 29.5902V36.3749H25.0947V29.5902H27.5463V28.3549H23.8594V29.5902Z"
              fill="#3E2818"
            />
            <path
              d="M37.9419 28.4025H35.4048H34.1315V29.6663V32.2129H35.4048V29.6663H37.9419V28.4025Z"
              fill="#3E2818"
            />
          </g>
          <defs>
            <clipPath>
              <rect width="64" height="64" rx="5.22449" fill="white" />
            </clipPath>
          </defs>
        </svg>
      )}
      <div className="gx-empty-state__title gx-title-2">{title}</div>
      {description && (
        <div className="gx-empty-state__subTitle gx-body-small">
          {description}
        </div>
      )}
      {children && children}
    </div>
  );
};
