import React from "react";
import { render, screen } from "../../../test/utilities";
import { EmptyState } from "./EmptyState";
import { it, expect } from "vitest";
import "@testing-library/jest-dom";
import { Button } from "../../Button";

it("should render the EmptyState component with default props", () => {
  render(<EmptyState title="No matching results found" />);
  expect(screen.getByText("No matching results found")).toBeInTheDocument();
  expect(screen.getByRole("img")).toBeInTheDocument();
});

it("should render the EmptyState with description", () => {
  render(
    <EmptyState
      title="No matching results found"
      description="Try to change filters"
    />
  );
  expect(screen.getByText("No matching results found")).toBeInTheDocument();
  expect(screen.getByText("Try to change filters")).toBeInTheDocument();
});

it("should render the EmptyState with children", () => {
  render(
    <EmptyState title="No matching results found">
      <Button variant="accent">Try to change filters</Button>
    </EmptyState>
  );
  expect(screen.getByText("No matching results found")).toBeInTheDocument();
  expect(screen.getByText("Try to change filters")).toBeInTheDocument();
});
