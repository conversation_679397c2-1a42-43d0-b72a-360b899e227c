# Change Log

All notable changes to this project will be documented in this file.
See [Conventional Commits](https://conventionalcommits.org) for commit guidelines.

## [1.1.1](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/character-counter@1.1.0...@gx-design/character-counter@1.1.1) (2025-08-01)

### Bug Fixes

- **character-counter:** optional maxLength prop ([1de33b2](https://gitlab.pepita.io/getrix/gx-design/commit/1de33b25d3080258e1ea699cdb518a96037dcc7c))

# [1.1.0](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/character-counter@1.0.1...@gx-design/character-counter@1.1.0) (2025-07-04)

### Features

- **character-counter:** added component, tests, updated stories and package ([f243761](https://gitlab.pepita.io/getrix/gx-design/commit/f24376164c6597a01ce73acf6f181b6f2cd830da))

## 1.0.1 (2024-03-18)

**Note:** Version bump only for package @gx-design/character-counter
