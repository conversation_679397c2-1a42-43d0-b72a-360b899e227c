import clsx from "clsx";
import React from "react";

export type CharacterCounterProps = {
  /**
   Current character count
  */
  characterCount: number;
  /**
   Maximum allowed characters
  */
  maxLength?: number;
  /**
   The CharacterCounter variant
  */
  variant?: "success" | "error";
};

export const CharacterCounter: React.FC<CharacterCounterProps> = ({
  characterCount,
  maxLength,
  variant,
}) => {
  return (
    <div
      className={clsx(
        "gx-character-counter",
        variant && `gx-character-counter--${variant}`
      )}
    >
      <span className="gx-character-counter__count">{characterCount}</span>
      {maxLength && (
        <>
          {" / "}
          <span className="gx-character-counter__max">{maxLength}</span>
        </>
      )}
    </div>
  );
};
