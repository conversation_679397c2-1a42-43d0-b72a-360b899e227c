import React from "react";
import { render, screen } from "../../../test/utilities";
import { CharacterCounter, CharacterCounterProps } from "./CharacterCounter";
import { it, expect } from "vitest";

const variants: CharacterCounterProps["variant"][] = ["success", "error"];

it("renders the Counter without maxLength or variant", () => {
  render(<CharacterCounter characterCount={50} />);
  const characterCounter = screen.getByText("50");
  expect(characterCounter).toBeInTheDocument();
  expect(characterCounter.parentElement).not.toHaveClass(
    "gx-character-counter--success"
  );
  expect(characterCounter.parentElement).not.toHaveClass(
    "gx-character-counter--error"
  );
});

variants?.forEach((variant) => {
  it(`renders the Counter with the correct style class for ${variant}`, () => {
    render(
      <CharacterCounter characterCount={50} maxLength={100} variant={variant} />
    );
    const characterCounter = screen.getByText("50");
    expect(characterCounter.parentElement).toHaveClass(
      `gx-character-counter--${variant}`
    );
  });
});
