{"name": "@gx-design/character-counter", "version": "1.1.1", "description": "Gx Design Character Counter component", "source": "src/CharacterCounter.tsx", "main": "dist/index.js", "module": "dist/index.mjs", "peerDependencies": {"react": "*"}, "devDependencies": {"tsup": "^7.2.0"}, "dependencies": {"clsx": "1.1.1"}, "scripts": {"build": "tsup", "watch": "tsup --watch"}, "repository": {"type": "git", "url": "********************:getrix/gx-design.git", "directory": "packages/CharacterCounter"}, "typings": "dist/index.d.ts", "volta": {"extends": "../../package.json"}}