{"name": "@gx-design/illustration", "version": "4.0.2", "description": "Gx Design Illustration component", "source": "src/Illustration.tsx", "main": "dist/index.js", "module": "dist/index.mjs", "peerDependencies": {"react": "*"}, "devDependencies": {"tsup": "^7.2.0"}, "dependencies": {"@gx-design/illustration-set": "^3.1.2", "clsx": "1.1.1"}, "scripts": {"build": "tsup", "watch": "tsup --watch"}, "repository": {"type": "git", "url": "********************:getrix/gx-design.git", "directory": "packages/Illustration"}, "typings": "dist/index.d.ts", "volta": {"extends": "../../package.json"}}