import React from "react";
import clsx from "clsx";
import { illustrationSet } from "@gx-design/illustration-set";

// Props
export interface IllustrationProps {
  /**
   The illustration name, picked from the library
  */
  name: keyof typeof illustrationSet;
  /**
   The illustration width
  */
  width?: string;
  /**
   The illustration height
  */
  height?: string;
  /**
   The viewbox size
  */
  viewBox?: string;
  /**
   Custom class name
  */
  className?: string;
}

// Component
export const Illustration: React.FC<IllustrationProps> = React.forwardRef<
  SVGSVGElement,
  IllustrationProps
>(
  (
    { name, className, width = "16", height = "16", viewBox = "0 0 16 16" },
    ref
  ) => {
    if (!illustrationSet[name]) {
      //eslint-disable-next-line no-console
      console.warn(
        "Error: Illustration name not provided or not found in the set."
      );
      return null;
    }

    return (
      <svg
        ref={ref}
        xmlns="http://www.w3.org/2000/svg"
        width={width}
        height={height}
        viewBox={viewBox}
        className={clsx("gx-icon gx-icon--illustration", className)}
        data-testid={"gx-icon--illustration"}
      >
        <path fillRule="evenodd" d={illustrationSet[name]} />
      </svg>
    );
  }
);
