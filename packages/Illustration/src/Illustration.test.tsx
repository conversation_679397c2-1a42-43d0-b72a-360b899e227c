import React from "react";
import { render, screen } from "../../../test/utilities";
import { Illustration } from "./Illustration";
import { it, expect } from "vitest";

it("renders the illustration with default props", () => {
  render(<Illustration name="apartment" />);
  const svgElement = screen.getByTestId("gx-icon--illustration");
  expect(svgElement).toHaveClass("gx-icon--illustration");
  expect(svgElement).toBeInTheDocument();
  expect(svgElement).toHaveAttribute("width", "16");
  expect(svgElement).toHaveAttribute("height", "16");
  expect(svgElement).toHaveAttribute("viewBox", "0 0 16 16");
});

it("renders the illustration with custom width and height", () => {
  render(<Illustration name="apartment" width="32" height="32" />);
  const svgElement = screen.getByTestId("gx-icon--illustration");
  expect(svgElement).toHaveAttribute("width", "32");
  expect(svgElement).toHaveAttribute("height", "32");
});

it("renders the illustration with custom viewBox", () => {
  render(<Illustration name="apartment" viewBox="0 0 32 32" />);
  const svgElement = screen.getByTestId("gx-icon--illustration");
  expect(svgElement).toHaveAttribute("viewBox", "0 0 32 32");
});

it("applies custom className", () => {
  render(<Illustration name="apartment" className="custom-class" />);
  const svgElement = screen.getByTestId("gx-icon--illustration");
  expect(svgElement).toHaveClass("custom-class");
});

it("checks if svg renders if the provided name does not exist in the illustration set", () => {
  render(<Illustration name="nonExistentIllustration" />);
  const svgElement = document.querySelector("gx-icon--illustration");
  expect(svgElement).not.toBeInTheDocument();
});
