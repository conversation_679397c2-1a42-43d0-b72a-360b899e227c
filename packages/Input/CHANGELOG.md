# Change Log

All notable changes to this project will be documented in this file.
See [Conventional Commits](https://conventionalcommits.org) for commit guidelines.

## [5.3.15](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/input@5.3.14...@gx-design/input@5.3.15) (2025-07-24)

**Note:** Version bump only for package @gx-design/input

## [5.3.14](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/input@5.3.13...@gx-design/input@5.3.14) (2025-07-17)

**Note:** Version bump only for package @gx-design/input

## [5.3.13](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/input@5.3.12...@gx-design/input@5.3.13) (2025-07-09)

**Note:** Version bump only for package @gx-design/input

## [5.3.12](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/input@5.3.11...@gx-design/input@5.3.12) (2025-07-08)

**Note:** Version bump only for package @gx-design/input

## [5.3.11](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/input@5.3.10...@gx-design/input@5.3.11) (2025-07-07)

**Note:** Version bump only for package @gx-design/input

## [5.3.10](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/input@5.3.9...@gx-design/input@5.3.10) (2025-07-04)

**Note:** Version bump only for package @gx-design/input

## [5.3.9](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/input@5.3.8...@gx-design/input@5.3.9) (2025-06-24)

### Bug Fixes

- **fontSizeInput:** fixed fontsize of inputs on desktop ([944a1ee](https://gitlab.pepita.io/getrix/gx-design/commit/944a1eee48e18dc8dd2aa01c781ecc69152197de))

## [5.3.8](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/input@5.3.7...@gx-design/input@5.3.8) (2025-06-09)

**Note:** Version bump only for package @gx-design/input

## [5.3.7](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/input@5.3.6...@gx-design/input@5.3.7) (2025-05-30)

**Note:** Version bump only for package @gx-design/input

## [5.3.6](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/input@5.3.5...@gx-design/input@5.3.6) (2025-05-23)

**Note:** Version bump only for package @gx-design/input

## [5.3.5](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/input@5.3.4...@gx-design/input@5.3.5) (2025-05-16)

**Note:** Version bump only for package @gx-design/input

## [5.3.4](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/input@5.3.3...@gx-design/input@5.3.4) (2025-05-14)

**Note:** Version bump only for package @gx-design/input

## [5.3.3](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/input@5.3.2...@gx-design/input@5.3.3) (2025-04-16)

**Note:** Version bump only for package @gx-design/input

## [5.3.2](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/input@5.3.1...@gx-design/input@5.3.2) (2025-04-02)

**Note:** Version bump only for package @gx-design/input

## [5.3.1](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/input@5.3.0...@gx-design/input@5.3.1) (2025-02-14)

**Note:** Version bump only for package @gx-design/input

# [5.3.0](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/input@5.2.2...@gx-design/input@5.3.0) (2025-02-11)

### Features

- **label:** new component ([e773c54](https://gitlab.pepita.io/getrix/gx-design/commit/e773c549d3af67dd97d96f9a71d3a4db3d8a151f))

## [5.2.2](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/input@5.2.1...@gx-design/input@5.2.2) (2024-10-31)

**Note:** Version bump only for package @gx-design/input

## [5.2.1](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/input@5.2.0...@gx-design/input@5.2.1) (2024-10-02)

### Bug Fixes

- **form:** adeguato border-radius elementi form ([b73ca22](https://gitlab.pepita.io/getrix/gx-design/commit/b73ca22af26794957bff14e2e2ad1aa5dda2d32e))

# [5.2.0](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/input@5.1.0...@gx-design/input@5.2.0) (2024-09-23)

### Features

- **css:** custom properties ([cbc56c0](https://gitlab.pepita.io/getrix/gx-design/commit/cbc56c0e3e675d85f92ad25ac135cce270f02b2b))

## [5.1.1-alpha.0](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/input@5.1.0...@gx-design/input@5.1.1-alpha.0) (2024-09-16)

**Note:** Version bump only for package @gx-design/input

# [5.1.0](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/input@5.0.9...@gx-design/input@5.1.0) (2024-08-29)

### Features

- **colors:** pro new colors ([2103ea7](https://gitlab.pepita.io/getrix/gx-design/commit/2103ea76597b5ae75018d2d62947c6b01d613e82))

# [5.1.0-alpha.0](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/input@5.0.8...@gx-design/input@5.1.0-alpha.0) (2024-07-26)

### Features

- **colors:** pro new colors ([d88639f](https://gitlab.pepita.io/getrix/gx-design/commit/d88639f983895257b905dfad2fb65db3e75efee0))

## [5.0.9-alpha.0](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/input@5.0.8...@gx-design/input@5.0.9-alpha.0) (2024-07-22)

**Note:** Version bump only for package @gx-design/input

## [5.0.8](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/input@5.0.7...@gx-design/input@5.0.8) (2024-07-15)

**Note:** Version bump only for package @gx-design/input

## [5.0.7](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/input@5.0.6...@gx-design/input@5.0.7) (2024-06-14)

**Note:** Version bump only for package @gx-design/input

## [5.0.6](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/input@5.0.5...@gx-design/input@5.0.6) (2024-06-05)

**Note:** Version bump only for package @gx-design/input

## [5.0.5](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/input@5.0.4...@gx-design/input@5.0.5) (2024-06-03)

**Note:** Version bump only for package @gx-design/input

## [5.0.4](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/input@5.0.3...@gx-design/input@5.0.4) (2024-05-28)

**Note:** Version bump only for package @gx-design/input

## [5.0.3](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/input@5.0.2...@gx-design/input@5.0.3) (2024-05-16)

**Note:** Version bump only for package @gx-design/input

## [5.0.2](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/input@5.0.1...@gx-design/input@5.0.2) (2024-05-06)

**Note:** Version bump only for package @gx-design/input

## [5.0.1](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/input@5.0.0...@gx-design/input@5.0.1) (2024-04-18)

### Bug Fixes

- **out-animation:** on exit handler ([c0c71d7](https://gitlab.pepita.io/getrix/gx-design/commit/c0c71d75cf5becac1278fa97aae9eec11000de5d))

## [5.0.1-alpha.1](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/input@5.0.1-alpha.0...@gx-design/input@5.0.1-alpha.1) (2024-04-17)

**Note:** Version bump only for package @gx-design/input

## [5.0.1-alpha.0](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/input@5.0.0...@gx-design/input@5.0.1-alpha.0) (2024-04-16)

**Note:** Version bump only for package @gx-design/input

# [5.0.0](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/input@4.0.2...@gx-design/input@5.0.0) (2024-03-26)

### Features

- **button:** style and variant changes ([1fbe519](https://gitlab.pepita.io/getrix/gx-design/commit/1fbe519808701c8a752b6ec8b36fff8a0bd82a9b))

### BREAKING CHANGES

- **button:** button style to variant

## [4.0.2-alpha.0](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/input@4.0.1...@gx-design/input@4.0.2-alpha.0) (2024-03-25)

**Note:** Version bump only for package @gx-design/input

## [4.0.1](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/input@4.0.0...@gx-design/input@4.0.1) (2024-03-20)

**Note:** Version bump only for package @gx-design/input

# [4.0.0](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/input@3.4.0...@gx-design/input@4.0.0) (2024-03-18)

- feat!: new architecture ([5572bfc](https://gitlab.pepita.io/getrix/gx-design/commit/5572bfc9c05f1cb4aa5a0e134d2771ac5d7243f0))

### BREAKING CHANGES

- new architecture

# [3.4.0](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/input@3.3.7...@gx-design/input@3.4.0) (2024-03-18)

**Note:** Version bump only for package @gx-design/input

# [3.4.0-alpha.4](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/input@3.4.0-alpha.3...@gx-design/input@3.4.0-alpha.4) (2024-03-11)

**Note:** Version bump only for package @gx-design/input

# [3.4.0-alpha.3](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/input@3.4.0-alpha.2...@gx-design/input@3.4.0-alpha.3) (2024-03-11)

**Note:** Version bump only for package @gx-design/input

# [3.4.0-alpha.2](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/input@3.4.0-alpha.1...@gx-design/input@3.4.0-alpha.2) (2024-03-11)

**Note:** Version bump only for package @gx-design/input

# [3.4.0-alpha.1](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/input@3.4.0-alpha.0...@gx-design/input@3.4.0-alpha.1) (2024-03-08)

**Note:** Version bump only for package @gx-design/input

# [3.4.0-alpha.0](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/input@3.3.6...@gx-design/input@3.4.0-alpha.0) (2024-03-07)

### Features

- **scss:** alert and theme styles ([7c0102d](https://gitlab.pepita.io/getrix/gx-design/commit/7c0102d309fce10724944b1e2e355d391d84ebe7))

## [3.3.7-alpha.1](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/input@3.3.7-alpha.0...@gx-design/input@3.3.7-alpha.1) (2024-02-29)

**Note:** Version bump only for package @gx-design/input

## [3.3.7-alpha.0](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/input@3.3.6...@gx-design/input@3.3.7-alpha.0) (2024-02-28)

**Note:** Version bump only for package @gx-design/input

## [3.3.6](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/input@3.3.5...@gx-design/input@3.3.6) (2024-02-27)

**Note:** Version bump only for package @gx-design/input

## [3.3.5](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/input@3.3.4...@gx-design/input@3.3.5) (2024-01-31)

**Note:** Version bump only for package @gx-design/input

## [3.3.4](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/input@3.3.3...@gx-design/input@3.3.4) (2024-01-12)

**Note:** Version bump only for package @gx-design/input

## [3.3.3](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/input@3.3.2...@gx-design/input@3.3.3) (2023-12-20)

**Note:** Version bump only for package @gx-design/input

## [3.3.2](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/input@3.3.1...@gx-design/input@3.3.2) (2023-12-15)

**Note:** Version bump only for package @gx-design/input

## [3.3.1](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/input@3.3.0...@gx-design/input@3.3.1) (2023-12-01)

**Note:** Version bump only for package @gx-design/input

# [3.3.0](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/input@3.2.3...@gx-design/input@3.3.0) (2023-11-27)

**Note:** Version bump only for package @gx-design/input

# [3.3.0-alpha.0](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/input@3.2.3...@gx-design/input@3.3.0-alpha.0) (2023-11-27)

### Features

- **input:** add tests ([fe01c56](https://gitlab.pepita.io/getrix/gx-design/commit/fe01c5695238f722be90048f2039adea606bcba3))

## [3.2.3](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/input@3.2.2...@gx-design/input@3.2.3) (2023-11-06)

**Note:** Version bump only for package @gx-design/input

## [3.2.2](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/input@3.2.1...@gx-design/input@3.2.2) (2023-10-27)

**Note:** Version bump only for package @gx-design/input

## [3.2.1](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/input@3.2.0...@gx-design/input@3.2.1) (2023-10-23)

**Note:** Version bump only for package @gx-design/input

# [3.2.0](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/input@3.1.12...@gx-design/input@3.2.0) (2023-09-27)

### Features

- **storybook:** upgrade to 7 ([1c0e5e9](https://gitlab.pepita.io/getrix/gx-design/commit/1c0e5e941dcf7b841d1b5d2a2825f66f7921276e))

## [3.1.12](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/input@3.1.11...@gx-design/input@3.1.12) (2023-09-08)

**Note:** Version bump only for package @gx-design/input

## [3.1.11](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/input@3.1.10...@gx-design/input@3.1.11) (2023-08-28)

**Note:** Version bump only for package @gx-design/input

## [3.1.10](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/input@3.1.9...@gx-design/input@3.1.10) (2023-08-23)

**Note:** Version bump only for package @gx-design/input

## [3.1.9](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/input@3.1.8...@gx-design/input@3.1.9) (2023-07-27)

**Note:** Version bump only for package @gx-design/input

## [3.1.8](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/input@3.1.7...@gx-design/input@3.1.8) (2023-07-26)

**Note:** Version bump only for package @gx-design/input

## [3.1.7](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/input@3.1.6...@gx-design/input@3.1.7) (2023-07-17)

**Note:** Version bump only for package @gx-design/input

## [3.1.7-alpha.1](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/input@3.1.7-alpha.0...@gx-design/input@3.1.7-alpha.1) (2023-07-12)

**Note:** Version bump only for package @gx-design/input

## [3.1.7-alpha.0](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/input@3.1.6...@gx-design/input@3.1.7-alpha.0) (2023-07-12)

**Note:** Version bump only for package @gx-design/input

## [3.1.6](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/input@3.1.5...@gx-design/input@3.1.6) (2023-06-22)

**Note:** Version bump only for package @gx-design/input

## [3.1.5](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/input@3.1.4...@gx-design/input@3.1.5) (2023-06-19)

**Note:** Version bump only for package @gx-design/input

## [3.1.4](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/input@3.1.3...@gx-design/input@3.1.4) (2023-05-30)

**Note:** Version bump only for package @gx-design/input

## [3.1.3](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/input@3.1.2...@gx-design/input@3.1.3) (2023-05-16)

**Note:** Version bump only for package @gx-design/input

## [3.1.2](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/input@3.1.1...@gx-design/input@3.1.2) (2023-05-15)

**Note:** Version bump only for package @gx-design/input

## [3.1.1](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/input@3.1.0...@gx-design/input@3.1.1) (2023-05-15)

**Note:** Version bump only for package @gx-design/input

# [3.1.0](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/input@3.0.2...@gx-design/input@3.1.0) (2023-05-11)

### Features

- **icon:** update icon-set e fix componenti ([7154ee0](https://gitlab.pepita.io/getrix/gx-design/commit/7154ee0675a224385d543e7d17123692c1bc499a))

## [3.0.2](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/input@3.0.1...@gx-design/input@3.0.2) (2023-05-10)

**Note:** Version bump only for package @gx-design/input

## [3.0.2-alpha.0](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/input@3.0.1...@gx-design/input@3.0.2-alpha.0) (2023-05-10)

**Note:** Version bump only for package @gx-design/input

## [3.0.1](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/input@3.0.0...@gx-design/input@3.0.1) (2023-05-03)

**Note:** Version bump only for package @gx-design/input

# [3.0.0](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/input@2.0.14...@gx-design/input@3.0.0) (2023-04-13)

### Features

- **icons:** nuove icone ([f444215](https://gitlab.pepita.io/getrix/gx-design/commit/f44421598355b8d9de903a81f2ca6232887f780c))

### BREAKING CHANGES

- **icons:** changed icon sprite

# [3.0.0-alpha.0](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/input@2.0.14...@gx-design/input@3.0.0-alpha.0) (2023-04-05)

### Features

- **icons:** nuove icone ([6528540](https://gitlab.pepita.io/getrix/gx-design/commit/65285405393efb34dfdcc4329d99dd1ef41d70c1))

### BREAKING CHANGES

- **icons:** changed icon sprite

## [2.0.15-alpha.1](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/input@2.0.14...@gx-design/input@2.0.15-alpha.1) (2023-03-29)

**Note:** Version bump only for package @gx-design/input

## [2.0.15-alpha.0](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/input@2.0.14...@gx-design/input@2.0.15-alpha.0) (2023-03-29)

**Note:** Version bump only for package @gx-design/input

## [2.0.14](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/input@2.0.13...@gx-design/input@2.0.14) (2023-03-20)

**Note:** Version bump only for package @gx-design/input

## [2.0.13](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/input@2.0.12...@gx-design/input@2.0.13) (2023-03-07)

**Note:** Version bump only for package @gx-design/input

## [2.0.12](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/input@2.0.11...@gx-design/input@2.0.12) (2023-03-06)

**Note:** Version bump only for package @gx-design/input

## [2.0.11](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/input@2.0.10...@gx-design/input@2.0.11) (2023-02-27)

**Note:** Version bump only for package @gx-design/input

## [2.0.10](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/input@2.0.9...@gx-design/input@2.0.10) (2023-02-21)

**Note:** Version bump only for package @gx-design/input

## [2.0.9](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/input@2.0.8...@gx-design/input@2.0.9) (2023-01-30)

**Note:** Version bump only for package @gx-design/input

## [2.0.8](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/input@2.0.8-alpha.0...@gx-design/input@2.0.8) (2023-01-23)

**Note:** Version bump only for package @gx-design/input

## [2.0.8-alpha.0](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/input@2.0.7...@gx-design/input@2.0.8-alpha.0) (2023-01-23)

**Note:** Version bump only for package @gx-design/input

## [2.0.7](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/input@2.0.6...@gx-design/input@2.0.7) (2022-12-21)

**Note:** Version bump only for package @gx-design/input

## [2.0.6](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/input@2.0.5...@gx-design/input@2.0.6) (2022-11-22)

**Note:** Version bump only for package @gx-design/input

## [2.0.6-alpha.1](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/input@2.0.6-alpha.0...@gx-design/input@2.0.6-alpha.1) (2022-11-21)

**Note:** Version bump only for package @gx-design/input

## [2.0.6-alpha.0](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/input@2.0.5...@gx-design/input@2.0.6-alpha.0) (2022-11-21)

**Note:** Version bump only for package @gx-design/input

## [2.0.5](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/input@2.0.5-alpha.0...@gx-design/input@2.0.5) (2022-10-12)

### Bug Fixes

- **notification:** fix notify ([213000c](https://gitlab.pepita.io/getrix/gx-design/commit/213000c8bc11b83eb5d9ff401e51d57d76411548))

## [2.0.5-alpha.3](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/input@2.0.5-alpha.2...@gx-design/input@2.0.5-alpha.3) (2022-10-12)

**Note:** Version bump only for package @gx-design/input

## [2.0.5-alpha.2](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/input@2.0.5-alpha.0...@gx-design/input@2.0.5-alpha.2) (2022-10-12)

### Bug Fixes

- **notification:** fix notify ([789498a](https://gitlab.pepita.io/getrix/gx-design/commit/789498ae821b217be4d078c71bd053221e529648))
- **notification:** fix notify ([766047c](https://gitlab.pepita.io/getrix/gx-design/commit/766047c45b4ebff6e9aa8985fc926e76a0fd59c0))
- **notification:** fix notify ([5d949cd](https://gitlab.pepita.io/getrix/gx-design/commit/5d949cd42a3e78c3c63ba78aa1d1f4595e6e3397))

## [2.0.5-alpha.0](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/input@2.0.4...@gx-design/input@2.0.5-alpha.0) (2022-10-10)

**Note:** Version bump only for package @gx-design/input

## [2.0.4](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/input@2.0.3...@gx-design/input@2.0.4) (2022-09-19)

**Note:** Version bump only for package @gx-design/input

## [2.0.3](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/input@2.0.2...@gx-design/input@2.0.3) (2022-09-07)

**Note:** Version bump only for package @gx-design/input

## [2.0.2](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/input@2.0.1...@gx-design/input@2.0.2) (2022-08-24)

### Bug Fixes

- **ci:** pipeline ([4844b50](https://gitlab.pepita.io/getrix/gx-design/commit/4844b50e391c8e3f05e63e63cc7059c890974a04))

## [2.0.1](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/input@2.0.1-alpha.4...@gx-design/input@2.0.1) (2022-08-24)

**Note:** Version bump only for package @gx-design/input

## [2.0.1-alpha.6](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/input@2.0.1-alpha.5...@gx-design/input@2.0.1-alpha.6) (2022-08-22)

**Note:** Version bump only for package @gx-design/input

## [2.0.1-alpha.5](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/input@2.0.1-alpha.4...@gx-design/input@2.0.1-alpha.5) (2022-08-22)

**Note:** Version bump only for package @gx-design/input

## [2.0.1-alpha.4](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/input@2.0.1-alpha.3...@gx-design/input@2.0.1-alpha.4) (2022-08-02)

### Bug Fixes

- **release:** pepita-command ([759db81](https://gitlab.pepita.io/getrix/gx-design/commit/759db814e4a8b790526eaaba5633cbfeee6fddc4))
- **release:** pepita-command ([0c2903a](https://gitlab.pepita.io/getrix/gx-design/commit/0c2903a346dad3f793e3091c741d415e1d02fb00))

## [2.0.1-alpha.3](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/input@2.0.1-alpha.2...@gx-design/input@2.0.1-alpha.3) (2022-08-02)

### Bug Fixes

- **release:** pepita-command ([6654ba2](https://gitlab.pepita.io/getrix/gx-design/commit/6654ba27f362a439bdcf991079596ccc8a4264f5))

## [2.0.1-alpha.2](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/input@2.0.1-alpha.1...@gx-design/input@2.0.1-alpha.2) (2022-08-01)

**Note:** Version bump only for package @gx-design/input

## [2.0.1-alpha.1](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/input@2.0.1-alpha.0...@gx-design/input@2.0.1-alpha.1) (2022-08-01)

**Note:** Version bump only for package @gx-design/input

## [2.0.1-alpha.0](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/input@2.0.0...@gx-design/input@2.0.1-alpha.0) (2022-07-27)

### Bug Fixes

- **ci:** ci fix ([2077d96](https://gitlab.pepita.io/getrix/gx-design/commit/2077d964c372bf1e517b9431716db357119ebfc8))

# 2.0.0 (2022-07-18)

# 2.0.0-alpha.15 (2022-07-12)

# 2.0.0-alpha.14 (2022-07-08)

# 2.0.0-alpha.13 (2022-07-06)

# 2.0.0-alpha.12 (2022-07-06)

**Note:** Version bump only for package @gx-design/input

# [2.0.0-alpha.17](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/input@2.0.0-alpha.16...@gx-design/input@2.0.0-alpha.17) (2022-07-18)

**Note:** Version bump only for package @gx-design/input

# 2.0.0-alpha.16 (2022-07-15)

# 2.0.0-alpha.15 (2022-07-12)

# 2.0.0-alpha.14 (2022-07-08)

# 2.0.0-alpha.13 (2022-07-06)

# 2.0.0-alpha.12 (2022-07-06)

**Note:** Version bump only for package @gx-design/input

# [2.0.0-alpha.15](https://gitlab.pepita.io/getrix/gx-design/compare/v2.0.0-alpha.14...v2.0.0-alpha.15) (2022-07-12)

**Note:** Version bump only for package @gx-design/input

# [2.0.0-alpha.14](https://gitlab.pepita.io/getrix/gx-design/compare/v2.0.0-alpha.13...v2.0.0-alpha.14) (2022-07-08)

**Note:** Version bump only for package @gx-design/input

# [2.0.0-alpha.13](https://gitlab.pepita.io/getrix/gx-design/compare/v2.0.0-alpha.12...v2.0.0-alpha.13) (2022-07-06)

**Note:** Version bump only for package @gx-design/input

# [2.0.0-alpha.12](https://gitlab.pepita.io/getrix/gx-design/compare/v1.2.3...v2.0.0-alpha.12) (2022-07-06)

**Note:** Version bump only for package @gx-design/input

# [2.0.0-alpha.11](https://gitlab.pepita.io/getrix/gx-design/compare/v2.0.0-alpha.10...v2.0.0-alpha.11) (2022-06-23)

**Note:** Version bump only for package @gx-design/alert

# [2.0.0-alpha.10](https://gitlab.pepita.io/getrix/gx-design/compare/v2.0.0-alpha.9...v2.0.0-alpha.10) (2022-06-23)

**Note:** Version bump only for package @gx-design/alert

# [2.0.0-alpha.8](https://gitlab.pepita.io/getrix/gx-design/compare/v2.0.0-alpha.7...v2.0.0-alpha.8) (2022-06-22)

**Note:** Version bump only for package @gx-design/alert

# [2.0.0-alpha.7](https://gitlab.pepita.io/getrix/gx-design/compare/v2.0.0-alpha.6...v2.0.0-alpha.7) (2022-06-22)

**Note:** Version bump only for package @gx-design/alert

# [2.0.0-alpha.6](https://gitlab.pepita.io/getrix/gx-design/compare/v2.0.0-alpha.5...v2.0.0-alpha.6) (2022-06-22)

**Note:** Version bump only for package @gx-design/alert

# [2.0.0-alpha.5](https://gitlab.pepita.io/getrix/gx-design/compare/v1.2.3...v2.0.0-alpha.5) (2022-06-22)

### Features

- **alert:** alert component ([1d253e9](https://gitlab.pepita.io/getrix/gx-design/commit/1d253e9f32b7d5a398e0086748c3ad2bdbde45f3))
- **badge:** badge as package ([a359640](https://gitlab.pepita.io/getrix/gx-design/commit/a359640c26feab8638cfe04b905745d24c01c798))
- lerna ([d2acfcb](https://gitlab.pepita.io/getrix/gx-design/commit/d2acfcb44f9ffb8b0044362be8d367721ae19dee))

### BREAKING CHANGES

- adds lerna repo management

# [2.0.0-alpha.4](https://gitlab.pepita.io/getrix/gx-design/compare/v1.2.3...v2.0.0-alpha.4) (2022-06-15)

### Features

- **alert:** alert component ([1d253e9](https://gitlab.pepita.io/getrix/gx-design/commit/1d253e9f32b7d5a398e0086748c3ad2bdbde45f3))
- lerna ([29b12e6](https://gitlab.pepita.io/getrix/gx-design/commit/29b12e6e7d3f245d410915de3ceb7f90248f21b4))

### BREAKING CHANGES

- adds lerna repo management

# [2.0.0-alpha.3](https://gitlab.pepita.io/getrix/gx-design/compare/v2.0.0-alpha.2...v2.0.0-alpha.3) (2022-06-15)

**Note:** Version bump only for package @gx-design/alert

# [2.0.0-alpha.2](https://gitlab.pepita.io/getrix/gx-design/compare/v2.0.0-alpha.1...v2.0.0-alpha.2) (2022-06-15)

**Note:** Version bump only for package @gx-design/alert

# [2.0.0-alpha.1](https://gitlab.pepita.io/getrix/gx-design/compare/v2.0.0-alpha.0...v2.0.0-alpha.1) (2022-06-15)

**Note:** Version bump only for package @gx-design/alert

# [2.0.0-alpha.0](https://gitlab.pepita.io/getrix/gx-design/compare/v1.2.3...v2.0.0-alpha.0) (2022-06-15)

### Features

- **alert:** alert component ([1d253e9](https://gitlab.pepita.io/getrix/gx-design/commit/1d253e9f32b7d5a398e0086748c3ad2bdbde45f3))
- lerna ([6612f21](https://gitlab.pepita.io/getrix/gx-design/commit/6612f2107c71b52281b47052b3e994939cc0efe7))

### BREAKING CHANGES

- adds lerna repo management
