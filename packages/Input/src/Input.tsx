import React, {
  InputHTMLAttributes,
  forwardRef,
  ForwardRefRenderFunction,
} from "react";
import { HelperText } from "@gx-design/helper-text";
import { Icon } from "@gx-design/icon";
import { Tooltip } from "@gx-design/tooltip";
import clsx from "clsx";

export type InputProps = {
  /**
   * The label title
   */
  label: string;
  /**
   * The tooltip helper text
   */
  tooltipHelper?: string;
  /**
   * Determine if label is visible to user
   */
  isLabelVisible?: boolean;
  /**
   * The error message
   */
  error?: string;
} & InputHTMLAttributes<HTMLInputElement>;

const InputComponent: ForwardRefRenderFunction<HTMLInputElement, InputProps> = (
  { label, tooltipHelper, isLabelVisible = true, error = "", ...props },
  ref
) => {
  return (
    <div className="gx-input-wrapper">
      <label
        htmlFor={props.id || undefined}
        className={clsx("gx-label", {
          "gx-sr-only": !isLabelVisible,
        })}
      >
        {label}
        {props.required && <span className="gx-label__required">*</span>}
        {tooltipHelper && (
          <span className="gx-tip">
            <Tooltip position="top" text={tooltipHelper}>
              <Icon className="gx-icon--info" name="info-circle" />
            </Tooltip>
          </span>
        )}
      </label>
      <input
        ref={ref}
        className={clsx("gx-input", {
          "gx-input--negative": error,
        })}
        {...props}
      />
      {error && <HelperText text={error} style="error" />}
    </div>
  );
};

export const Input = forwardRef(InputComponent);
