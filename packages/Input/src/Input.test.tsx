import { expect, it, vi } from "vitest";
import { render, screen } from "../../../test/utilities";
import { Input } from "./Input";

it("renders label and input", () => {
  render(<Input id="bay" label="Username" />);
  expect(screen.getByLabelText("Username")).toBeInTheDocument();
});

it("renders tooltip helper text", async () => {
  const { baseElement, user } = render(
    <Input label="Username" id="usr" tooltipHelper="This is the tooltip" />
  );

  const labelElement = screen.getByLabelText("Username");
  expect(labelElement).toBeInTheDocument();

  const iconElement = baseElement.querySelector(".gx-icon--info");

  expect(iconElement).toBeInTheDocument();

  if (iconElement) {
    await user.hover(iconElement);
    const tooltipElement = await screen.findByText("This is the tooltip");

    expect(tooltipElement).toBeInTheDocument();
  } else {
    expect.fail("Icon element not found");
  }
});

it("hides label when isLabelVisible is false", () => {
  render(<Input label="Username" isLabelVisible={false} />);
  expect(screen.queryByLabelText("Username")).not.toBeInTheDocument();
});

it("renders error message", () => {
  render(<Input label="Username" error="Invalid username" />);
  expect(screen.getByText("Invalid username")).toBeInTheDocument();
});

it("calls onChange callback when input value changes", async () => {
  const onChange = vi.fn();
  const { user } = render(
    <Input label="Username" id="a" onChange={onChange} />
  );
  await user.type(screen.getByLabelText("Username"), "test");
  expect(onChange).toHaveBeenCalledTimes(4);
});

it("displays an asterisc when the input is required", async () => {
  render(<Input label="Username" required />);
  expect(screen.getByText("Username")).toHaveTextContent("Username*");
});
