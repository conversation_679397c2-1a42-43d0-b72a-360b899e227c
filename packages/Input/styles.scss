// ==========================================================================
// Input - Input/Components
// ==========================================================================
@use "@gx-design/theme/styles" as *;
@use "@gx-design/tools/styles" as *;

.gx-input-stepper {
  &__value {
    display: inline-flex;
    justify-content: center;
    width: space(md);
    margin: 0 space(md);
  }
}

.gx-input-wrapper {
  position: relative;

  // Label is an icon
  &--withIcon {
    & > div:not(.gx-helper) {
      align-items: center;
      display: flex;
      width: 100%;

      .gx-icon {
        color: color(content-action);
        flex-shrink: 0;
        width: 1.6rem;
        height: 1.6rem;
        margin-right: space(md);
      }

      .gx-input-clear {
        .gx-icon {
          margin-right: 0;
        }
      }
    }
  }

  &.is-disabled {
    cursor: not-allowed;
  }
}

// -------------------------------
// Wrapper for input type number
// -------------------------------
.gx-input-number-wrapper {
  border-radius: radius(md);
  display: flex;

  &--negative {
    .gx-input {
      border: 0.1rem solid color(border-error);
    }
  }

  &--warning {
    .gx-input {
      border: 0.1rem solid color(border-warning);
    }
  }

  &.is-disabled {
    cursor: not-allowed;
  }
}

// -------------------------------
// Wrapper for input with loader
// -------------------------------
.gx-input-loader-wrapper {
  position: relative;

  // Loader
  .gx-input-loader {
    color: color(content-action);
    position: absolute;
    right: space(md);
    top: 50%;
    transform: translateY(-50%);
  }
}

// -------------------------------
// Wrapper for input with button
// -------------------------------
.gx-input-button-wrapper {
  display: flex;

  .gx-button {
    border-bottom-left-radius: 0;
    border-top-left-radius: 0;
  }

  .gx-input {
    border-bottom-right-radius: 0;
    border-top-right-radius: 0;
    border: 0.1rem solid color(border-main);
    border-right: 0;
  }

  &--negative {
    .gx-input {
      border-color: color(border-error);
    }
  }

  &--warning {
    .gx-input {
      border-color: color(border-warning);
    }
  }

  &.is-disabled {
    cursor: not-allowed;

    .gx-button {
      opacity: 0.65;
      cursor: not-allowed;
    }
  }
}

// -------------------------------
// Wrapper for input with addon
// -------------------------------
.gx-input-addon-wrapper {
  border: 0.1rem solid color(border-main);
  border-radius: radius(md);
  display: flex;
  height: 4rem;

  .gx-input {
    &:first-child {
      border-top-right-radius: 0;
      border-bottom-right-radius: 0;
    }

    &:last-child {
      border-top-left-radius: 0;
      border-bottom-left-radius: 0;
    }
  }

  // Using focus-within to highlight the whole wrapper when in focus
  &:focus-within {
    border: 0.1rem solid color(border-selected);
    box-shadow: 0 0 0 0.3rem color(background-brand-alt);
  }

  &--negative {
    border-color: color(border-error);
  }

  &--warning {
    border-color: color(border-warning);
  }

  &.is-disabled {
    cursor: not-allowed;

    .gx-input-addon {
      .gx-icon {
        color: color(content-low);
      }
    }
  }

  // This is needed because otherwise the wrapper will be 4.2rem height
  .gx-input,
  .gx-input-addon {
    height: 100%;
  }
}

.gx-input {
  background-color: color(background-main);
  border: 0.1rem solid color(border-main);
  border-radius: radius(md);
  @include typography(body);
  height: 4rem;
  padding: space(sm);
  width: 100%;
  color: color(content-high);
  // This is needed to remove safari inner shadow on mobile
  -webkit-appearance: none;

  &-password-recovery {
    position: absolute;
    right: space(sm);
    bottom: 2rem;
    transform: translateY(50%);
    @include z-index(base, 3);
    @include typography(body);

    &-wrapper {
      position: relative;

      // This is needed to prevent password overlaps with password recovery link
      .gx-input {
        padding-right: 12rem;
      }
    }
  }

  &-clear {
    align-items: center;
    color: color(content-medium);
    opacity: 0.6;
    display: none;
    height: 4rem;
    justify-content: center;
    position: absolute;
    right: space(sm);
    top: 50%;
    transform: translateY(-50%);
    width: 4rem;
    border: 0;
    padding: 0;
    @include z-index(base, 3);
    font-size: 1.6rem;

    &:hover {
      opacity: 1;
    }

    &-wrapper {
      position: relative;
    }

    &--visible {
      display: flex;
    }

    &--circled {
      cursor: pointer;
      width: 1.6rem;
      height: 1.6rem;
      background-color: transparent;

      .gx-icon {
        @include icon-size(xs);
      }
    }

    &:focus {
      outline: none;
      border: 0.1rem solid color(border-selected);
      box-shadow: 0 0 0 0.3rem color(background-brand-alt);
    }
  }

  &-addon {
    align-items: center;
    color: color(content-low);
    display: flex;
    flex-shrink: 0;
    height: 4rem;
    justify-content: center;
    padding: space(sm) space(md);
    background-color: color(background-alt);
    border-top-left-radius: radius(md);
    border-bottom-left-radius: radius(md);

    .gx-input + & {
      border-radius: 0 radius(md) radius(md) 0;
    }

    &--primary {
      color: color(content-action);
    }

    &--withIcon {
      padding: 0;
      width: 4rem;
    }

    .gx-icon {
      color: currentColor;
      @include icon-size(md);
    }
  }

  // Remove border cause with the addon, the wrapper already has it
  &--withAddon {
    border: 0;
  }

  &--withNumber {
    background-color: transparent;
    text-align: center;
    border: 0;
    margin: 0 space(sm);
  }

  &:placeholder {
    color: color(content-low);
  }

  &:disabled {
    background-color: color(background-alt);
    cursor: not-allowed;
    color: color(content-low);
    /* required on iOS */
    -webkit-text-fill-color: color(content-low);
    opacity: 1;

    + .gx-input-clear {
      cursor: not-allowed;
      background-color: color(content-low);
    }
  }

  &:focus {
    outline: none;
    @include z-index(base, 2);

    // Excluding --withAddon that uses focus-within
    &:not(.gx-input--withAddon) {
      border: 0.1rem solid color(border-selected);
      box-shadow: 0 0 0 0.3rem color(background-brand-alt);
    }
  }

  &.gx-input--negative {
    border-color: color(border-error);
    background-color: color(background-error) !important;
  }
}

@include media("screen", ">=#{breakpoint(sm)}") {
  .gx-input {
    @include typography(body);

    &-password-recovery {
      @include typography(body-small);

      &-wrapper {
        position: relative;

        // This is needed to prevent password overlaps with password recovery link
        .gx-input {
          padding-right: 10rem;
        }
      }
    }
  }
}
