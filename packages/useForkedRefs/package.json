{"name": "@gx-design/use-forked-refs", "version": "1.1.1", "description": "Gx Design useForkedRefs hook", "source": "src/useForkedrefs.ts", "main": "dist/index.js", "module": "dist/index.mjs", "peerDependencies": {"react": "*"}, "devDependencies": {"tsup": "^7.2.0"}, "scripts": {"build": "tsup", "watch": "tsup --watch"}, "repository": {"type": "git", "url": "********************:getrix/gx-design.git", "directory": "packages/useForkedRefs"}, "typings": "dist/index.d.ts", "volta": {"extends": "../../package.json"}}