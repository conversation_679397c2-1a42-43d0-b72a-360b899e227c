# Change Log

All notable changes to this project will be documented in this file.
See [Conventional Commits](https://conventionalcommits.org) for commit guidelines.

## [1.1.1](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/use-forked-refs@1.1.0...@gx-design/use-forked-refs@1.1.1) (2023-12-01)

**Note:** Version bump only for package @gx-design/use-forked-refs

# [1.1.0](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/use-forked-refs@1.0.7...@gx-design/use-forked-refs@1.1.0) (2023-09-27)

### Features

- **storybook:** upgrade to 7 ([1c0e5e9](https://gitlab.pepita.io/getrix/gx-design/commit/1c0e5e941dcf7b841d1b5d2a2825f66f7921276e))

## [1.0.7](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/use-forked-refs@1.0.6...@gx-design/use-forked-refs@1.0.7) (2023-09-08)

**Note:** Version bump only for package @gx-design/use-forked-refs

## [1.0.6](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/use-forked-refs@1.0.3...@gx-design/use-forked-refs@1.0.6) (2023-05-03)

### Bug Fixes

- **version:** update ([b86d122](https://gitlab.pepita.io/getrix/gx-design/commit/b86d122e4ebfae3c29b22379c844880c05c612b5))

## [1.0.3](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/use-forked-refs@1.0.2...@gx-design/use-forked-refs@1.0.3) (2022-11-22)

**Note:** Version bump only for package @gx-design/use-forked-refs

## [1.0.3-alpha.1](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/use-forked-refs@1.0.3-alpha.0...@gx-design/use-forked-refs@1.0.3-alpha.1) (2022-11-21)

**Note:** Version bump only for package @gx-design/use-forked-refs

## [1.0.3-alpha.0](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/use-forked-refs@1.0.2...@gx-design/use-forked-refs@1.0.3-alpha.0) (2022-11-21)

**Note:** Version bump only for package @gx-design/use-forked-refs

## [1.0.2](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/use-forked-refs@1.0.1...@gx-design/use-forked-refs@1.0.2) (2022-10-12)

### Bug Fixes

- **notification:** fix notify ([213000c](https://gitlab.pepita.io/getrix/gx-design/commit/213000c8bc11b83eb5d9ff401e51d57d76411548))

## [1.0.2-alpha.0](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/use-forked-refs@1.0.1...@gx-design/use-forked-refs@1.0.2-alpha.0) (2022-10-12)

### Bug Fixes

- **notification:** fix notify ([5d949cd](https://gitlab.pepita.io/getrix/gx-design/commit/5d949cd42a3e78c3c63ba78aa1d1f4595e6e3397))

## 1.0.1 (2022-10-06)

**Note:** Version bump only for package @gx-design/use-forked-refs

## 1.0.1-alpha.0 (2022-09-26)

**Note:** Version bump only for package @gx-design/use-forked-refs
