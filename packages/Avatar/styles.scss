// ==========================================================================
// Avatar - Components/Avatar
// ==========================================================================
@use "@gx-design/theme/styles" as *;

.gx-avatar {
  position: relative;
  width: 4rem;
  height: 4rem;
  border-radius: radius(rounded);
  overflow: hidden;

  img {
    max-width: 100%;
    max-height: 100%;
  }

  .is-horizontal {
    max-width: none;
    position: absolute;
    transform: translateX(-50%);
    left: 50%;
  }

  .is-vertical {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    max-width: 100%;
    max-height: none;
  }

  &--small {
    width: 2.4rem;
    height: 2.4rem;
  }

  &--big {
    width: 6.4rem;
    height: 6.4rem;
  }
}
