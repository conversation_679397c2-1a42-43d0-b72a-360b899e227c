import React, { useState } from "react";
import clsx from "clsx";

export interface AvatarProps {
  /**
   The Avatar image
  */
  avatarImage: string;
  /**
   Define the size of the avatar
  */
  size?: "small" | "big";
  /**
   Alt text
  */
  altText?: string;
  /**
    Custom class names
  */
  className?: string;
}

export const Avatar: React.FC<AvatarProps> = ({
  avatarImage,
  size = null,
  altText = "Avatar",
  className,
}) => {
  const [imageOrientationClass, setImageOrientationClass] = useState<
    string | null
  >(null);

  // Function to handle image load and check orientation
  const handleImageLoad = (
    event: React.SyntheticEvent<HTMLImageElement, Event>
  ) => {
    const img = event.currentTarget;

    if (img.width > img.height) {
      setImageOrientationClass("is-horizontal");
    } else if (img.width < img.height) {
      setImageOrientationClass("is-vertical");
    } else {
      setImageOrientationClass(null);
    }
  };

  return (
    <div className={clsx("gx-avatar", size && `gx-avatar--${size}`, className)}>
      <img
        src={avatarImage}
        alt={altText}
        className={
          imageOrientationClass ? `${imageOrientationClass}` : undefined
        }
        onLoad={handleImageLoad}
      />
    </div>
  );
};
