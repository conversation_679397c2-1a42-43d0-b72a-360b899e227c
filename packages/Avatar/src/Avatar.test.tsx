import React from "react";
import { render, screen, fireEvent } from "../../../test/utilities";
import { Avatar } from "./Avatar";
import { it, expect } from "vitest";

it("renders an image with the correct src and alt text", () => {
  const avatarImage = "https://example.com/avatar.jpg";
  const altText = "User Avatar";

  render(<Avatar avatarImage={avatarImage} altText={altText} />);

  // Check that the image is rendered with the correct src and alt
  const img = screen.getByAltText(altText);
  expect(img).toBeInTheDocument();
  expect(img).toHaveAttribute("src", avatarImage);
  expect(img).toHaveAttribute("alt", altText);
});

it("should set the correct orientation class based on the image ratio", async () => {
  render(<Avatar avatarImage="test.jpg" altText="Test Avatar" />);

  const img = screen.getByAltText("Test Avatar") as HTMLImageElement;

  if (img) {
    img.width = 200;
    img.height = 100;
    fireEvent.load(img);
  }

  expect(img).toHaveClass("is-horizontal");

  if (img) {
    img.width = 100;
    img.height = 200;
    fireEvent.load(img);
  }

  expect(img).toHaveClass("is-vertical");

  if (img) {
    img.width = 200;
    img.height = 200;
    fireEvent.load(img);
  }

  expect(img).not.toHaveClass();
});

it("should render with gx-avatar--small class when size is small", () => {
  render(<Avatar avatarImage="test.jpg" size="small" altText="Test Avatar" />);
  const avatarSmall = screen.getByAltText("Test Avatar");
  expect(avatarSmall.parentElement).toHaveClass("gx-avatar--small");
});

it("should render with gx-avatar--big class when size is big", () => {
  render(<Avatar avatarImage="test.jpg" size="big" altText="Test Avatar" />);
  const avatarBig = screen.getByAltText("Test Avatar");
  expect(avatarBig.parentElement).toHaveClass("gx-avatar--big");
});

it("should apply custom className if provided", () => {
  render(
    <Avatar
      avatarImage="test.jpg"
      altText="Test Avatar"
      className="custom-class"
    />
  );

  const avatar = screen.getByAltText("Test Avatar");

  expect(avatar.parentElement).toHaveClass("custom-class");
});
