{"name": "@gx-design/accordion", "version": "5.2.16", "description": "Gx Design Accordion component", "source": "src/Accordion.tsx", "main": "dist/index.js", "module": "dist/index.mjs", "peerDependencies": {"react": "*"}, "devDependencies": {"tsup": "^7.2.0"}, "dependencies": {"@gx-design/button": "^5.3.13", "@gx-design/icon": "^5.6.3", "@gx-design/theme": "^1.4.0", "@gx-design/use-media-match": "^3.0.2", "clsx": "1.1.1"}, "scripts": {"build": "tsup", "watch": "tsup --watch"}, "repository": {"type": "git", "url": "********************:getrix/gx-design.git", "directory": "packages/Accordion"}, "typings": "dist/index.d.ts", "volta": {"extends": "../../package.json"}}