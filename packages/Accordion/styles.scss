@use "@gx-design/theme/styles" as *;

$background: color(background-alt) !default;
$gx-accordion: "gx-accordion";

.#{$gx-accordion} {
  --nd-accordion-bg: #{$background};

  overflow: hidden;
  border: 0.1rem solid color(border-main);
  border-radius: radius(sm);

  & + & {
    margin-top: space("md");
  }

  &__head {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: space(sm) space(md);
    height: 5.6rem;
    background-color: $background;
    cursor: pointer;

    .gx-icon {
      @include icon-size(md);
      color: color(content-action);
      transition: transform 0.3s ease-in-out;
    }

    .gx-button {
      margin-right: space(md);
    }
  }

  .is-open & {
    &__head {
      .gx-icon {
        transform: rotate(180deg);
      }
      .gx-button {
        .gx-icon {
          transform: none;
        }
      }
    }
  }

  &__body {
    max-height: 0;
    padding: 0 space(md);
    border: 0 solid transparent;
    transition: all 0.3s ease-in-out;

    .is-open & {
      max-height: 100vh;
      display: block;
      padding: space(md);
      border-top: 0.1rem solid color(border-main);
    }
  }
}
