import React, { ReactElement, useState } from "react";
import { Icon } from "@gx-design/icon";
import { ButtonProps } from "@gx-design/button";
import { useMediaMatch } from "@gx-design/use-media-match";
import clsx from "clsx";

export type AccordionProps = {
  /**
   The content of the Accordion
  */
  children:
    | ReactElement<AccordionItemProps>
    | Array<ReactElement<AccordionItemProps>>;
};

export type AccordionItemProps = {
  /**
   The title of the Accordion
  */
  title: string;
  /**
   Call to action (Button component)
  */
  cta?: ReactElement<ButtonProps>;
  /**
   The content of the Accordion
  */
  children: ReactElement;
};

export const Accordion: React.FC<AccordionProps> = ({ children }) => {
  return <>{children}</>;
};

export const AccordionItem: React.FC<AccordionItemProps> = ({
  title,
  cta,
  children,
}) => {
  const isDesktop = useMediaMatch("desktop");
  const [isOpen, setOpen] = useState<boolean>(true);
  const toggleClass = () => {
    setOpen(!isOpen);
  };

  return (
    <div className={clsx("gx-accordion", isOpen ? "is-close" : "is-open")}>
      <div className="gx-accordion__head" onClick={toggleClass}>
        <div className="gx-title-2">{title}</div>
        <div>
          {cta && isDesktop ? cta : null}
          <Icon data-testid="accordion-icon" name="arrow-down" />
        </div>
      </div>
      <div className="gx-accordion__body">{children}</div>
    </div>
  );
};
