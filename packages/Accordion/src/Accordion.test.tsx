import React from "react";
import { render, screen, fireEvent } from "../../../test/utilities";
import { Accordion, AccordionItem } from "./Accordion";
import { it, expect, describe, vi, afterEach } from "vitest";
import { Button } from "@gx-design/button";
import { useMediaMatch } from "@gx-design/use-media-match";
// Mock the module and define the mock function inside the factory
vi.mock("@gx-design/use-media-match");

describe("Accordion", () => {
  afterEach(() => {
    vi.resetAllMocks();
  });

  it("should render the accordion with multiple items", () => {
    render(
      <Accordion>
        <AccordionItem title="Item 1">
          <>Content 1</>
        </AccordionItem>
        <AccordionItem title="Item 2">
          <>Content 2</>
        </AccordionItem>
      </Accordion>
    );
    expect(screen.getByText("Item 1")).toBeInTheDocument();
    expect(screen.getByText("Item 2")).toBeInTheDocument();
    expect(screen.getByText("Content 1")).toBeInTheDocument();
    expect(screen.getByText("Content 2")).toBeInTheDocument();
  });

  it("should toggle accordion item open/close state", () => {
    render(
      <Accordion>
        <AccordionItem title="Toggle Item">
          <>Content</>
        </AccordionItem>
      </Accordion>
    );

    const toggleItem = screen.getByText("Toggle Item");
    const accordionHeader = toggleItem.closest(".gx-accordion__head");
    const accordion = toggleItem.closest(".gx-accordion");

    expect(accordion).toHaveClass("is-close");
    accordionHeader && fireEvent.click(accordionHeader);
    expect(accordion).toHaveClass("is-open");
    accordionHeader && fireEvent.click(accordionHeader);
    expect(accordion).toHaveClass("is-close");
  });

  it("renders CTA button only on desktop", () => {
    vi.mocked(useMediaMatch).mockReturnValue(true);
    const mockCta = <Button variant="default">Action</Button>;

    const { rerender } = render(
      <Accordion>
        <AccordionItem title="Section" cta={mockCta}>
          <>Content</>
        </AccordionItem>
      </Accordion>
    );

    // Assert that the CTA button is rendered
    expect(screen.getByText("Action")).toBeInTheDocument();

    // Mock mobile view
    vi.mocked(useMediaMatch).mockReturnValue(false);

    // Rerender the component with the updated mock
    rerender(
      <Accordion>
        <AccordionItem title="Section" cta={mockCta}>
          <>Content</>
        </AccordionItem>
      </Accordion>
    );

    // Assert that the CTA button is not rendered
    expect(screen.queryByText("Action")).not.toBeInTheDocument();
  });

  it("renders arrow icon in header", () => {
    render(
      <Accordion>
        <AccordionItem title="Section">
          <>Content</>
        </AccordionItem>
      </Accordion>
    );

    const icon = screen.getByTestId("accordion-icon");
    expect(icon).not.toBeNull();

    // Assert that the element is in the document
    expect(icon).toBeInTheDocument();
  });
});
