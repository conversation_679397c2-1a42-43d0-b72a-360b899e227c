import { expect, it } from "vitest";
import { render, screen } from "../../../test/utilities";
import { List, ListItem } from "./List";
import React from "react";

it("should render a list with item with a text", () => {
  render(
    <List>
      <ListItem content="Elemento 1" />
    </List>
  );

  expect(screen.getByText(/Elemento/)).toBeInTheDocument();
});

it("should render a List with an item using an icon", () => {
  const { container } = render(
    <List>
      <ListItem content="Elemento 1" icon="arrow-down" />
    </List>
  );

  expect(screen.getByText(/Elemento/)).toBeInTheDocument();
  expect(container.querySelector("svg")?.childElementCount).toBeGreaterThan(0);
});

it("List should render two items with the text", () => {
  render(
    <List>
      <ListItem content="Elemento 1" />
      <ListItem content="Elemento 2" />
    </List>
  );

  expect(screen.getAllByText(/Elemento/)).toHaveLength(2);
});

it("List should render custom class if defined", () => {
  render(
    <List className="test-class">
      <ListItem content="Elemento 1" />
      <ListItem content="Elemento 2" />
    </List>
  );

  expect(
    screen.getByRole("list").classList.contains("test-class")
  ).toBeTruthy();
});

it("ListItems should render custom class if defined", () => {
  render(
    <List>
      <ListItem content="Elemento 1" className="test-class-1" />
      <ListItem content="Elemento 2" className="test-class-2" />
    </List>
  );

  expect(
    screen.getAllByRole("listitem")[0].classList.contains("test-class-1")
  ).toBeTruthy();
  expect(
    screen.getAllByRole("listitem")[1].classList.contains("test-class-2")
  ).toBeTruthy();
});

it.todo("should not render an icon if the name does not exist");
