import React, { ReactElement, ReactNode } from "react";
import { Icon, IconProps } from "@gx-design/icon";
import clsx from "clsx";

export type ListProps = {
  /** 
   The content of the List
  */
  children: ReactElement | Array<ReactElement | null | boolean>;
  /**
   Custom class name
  */
  className?: string;
};

export type ListItemProps = {
  /**
   The content to render inside the listItem, can be a simple text or a complex component
  */
  content: ReactNode;
  /**
    Icon
  */
  icon?: IconProps["name"];
  /**
   Custom class name
  */
  className?: string;
};

export const List: React.FC<ListProps> = ({ children, className }) => {
  return (
    <div role="list" className={clsx("gx-list", className)}>
      {children}
    </div>
  );
};

export const ListItem: React.FC<ListItemProps> = ({
  content,
  icon = null,
  className,
}) => {
  return (
    <div
      role="listitem"
      className={clsx("gx-list__item", className, {
        "gx-list__item--withElement": icon,
      })}
    >
      {icon && <Icon name={icon} />}
      <span>{content}</span>
    </div>
  );
};
