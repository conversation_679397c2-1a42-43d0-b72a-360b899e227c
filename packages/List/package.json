{"name": "@gx-design/list", "version": "5.3.13", "description": "Gx Design List component", "source": "src/List.tsx", "main": "dist/index.js", "module": "dist/index.mjs", "peerDependencies": {"react": "*"}, "devDependencies": {"tsup": "^7.2.0"}, "dependencies": {"@gx-design/icon": "^5.6.3", "@gx-design/theme": "^1.4.0", "clsx": "1.1.1"}, "scripts": {"build": "tsup", "watch": "tsup --watch"}, "repository": {"type": "git", "url": "********************:getrix/gx-design.git", "directory": "packages/List"}, "typings": "dist/index.d.ts", "volta": {"extends": "../../package.json"}}