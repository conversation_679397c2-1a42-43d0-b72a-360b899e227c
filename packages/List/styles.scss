// ==========================================================================
// List - Components
// ==========================================================================
@use "@gx-design/theme/styles" as *;

.gx-list {
  padding: 0;
  margin: 0;
  list-style: none;
  @include typography(body-small);

  &__item {
    span {
      & + span {
        padding-left: space(xs);
        margin-left: space(xs);
        border-left: 0.1rem solid color(border-main);
      }

      svg {
        margin-left: space(xs);
      }
    }

    .gx-icon {
      @include icon-size(xs);
      flex-shrink: 0;
    }

    &--withElement {
      display: flex;
      align-items: center;
      gap: 0 space(xs);

      &Interactive {
        .gx-icon {
          color: color(content-action);
        }
      }
    }
  }
}
