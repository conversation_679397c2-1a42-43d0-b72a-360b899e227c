// ==========================================================================
// Theme - Tools
// ==========================================================================
@use "sass:map";
@use "sass:meta";
@use "sass:string";
@use "tokens" as tokens;
@use "@gx-design/tools/styles" as tools;

// Generate one level map from $tokens

@mixin icon-size($size) {
  @if map-has-key(tokens.$icon-sizes, $size) {
    font-size: map.get(tokens.$icon-sizes, $size);
  } @else {
    @warn "Attention! '$size' it is not a valid value.";
  }
}

@mixin typography($style) {
  $style-props: map.get(tokens.$typography, $style);

  @each $key, $value in $style-props {
    @if $key == "desktop" {
      $sub: map.get($style-props, $key);

      @include tools.media(">=#{breakpoint(md)}") {
        @each $k, $v in $sub {
          #{$k}: $v;
        }
      }
    } @else {
      #{$key}: $value;
    }
  }
}

@function color($val, $raw: false) {
  $color: null;
  @if (
    meta.type-of($val) ==
      "color" or
      $val ==
      "currentColor" or
      string.index($val, "var(--")
  ) {
    $color: $val;
  } @else if $raw == true {
    $color: map.get(tokens.$colors, $val);
  } @else {
    $color: var(--#{$val});
  }
  @return $color;
}

// check if $value1 and $value2 exists in $map
// return true or false
@function _test($map, $key) {
  $warn: "Invalid option:  #{$key}. Choose a group and a key from: #{map.keys($map)}";
  $check: map.has-key($map, $key);

  @if $check == true {
    @return true;
  } @else {
    @warn $warn;
    @error $warn;
    @return false;
  }
}

// get $key from $sub in $map
// or trhow a warning and an error
// warning is usefull when the error doesn't get displayed in dev mode somethimes
@function get($map, $keys...) {
  @each $key in $keys {
    $test: _test($map, $key);

    $map: map.get($map, $key);
  }

  @return $map;
}

@mixin z-index($index, $adjustment: 0) {
  $allowed-values: -9, -8, -7, -6, -5, -4, -3, -2, -1, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9;

  @if (map-has-key(tokens.$z-indexes, $index)) or
    (type-of($index) == number and floor($index) == $index)
  {
    // $index è una chiave valida in $z-indexes o un numero intero
    @if (index($allowed-values, $adjustment) == null) {
      @error "Attention! '$adjustment' is not a valid adjustment value. Adjustment must be one of the allowed values: #{$allowed-values}.";
    } @else {
      @if map-has-key(tokens.$z-indexes, $index) {
        $base-value: map.get(tokens.$z-indexes, $index);
        $final-z-index: $base-value + ($adjustment * 10);
        z-index: $final-z-index;
      } @else {
        $base-value: $index;
        $final-z-index: $base-value + ($adjustment * 10);
        z-index: $final-z-index;
      }
    }
  } @else {
    // $index non è una chiave valida in $z-indexes né un numero intero
    @error "Attention! '$index' is not a valid index value. It must be a key in $z-indexes or an integer.";
  }
}

@function space($key) {
  @return get(tokens.$spacings, $key);
}

@function radius($key) {
  @return get(tokens.$radii, $key);
}

@function elevation($key) {
  @return get(tokens.$elevations, $key);
}

@function breakpoint($key) {
  @return get(tokens.$breakpoints, $key);
}
