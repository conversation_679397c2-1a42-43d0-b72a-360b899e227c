# Change Log

All notable changes to this project will be documented in this file.
See [Conventional Commits](https://conventionalcommits.org) for commit guidelines.

# [1.4.0](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/theme@1.3.0...@gx-design/theme@1.4.0) (2024-10-31)

### Features

- **toogle:** new component ([a5bf969](https://gitlab.pepita.io/getrix/gx-design/commit/a5bf969c811e9624a430594f06ee9d36e8e68fe1))

# [1.3.0](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/theme@1.2.0...@gx-design/theme@1.3.0) (2024-09-23)

### Features

- **css:** custom properties ([cbc56c0](https://gitlab.pepita.io/getrix/gx-design/commit/cbc56c0e3e675d85f92ad25ac135cce270f02b2b))

# [1.3.0-alpha.0](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/theme@1.2.0...@gx-design/theme@1.3.0-alpha.0) (2024-09-16)

### Features

- **css:** custom properties ([1eabaee](https://gitlab.pepita.io/getrix/gx-design/commit/1eabaeea4e691c8c6672cf237e48ece7ab491fb7))

# [1.2.0](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/theme@1.1.2...@gx-design/theme@1.2.0) (2024-08-29)

### Features

- **colors:** pro new colors ([2103ea7](https://gitlab.pepita.io/getrix/gx-design/commit/2103ea76597b5ae75018d2d62947c6b01d613e82))

# [1.2.0-alpha.1](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/theme@1.1.2...@gx-design/theme@1.2.0-alpha.1) (2024-07-26)

### Features

- **colors:** pro new colors ([d88639f](https://gitlab.pepita.io/getrix/gx-design/commit/d88639f983895257b905dfad2fb65db3e75efee0))

# [1.2.0-alpha.0](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/theme@1.1.2...@gx-design/theme@1.2.0-alpha.0) (2024-07-22)

### Features

- **colors:** pro new colors ([1cc3271](https://gitlab.pepita.io/getrix/gx-design/commit/1cc327120221beae8fb3231bb0ac9170b77e1b4a))

## [1.1.2](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/theme@1.1.1...@gx-design/theme@1.1.2) (2024-03-26)

**Note:** Version bump only for package @gx-design/theme

## [1.1.1](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/theme@1.1.0...@gx-design/theme@1.1.1) (2024-03-20)

**Note:** Version bump only for package @gx-design/theme

# 1.1.0 (2024-03-18)

**Note:** Version bump only for package @gx-design/theme

# 1.1.0-alpha.2 (2024-03-07)

### Features

- **scss:** alert and theme styles ([186deed](https://gitlab.pepita.io/getrix/gx-design/commit/186deede25c30e59b1c98dd3702c328504c6050b))
- **scss:** alert and theme styles ([6213e64](https://gitlab.pepita.io/getrix/gx-design/commit/6213e6473351d66cb0a43547ca04c69b6e1854cc))
- **scss:** alert and theme styles ([7c0102d](https://gitlab.pepita.io/getrix/gx-design/commit/7c0102d309fce10724944b1e2e355d391d84ebe7))
- **styles:** general ([fc4d50a](https://gitlab.pepita.io/getrix/gx-design/commit/fc4d50a8c23c848f36e9a3c9831cea4606eff985))

# [1.1.0-alpha.1](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/theme@1.1.0-alpha.0...@gx-design/theme@1.1.0-alpha.1) (2024-02-29)

**Note:** Version bump only for package @gx-design/theme

# 1.1.0-alpha.0 (2024-02-28)

### Features

- **scss:** alert and theme styles ([d104cb9](https://gitlab.pepita.io/getrix/gx-design/commit/d104cb95f68efd2014cb0cda3105e9b9744e1b36))
- **scss:** alert and theme styles ([7a0c49d](https://gitlab.pepita.io/getrix/gx-design/commit/7a0c49d38dce8d15a4fd13929a3008198435487f))
