// ==========================================================================
// Theme - ImmoPro Tokens
// ==========================================================================

$tokens: (
  colors: (
    background: (
      main: #ffffff,
      alt: #f5f6f7,
      reversed: #373c47,
      brand: #0074c1,
      brand-alt: #e6f1f9,
      accent: #c12400,
      action: #ffffff,
      selectable: #ffffff,
      selected: #e6f1f9,
      selected-high: #0074c1,
      info: #e4e9f9,
      info-high: #095ec8,
      success: #e4f1e7,
      success-high: #1b9c4e,
      warning: #fff1df,
      warning-high: #fda932,
      error: #f8e5e6,
      error-high: #c11e3d,
      notification: #d82244,
    ),
    content: (
      high: #373c47,
      medium: #60687a,
      low: #9fa5b6,
      reversed: #ffffff,
      accent: #ffffff,
      action: #0074c1,
      selectable: #373c47,
      selected: #0074c1,
      selected-high: #ffffff,
      info: #095ec8,
      success: #1b9c4e,
      warning: #fda932,
      error: #c11e3d,
      notification: #ffffff,
    ),
    border: (
      main: #d5d7dd,
      reversed: #ffffff,
      active: #0074c1,
      action: #d5d7dd,
      selectable: #d5d7dd,
      selected: #8abfe2,
      info: #a1b5ed,
      success: #a3d2af,
      warning: #fdba5b,
      error: #e8a3aa,
    ),
  ),
  spacings: (
    xs: 0.4rem,
    sm: 0.8rem,
    md: 1.6rem,
    lg: 2.4rem,
    xl: 3.2rem,
    2xl: 4.8rem,
    3xl: 6.4rem,
    4xl: 8rem,
  ),
  radii: (
    sm: 0.4rem,
    md: 0.8rem,
    lg: 1.6rem,
    rounded: 99em,
  ),
  elevations: (
    fixed-top: (
      0 0 0.2rem 0 rgba(#373c47, 0.16),
      0 0.1rem 0.4rem 0 rgba(#373c47, 0.12),
    ),
    fixed-bottom: (
      0 0 0.2rem 0 rgba(#373c47, 0.16),
      0 -0.1rem 0.4rem 0 rgba(#373c47, 0.12),
    ),
    raised: (
      0 0.4rem 0.8rem 0 rgba(#373c47, 0.16),
      0 0.8rem 2.4rem 0 rgba(#373c47, 0.16),
    ),
    overlay: (
      0 0.8rem 6rem 0 rgba(#373c47, 0.16),
      0 1.2rem 2.4rem -0.4rem rgba(#373c47, 0.24),
    ),
  ),
  typography: (
    display-1: (
      font-size: 4rem,
      font-weight: 400,
      line-height: 5.6rem,
      letter-spacing: -0.15rem,
      desktop: (
        font-size: 4.8rem,
        line-height: 6.4rem,
      ),
    ),
    display-2: (
      font-size: 3.2rem,
      font-weight: 500,
      line-height: 4.8rem,
      letter-spacing: -0.1rem,
    ),
    display-subtitle: (
      font-size: 2rem,
      font-weight: 500,
      line-height: 2.4rem,
    ),
    title-1: (
      font-size: 2rem,
      font-weight: 600,
      line-height: 2.4rem,
      letter-spacing: -0.05rem,
      desktop: (
        font-size: 2.4rem,
        line-height: 3.2rem,
      ),
    ),
    title-2: (
      font-size: 1.6rem,
      font-weight: 600,
      line-height: 2.4rem,
      desktop: (
        letter-spacing: -0.05rem,
      ),
    ),
    body: (
      font-size: 1.6rem,
      font-weight: 400,
      line-height: 2.4rem,
    ),
    button: (
      font-size: 1.4rem,
      font-weight: 600,
      line-height: 1.6rem,
      text-transform: uppercase,
    ),
    body-small: (
      font-size: 1.4rem,
      font-weight: 400,
      line-height: 1.6rem,
    ),
    body-tiny: (
      font-size: 1.2rem,
      font-weight: 400,
      line-height: 1.6rem,
    ),
    overline: (
      font-size: 1.2rem,
      font-weight: 400,
      line-height: 1.6rem,
      text-transform: uppercase,
    ),
  ),
  breakpoints: (
    xs: 30em,
    sm: 48em,
    md: 64em,
    lg: 80em,
    xl: 90em,
  ),
  icon-sizes: (
    //unofficial
    xs: 1.6rem,
    sm: 2rem,
    md: 2.4rem,
  ),
  z-indexes: (
    //unofficial
    base: 1000,
    dropdown: 1200,
    head-section: 1400,
    loader-overlay: 1600,
    sidebar: 1800,
    navigation: 2000,
    modal: 2200,
    snackbar: 2400,
    popover: 2600,
    tooltip: 2800,
  ),
);
