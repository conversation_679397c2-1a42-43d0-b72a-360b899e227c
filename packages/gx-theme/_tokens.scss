// ==========================================================================
// Theme - Tokens
// ==========================================================================
@use "sass:map";

/// @param { number } $pixels - value in pixel without unit
/// @return { number } value in em. [16] is the common default font-size of HTML
@function emConverter($pixels) {
  @return ($pixels / 16) * 1em;
}

// NEW TOKENS
$tokens: (
  colors: (
    background: (
      main: #ffffff,
      alt: #f5f6f7,
      reversed: #373c47,
      brand: #428cc6,
      brand-alt: #e6edf5,
      accent: #428cc6,
      action: #ffffff,
      selectable: #ffffff,
      selected: #e6edf5,
      selected-high: #428cc6,
      info: #e4e9f9,
      info-high: #095ec8,
      success: #e4f1e7,
      success-high: #1b9c4e,
      warning: #fff1df,
      warning-high: #fda932,
      error: #f8e5e6,
      error-high: #c11e3d,
      notification: #d82244,
    ),
    content: (
      high: #373c47,
      medium: #60687a,
      low: #9fa5b6,
      reversed: #ffffff,
      accent: #ffffff,
      action: #428cc6,
      selectable: #373c47,
      selected: #428cc6,
      selected-high: #ffffff,
      info: #095ec8,
      success: #1b9c4e,
      warning: #fda932,
      error: #c11e3d,
      notification: #ffffff,
    ),
    border: (
      main: #d5d7dd,
      reversed: #ffffff,
      action: #cad8ea,
      selectable: #d5d7dd,
      selected: #cad8ea,
      info: #a1b5ed,
      success: #a3d2af,
      warning: #fdba5b,
      error: #e8a3aa,
    ),
  ),
  spacings: (
    xs: 0.4rem,
    sm: 0.8rem,
    md: 1.6rem,
    lg: 2.4rem,
    xl: 3.2rem,
    2xl: 4.8rem,
    3xl: 6.4rem,
    4xl: 8rem,
  ),
  radii: (
    sm: 0.4rem,
    md: 0.8rem,
    lg: 1.6rem,
    rounded: 99em,
  ),
  elevations: (
    fixed-top: (
      0 0 0.2rem 0 rgba(#373c47, 0.16),
      0 0.1rem 0.4rem 0 rgba(#373c47, 0.12),
    ),
    fixed-bottom: (
      0 0 0.2rem 0 rgba(#373c47, 0.16),
      0 -0.1rem 0.4rem 0 rgba(#373c47, 0.12),
    ),
    raised: (
      0 0.4rem 0.8rem 0 rgba(#373c47, 0.16),
      0 0.8rem 2.4rem 0 rgba(#373c47, 0.16),
    ),
    overlay: (
      0 0.8rem 6rem 0 rgba(#373c47, 0.16),
      0 1.2rem 2.4rem -0.4rem rgba(#373c47, 0.24),
    ),
  ),
  typography: (
    display-1: (
      font-size: 4rem,
      font-weight: 400,
      line-height: 5.6rem,
      letter-spacing: -0.15rem,
      desktop: (
        font-size: 4.8rem,
        line-height: 6.4rem,
      ),
    ),
    display-2: (
      font-size: 3.2rem,
      font-weight: 500,
      line-height: 4.8rem,
      letter-spacing: -0.1rem,
    ),
    display-subtitle: (
      font-size: 2rem,
      font-weight: 500,
      line-height: 2.4rem,
    ),
    title-1: (
      font-size: 2rem,
      font-weight: 600,
      line-height: 2.4rem,
      letter-spacing: -0.05rem,
      desktop: (
        font-size: 2.4rem,
        line-height: 3.2rem,
      ),
    ),
    title-2: (
      font-size: 1.6rem,
      font-weight: 600,
      line-height: 2.4rem,
      desktop: (
        letter-spacing: -0.05rem,
      ),
    ),
    body: (
      font-size: 1.6rem,
      font-weight: 400,
      line-height: 2.4rem,
    ),
    button: (
      font-size: 1.4rem,
      font-weight: 600,
      line-height: 1.6rem,
      text-transform: uppercase,
    ),
    body-small: (
      font-size: 1.4rem,
      font-weight: 400,
      line-height: 1.6rem,
    ),
    body-tiny: (
      font-size: 1.2rem,
      font-weight: 400,
      line-height: 1.6rem,
    ),
    overline: (
      font-size: 1.2rem,
      font-weight: 400,
      line-height: 1.6rem,
      text-transform: uppercase,
    ),
  ),
  breakpoints: (
    xs: emConverter(480),
    sm: emConverter(768),
    md: emConverter(1024),
    lg: emConverter(1280),
    xl: emConverter(1440),
  ),
  icon-sizes: (
    //unofficial
    xs: 1.6rem,
    sm: 2rem,
    md: 2.4rem,
  ),
  z-indexes: (
    //unofficial
    base: 1000,
    dropdown: 1200,
    head-section: 1400,
    loader-overlay: 1600,
    sidebar: 1800,
    navigation: 2000,
    modal: 2200,
    snackbar: 2400,
    popover: 2600,
    tooltip: 2800,
  ),
) !default;

@function useColorMap() {
  $map: ();
  @each $category, $sub in map.get($tokens, colors) {
    @each $key, $value in $sub {
      $map: map.set($map, #{$category}-#{$key}, $value);
    }
  }
  @return $map;
}

$colors: useColorMap();
$spacings: map.get($tokens, spacings);
$radii: map.get($tokens, radii);
$elevations: map.get($tokens, elevations);
$typography: map.get($tokens, typography);
$breakpoints: map.get($tokens, breakpoints);
$icon-sizes: map.get($tokens, icon-sizes);
$z-indexes: map.get($tokens, z-indexes);
