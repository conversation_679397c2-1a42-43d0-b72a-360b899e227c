import React from "react";
import clsx from "clsx";

export interface EnergeticTagProps {
  /**
   The score of the Energetic class
  */
  score: "a4" | "a3" | "a2" | "a1" | "a-plus" | "a" | "b" | "c" | "d" | "e" | "f" | "g"; 
  // "b-plus" | "gamma" | "delta" | "epsilon" | "zeta" | "eta" for Greece
   /**
  Custom class names
  */
  className?: any;
}

export const EnergeticTag: React.FC<EnergeticTagProps> = ({ score = "g" , className }) => {
  return (
    <div
      className={clsx(
        "gx-energetic-tag",
        score && `gx-energetic-tag--${score}`,
        className
      )}
    >
      {score === "a-plus" ? `A+` : `${score.toUpperCase()}`}
    
    </div>
  );
};
