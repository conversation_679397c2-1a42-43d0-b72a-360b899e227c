import React from "react";
import { render, screen } from "../../../test/utilities";
import { EnergeticTag } from "./EnergeticTag";
import { it, expect } from "vitest";

const styles = [
  "a4",
  "a3",
  "a2",
  "a1",
  "a-plus",
  "a",
  "b",
  "c",
  "d",
  "e",
  "f",
  "g",
];

styles.forEach((style) => {
  it(`renders the energetic tag with the correct style class for ${style}`, () => {
    render(<EnergeticTag score={style} />);
    const energeticTag = screen.getByText(style === "a-plus" ? `A+` : `${style.toUpperCase()}`);
    expect(energeticTag).toHaveClass(`gx-energetic-tag--${style}`);
  });
});

it("applies custom className if provided", () => {
  render(<EnergeticTag score="g" className="custom-class" />);
  const energeticTag = screen.getByText("G");
  expect(energeticTag).toHaveClass("custom-class");
});
