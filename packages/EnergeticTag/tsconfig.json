{"compilerOptions": {"pretty": true, "target": "es6", "lib": ["dom", "dom.iterable", "esnext"], "allowJs": true, "skipLibCheck": true, "strict": false, "forceConsistentCasingInFileNames": true, "noEmit": true, "esModuleInterop": true, "module": "esnext", "moduleResolution": "node", "resolveJsonModule": true, "isolatedModules": true, "jsx": "preserve", "downlevelIteration": true, "noUnusedLocals": true, "strictNullChecks": true, "noImplicitAny": true, "baseUrl": "."}, "exclude": ["dist", "node_modules"]}