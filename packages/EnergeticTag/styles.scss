// ==========================================================================
// Energetic Tag - Components
// ==========================================================================
@use "@gx-design/theme/styles" as *;

$energetic-classes: (
  a4: #4b6c01,
  a3: #5c8501,
  a2: #6e9e02,
  a1: #7fb802,
  a-plus: #6e9e02,
  a: #7fb802,
  b: #91d102,
  c: #ebc400,
  d: #eb9d00,
  e: #e67300,
  f: #d22300,
  g: #b80000,
  // for Greece
  // b-plus: #91d102,
  // gamma: #ebc400,
  // delta: #eb9d00,
  // epsilon: #e67300,
  // zeta: #d22300,
  // eta: #b80000, 
);

.gx-energetic-tag {
  position: relative;
  display: inline-flex;
  min-width: 2.4rem;
  align-items: center;
  justify-content: center;
  height: 2.4rem;
  padding: 0 space(xs);
  background-color: color(background-brand);
  color: color(content-accent) !important;
  border-radius: radius(sm) 0 0 radius(sm);
  @include typography(body-small);

  &:after {
    content: "";
    position: absolute;
    left: 100%;
    height: 0;
    border-style: solid;
    border-width: 1.2rem 0 1.2rem 1.2rem;
    border-color: transparent transparent transparent color(background-brand);
  }

  @each $energetic-class, $energetic-color in $energetic-classes {
    &--#{$energetic-class} {
      background-color: #{$energetic-color} !important;

      &:after {
        border-left-color: #{$energetic-color};
      }
    }
  }
}
