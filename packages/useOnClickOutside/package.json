{"name": "@gx-design/use-on-click-outside", "version": "3.0.2", "description": "Gx Design useOnClickOutside hook", "source": "src/useOnClickOutside.ts", "main": "dist/index.js", "module": "dist/index.mjs", "peerDependencies": {"react": "*"}, "devDependencies": {"tsup": "^7.2.0"}, "dependencies": {"clsx": "1.1.1"}, "scripts": {"build": "tsup", "watch": "tsup --watch"}, "repository": {"type": "git", "url": "********************:getrix/gx-design.git", "directory": "packages/useOnClickOutside"}, "typings": "dist/index.d.ts", "volta": {"extends": "../../package.json"}}