# Change Log

All notable changes to this project will be documented in this file.
See [Conventional Commits](https://conventionalcommits.org) for commit guidelines.

## [3.0.2](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/use-on-click-outside@3.0.1...@gx-design/use-on-click-outside@3.0.2) (2025-04-02)

**Note:** Version bump only for package @gx-design/use-on-click-outside

## [3.0.1](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/use-on-click-outside@3.0.0...@gx-design/use-on-click-outside@3.0.1) (2025-02-27)

**Note:** Version bump only for package @gx-design/use-on-click-outside

# [3.0.0](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/use-on-click-outside@2.1.1...@gx-design/use-on-click-outside@3.0.0) (2024-03-18)

- feat!: new architecture ([5572bfc](https://gitlab.pepita.io/getrix/gx-design/commit/5572bfc9c05f1cb4aa5a0e134d2771ac5d7243f0))

### BREAKING CHANGES

- new architecture

## [2.1.1](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/use-on-click-outside@2.1.0...@gx-design/use-on-click-outside@2.1.1) (2023-12-01)

**Note:** Version bump only for package @gx-design/use-on-click-outside

# [2.1.0](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/use-on-click-outside@2.0.2...@gx-design/use-on-click-outside@2.1.0) (2023-09-27)

### Features

- **storybook:** upgrade to 7 ([1c0e5e9](https://gitlab.pepita.io/getrix/gx-design/commit/1c0e5e941dcf7b841d1b5d2a2825f66f7921276e))

## [2.0.2](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/use-on-click-outside@2.0.1...@gx-design/use-on-click-outside@2.0.2) (2023-09-08)

**Note:** Version bump only for package @gx-design/use-on-click-outside

## 2.0.1 (2023-05-19)

**Note:** Version bump only for package @gx-design/use-on-click-outside
