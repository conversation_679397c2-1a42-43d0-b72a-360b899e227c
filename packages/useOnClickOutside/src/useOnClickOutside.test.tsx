import { afterEach, describe, expect, it, vi } from "vitest";
import { useOnClickOutside } from "./useOnClickOutside";
import { render, screen } from "../../../test/utilities";
import { useRef } from "react";

const SetupComponent = ({
  shouldListen = true,
  handler,
}: {
  shouldListen: boolean;
  handler: () => void;
}) => {
  const ref1 = useRef(null);
  const ref2 = useRef(null);

  useOnClickOutside({ refs: [ref1, ref2], handler, shouldListen });

  return (
    <div data-testid="container">
      <div ref={ref1} data-testid="ref1" />
      <div ref={ref2} data-testid="ref2" />
    </div>
  );
};
describe("useOnClickOutside", () => {
  afterEach(() => {
    vi.restoreAllMocks();
  });

  it("should call handler when clicking outside the ref", async () => {
    const handler = vi.fn();

    const { user } = render(
      <SetupComponent shouldListen={true} handler={handler} />
    );

    const outsideElement = screen.getByTestId("container");

    await user.click(outsideElement);

    expect(handler).toHaveBeenCalledTimes(1);
  });

  it("should not call handler when clicking inside the ref", async () => {
    const handler = vi.fn();

    const { user } = render(
      <SetupComponent shouldListen={true} handler={handler} />
    );

    const firstRef = screen.getByTestId("ref1");

    await user.click(firstRef);

    expect(handler).not.toHaveBeenCalled();
  });

  it("should not call handler when shouldListen is false", async () => {
    const handler = vi.fn();

    const { user } = render(
      <SetupComponent shouldListen={false} handler={handler} />
    );

    const outsideElement = screen.getByTestId("container");

    await user.click(outsideElement);

    expect(handler).not.toHaveBeenCalled();
  });

  it("should handle multiple refs", async () => {
    const handler = vi.fn();

    const { user } = render(
      <SetupComponent shouldListen={true} handler={handler} />
    );

    const firstRef = screen.getByTestId("ref1");
    const secondRef = screen.getByTestId("ref2");
    const outsideElement = screen.getByTestId("container");

    await user.click(firstRef);
    await user.click(secondRef);

    await user.click(outsideElement);
    expect(handler).toHaveBeenCalledTimes(1);
  });
});
