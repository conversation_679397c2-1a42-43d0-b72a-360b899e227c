import React, { useEffect } from "react";

export type UseOnClickOutsideInput = {
  refs: React.MutableRefObject<HTMLElement | null> | React.MutableRefObject<HTMLElement | null>[];
  handler: (event: MouseEvent | TouchEvent) => void;
  shouldListen?: boolean;
};

const CLICK_EVENTS = ["mousedown", "touchstart"] as const;

/**
 * Hook responsible to attach/remove an event listeners `mousedown` and `touchstart`, and invoke it when clicking outside the ref(s).
 * It can handle an array of refs; it can remove event listeners not only when component unmounts, but also through the `shouldListen` parameter.
 * @param refs a single ref or an array of refs to check for click inside/outside
 * @param handler the action to be invoked when clicking outside the ref(s)
 * @param shouldListen optional boolean which removes the event listener when false, useful to
 * enable event listener only when `true`; defaults to `true`
 */

export const useOnClickOutside = ({ refs, handler, shouldListen = true }: UseOnClickOutsideInput) => {
  useEffect(() => {
    const inputRefs = Array.isArray(refs) ? refs : [refs];

    const listener = (event: MouseEvent | TouchEvent) => {
      const target = ("composedPath" in event ? event.composedPath()[0] : (event as Event).target) as HTMLElement;
      const isClickInside = inputRefs.some((ref) => !ref.current || ref.current.contains(target));

      if (isClickInside) {
        return;
      }
      handler(event);
    };

    if (shouldListen) {
      CLICK_EVENTS.forEach((eventName) => document.addEventListener(eventName, listener));
    } else {
      CLICK_EVENTS.forEach((eventName) => document.removeEventListener(eventName, listener));
    }

    return () => {
      CLICK_EVENTS.forEach((eventName) => document.removeEventListener(eventName, listener));
    };
  }, [refs, handler, shouldListen]);
};
