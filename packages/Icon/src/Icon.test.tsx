import iconsSchema from "@pepita-fe/sprite-b2b/sprite.json";
import { describe, expect, it, vi } from "vitest";
import { render, screen } from "../../../test/utilities";
import { Icon, getAllIcons, getIconObject } from "./Icon";

it("should return all available icons", () => {
  const icons = getAllIcons(iconsSchema);

  for (const icon of icons) {
    expect(icon).toEqual(
      expect.objectContaining({
        attributes: expect.objectContaining({
          id: expect.any(String),
        }),
      })
    );
  }
});

it("should find by icon name and return the icon object", () => {
  const icons = getAllIcons(iconsSchema);
  const name = "align-vertical-bottom";
  const expectedIcon = { attributes: { id: "align-vertical-bottom" } };

  expect(getIconObject(icons, name)).toMatchObject(expectedIcon);
});

describe("should all icon-sets renders properly", () => {
  for (const icon of getAllIcons(iconsSchema)) {
    it(`should render the icon with name: ${icon.attributes.id}`, () => {
      const { baseElement } = render(<Icon name={icon.attributes.id} />);

      const element = baseElement.querySelector(".gx-icon");

      expect(element).toBeInTheDocument();
      expect(element?.children.length).toBeGreaterThanOrEqual(1);
    });
  }
});

it("should render the icon and be clickable", async () => {
  const fakeClick = vi.fn();
  const { user } = render(
    <Icon name="home--active" aria-label="home" onClick={fakeClick} />
  );

  const element = screen.getByRole("button", { name: "home" });
  await user.click(element);

  expect(fakeClick).toHaveBeenCalled();
  expect(element).toBeInTheDocument();
  expect(element?.children.length).toBeGreaterThanOrEqual(1);
});

it("should render the svg icon but empty (this is the actual behaviour but could be improved)", async () => {
  const fakeClick = vi.fn();
  const notExistingIconName = "an-icon-name-that-not-exists";
  const { user } = render(
    <Icon
      aria-label="not-exist"
      name={notExistingIconName as any}
      onClick={fakeClick}
    />
  );

  const element = screen.getByRole("button", { name: "not-exist" });

  await user.click(element);

  expect(fakeClick).toHaveBeenCalledTimes(1);

  expect(element?.children).toHaveLength(0);
});
