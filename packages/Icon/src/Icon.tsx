import React, { JSX } from "react";
import clsx from "clsx";
import iconsSchema from "@pepita-fe/sprite-b2b/sprite.json";
import { IconName } from "@pepita-fe/sprite-b2b/sprite";

// Props
export type IconProps = {
  /**
   Sprite
  */
  sprite?: any;
  /**
   The icon name, picked from the library
  */
  name: IconName;
  /**
   Custom class name
  */
  className?: string;
  /**
   The onClick handler
  */
  onClick?: React.MouseEventHandler<SVGSVGElement>;
  /**
   * Aria label
   */
  "aria-label"?: string;
  /**
   * Test id
   */
  "data-testid"?: string;
} & JSX.IntrinsicElements["svg"];

export const getAllIcons = (sprite: any) => {
  return sprite.children.filter((x: any) => x.name === "symbol");
};

export const getIconObject = (icons: any[], name: string) => {
  return icons.find((x: any) => x.attributes.id === name);
};

// Component
export const Icon = React.forwardRef<SVGSVGElement, IconProps>(
  ({ sprite = iconsSchema, name, className, onClick, ...props }, ref) => {
    const icons = getAllIcons(sprite);
    const iconObj = getIconObject(icons, name);

    return (
      <svg
        ref={ref}
        viewBox="0 0 24 24"
        width="24"
        height="24"
        className={clsx("gx-icon", className)}
        role={onClick ? "button" : undefined}
        onClick={onClick && onClick}
        aria-label={props["aria-label"]}
        data-testid={props["data-testid"]}
        {...props}
      >
        {iconObj?.children.map((icon: any, i: number) => {
          const As = icon.name;
          const attrs = icon.attributes;

          return <As {...attrs} fillRule="evenodd" key={i} />;
        })}
      </svg>
    );
  }
);

Icon.displayName = "Icon";
