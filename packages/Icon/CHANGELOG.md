# Change Log

All notable changes to this project will be documented in this file.
See [Conventional Commits](https://conventionalcommits.org) for commit guidelines.

## [5.6.3](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/icon@5.6.2...@gx-design/icon@5.6.3) (2025-07-17)

**Note:** Version bump only for package @gx-design/icon

## [5.6.2](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/icon@5.6.1...@gx-design/icon@5.6.2) (2025-07-09)

**Note:** Version bump only for package @gx-design/icon

## [5.6.1](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/icon@5.6.0...@gx-design/icon@5.6.1) (2025-07-07)

**Note:** Version bump only for package @gx-design/icon

# [5.6.0](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/icon@5.5.7...@gx-design/icon@5.6.0) (2025-07-04)

### Features

- **lemon-social-icon-update:** updated package pepita-fe/sprite-b2b ([dbcd08e](https://gitlab.pepita.io/getrix/gx-design/commit/dbcd08efc995c5851a98c12d44ef4db9278503af))

## [5.5.7](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/icon@5.5.6...@gx-design/icon@5.5.7) (2025-06-09)

**Note:** Version bump only for package @gx-design/icon

## [5.5.6](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/icon@5.5.5...@gx-design/icon@5.5.6) (2025-05-30)

**Note:** Version bump only for package @gx-design/icon

## [5.5.5](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/icon@5.5.4...@gx-design/icon@5.5.5) (2025-05-23)

### Bug Fixes

- **chip:** icon size and update sprite ([1f1739a](https://gitlab.pepita.io/getrix/gx-design/commit/1f1739a0349eebc1b19ba6e25446581d828eaacc))

## [5.5.4](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/icon@5.5.3...@gx-design/icon@5.5.4) (2025-05-16)

**Note:** Version bump only for package @gx-design/icon

## [5.5.3](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/icon@5.5.2...@gx-design/icon@5.5.3) (2025-05-14)

**Note:** Version bump only for package @gx-design/icon

## [5.5.2](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/icon@5.5.1...@gx-design/icon@5.5.2) (2025-04-16)

**Note:** Version bump only for package @gx-design/icon

## [5.5.1](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/icon@5.5.0...@gx-design/icon@5.5.1) (2025-02-14)

**Note:** Version bump only for package @gx-design/icon

# [5.5.0](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/icon@5.4.1...@gx-design/icon@5.5.0) (2025-02-11)

### Features

- **label:** new component ([e773c54](https://gitlab.pepita.io/getrix/gx-design/commit/e773c549d3af67dd97d96f9a71d3a4db3d8a151f))

## [5.4.1](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/icon@5.4.0...@gx-design/icon@5.4.1) (2024-10-31)

**Note:** Version bump only for package @gx-design/icon

# [5.4.0](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/icon@5.3.0...@gx-design/icon@5.4.0) (2024-09-23)

### Features

- **css:** custom properties ([cbc56c0](https://gitlab.pepita.io/getrix/gx-design/commit/cbc56c0e3e675d85f92ad25ac135cce270f02b2b))

## [5.3.1-alpha.0](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/icon@5.3.0...@gx-design/icon@5.3.1-alpha.0) (2024-09-16)

**Note:** Version bump only for package @gx-design/icon

# [5.3.0](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/icon@5.2.4...@gx-design/icon@5.3.0) (2024-08-29)

### Features

- **colors:** pro new colors ([2103ea7](https://gitlab.pepita.io/getrix/gx-design/commit/2103ea76597b5ae75018d2d62947c6b01d613e82))

# [5.3.0-alpha.0](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/icon@5.2.3...@gx-design/icon@5.3.0-alpha.0) (2024-07-26)

### Features

- **colors:** pro new colors ([d88639f](https://gitlab.pepita.io/getrix/gx-design/commit/d88639f983895257b905dfad2fb65db3e75efee0))

## [5.2.4-alpha.0](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/icon@5.2.3...@gx-design/icon@5.2.4-alpha.0) (2024-07-22)

**Note:** Version bump only for package @gx-design/icon

## [5.2.3](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/icon@5.2.2...@gx-design/icon@5.2.3) (2024-07-15)

**Note:** Version bump only for package @gx-design/icon

## [5.2.2](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/icon@5.2.1...@gx-design/icon@5.2.2) (2024-06-14)

**Note:** Version bump only for package @gx-design/icon

## [5.2.1](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/icon@5.2.0...@gx-design/icon@5.2.1) (2024-06-05)

### Bug Fixes

- **checkbox:** corner case ([e1a0a72](https://gitlab.pepita.io/getrix/gx-design/commit/e1a0a72d35edeb01d09f70196bcd76487bd4d2f8))

# [5.2.0](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/icon@5.1.2...@gx-design/icon@5.2.0) (2024-06-03)

### Features

- **icons:** update sprite with lens icons ([73cb85a](https://gitlab.pepita.io/getrix/gx-design/commit/73cb85a1391f949be57fc3fac6449936bb7aa846))

## [5.1.2](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/icon@5.1.1...@gx-design/icon@5.1.2) (2024-05-28)

**Note:** Version bump only for package @gx-design/icon

## [5.1.1](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/icon@5.1.0...@gx-design/icon@5.1.1) (2024-05-16)

**Note:** Version bump only for package @gx-design/icon

# [5.1.0](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/icon@5.0.0...@gx-design/icon@5.1.0) (2024-05-06)

### Features

- **icons:** add new icons for matches ([ba9e7e9](https://gitlab.pepita.io/getrix/gx-design/commit/ba9e7e945ce893d3811fa074e02807b1f2d5560a))

# [5.0.0](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/icon@4.0.1...@gx-design/icon@5.0.0) (2024-03-26)

### Features

- **button:** style and variant changes ([1fbe519](https://gitlab.pepita.io/getrix/gx-design/commit/1fbe519808701c8a752b6ec8b36fff8a0bd82a9b))

### BREAKING CHANGES

- **button:** button style to variant

# [4.1.0-alpha.0](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/icon@4.0.1...@gx-design/icon@4.1.0-alpha.0) (2024-03-25)

### Features

- **button:** style and variant changes ([37c6c2b](https://gitlab.pepita.io/getrix/gx-design/commit/37c6c2b61fcbe48add378261292c61c60f918e60))

## [4.0.1](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/icon@4.0.0...@gx-design/icon@4.0.1) (2024-03-20)

**Note:** Version bump only for package @gx-design/icon

# [4.0.0](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/icon@3.5.0...@gx-design/icon@4.0.0) (2024-03-18)

- feat!: new architecture ([5572bfc](https://gitlab.pepita.io/getrix/gx-design/commit/5572bfc9c05f1cb4aa5a0e134d2771ac5d7243f0))

### BREAKING CHANGES

- new architecture

# [3.5.0](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/icon@3.4.0...@gx-design/icon@3.5.0) (2024-03-18)

**Note:** Version bump only for package @gx-design/icon

# [3.5.0-alpha.2](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/icon@3.4.0...@gx-design/icon@3.5.0-alpha.2) (2024-03-07)

### Features

- **scss:** alert and theme styles ([7c0102d](https://gitlab.pepita.io/getrix/gx-design/commit/7c0102d309fce10724944b1e2e355d391d84ebe7))

# [3.5.0-alpha.1](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/icon@3.5.0-alpha.0...@gx-design/icon@3.5.0-alpha.1) (2024-02-29)

**Note:** Version bump only for package @gx-design/icon

# [3.5.0-alpha.0](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/icon@3.4.0...@gx-design/icon@3.5.0-alpha.0) (2024-02-28)

### Features

- **scss:** alert and theme styles ([d104cb9](https://gitlab.pepita.io/getrix/gx-design/commit/d104cb95f68efd2014cb0cda3105e9b9744e1b36))

# [3.4.0](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/icon@3.3.5...@gx-design/icon@3.4.0) (2024-02-27)

### Features

- **icon:** add triangle icons ([beb3447](https://gitlab.pepita.io/getrix/gx-design/commit/beb34478fa4703426f113e85280c4ec793fc068f))

## [3.3.5](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/icon@3.3.4...@gx-design/icon@3.3.5) (2024-01-31)

**Note:** Version bump only for package @gx-design/icon

## [3.3.4](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/icon@3.3.3...@gx-design/icon@3.3.4) (2024-01-12)

**Note:** Version bump only for package @gx-design/icon

## [3.3.3](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/icon@3.3.2...@gx-design/icon@3.3.3) (2023-12-15)

**Note:** Version bump only for package @gx-design/icon

## [3.3.2](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/icon@3.3.1...@gx-design/icon@3.3.2) (2023-12-01)

**Note:** Version bump only for package @gx-design/icon

## [3.3.1](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/icon@3.3.0...@gx-design/icon@3.3.1) (2023-11-27)

**Note:** Version bump only for package @gx-design/icon

## [3.3.1-alpha.0](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/icon@3.3.0...@gx-design/icon@3.3.1-alpha.0) (2023-11-27)

**Note:** Version bump only for package @gx-design/icon

# [3.3.0](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/icon@3.2.7...@gx-design/icon@3.3.0) (2023-11-06)

### Features

- **icon:** add tests ([ca1db69](https://gitlab.pepita.io/getrix/gx-design/commit/ca1db690b8524fe435a118c5e231341a0ebdbb6c))

## [3.2.7](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/icon@3.2.6...@gx-design/icon@3.2.7) (2023-10-27)

**Note:** Version bump only for package @gx-design/icon

## [3.2.6](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/icon@3.2.5...@gx-design/icon@3.2.6) (2023-10-23)

**Note:** Version bump only for package @gx-design/icon

## [3.2.5](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/icon@3.2.4...@gx-design/icon@3.2.5) (2023-09-08)

**Note:** Version bump only for package @gx-design/icon

## [3.2.4](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/icon@3.2.3...@gx-design/icon@3.2.4) (2023-08-28)

**Note:** Version bump only for package @gx-design/icon

## [3.2.3](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/icon@3.2.2...@gx-design/icon@3.2.3) (2023-08-23)

**Note:** Version bump only for package @gx-design/icon

## [3.2.2](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/icon@3.2.1...@gx-design/icon@3.2.2) (2023-07-27)

**Note:** Version bump only for package @gx-design/icon

## [3.2.1](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/icon@3.2.0...@gx-design/icon@3.2.1) (2023-07-26)

**Note:** Version bump only for package @gx-design/icon

# [3.2.0](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/icon@3.1.6...@gx-design/icon@3.2.0) (2023-07-17)

**Note:** Version bump only for package @gx-design/icon

# [3.2.0-alpha.1](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/icon@3.2.0-alpha.0...@gx-design/icon@3.2.0-alpha.1) (2023-07-12)

**Note:** Version bump only for package @gx-design/icon

# [3.2.0-alpha.0](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/icon@3.1.6...@gx-design/icon@3.2.0-alpha.0) (2023-07-12)

### Features

- **token:** foundation e common ([0a7fd4a](https://gitlab.pepita.io/getrix/gx-design/commit/0a7fd4a71fa56887d62cb56e4916c414d2ba12c8))

## [3.1.6](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/icon@3.1.5...@gx-design/icon@3.1.6) (2023-06-22)

**Note:** Version bump only for package @gx-design/icon

## [3.1.5](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/icon@3.1.4...@gx-design/icon@3.1.5) (2023-06-19)

**Note:** Version bump only for package @gx-design/icon

## [3.1.4](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/icon@3.1.3...@gx-design/icon@3.1.4) (2023-05-30)

**Note:** Version bump only for package @gx-design/icon

## [3.1.3](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/icon@3.1.2...@gx-design/icon@3.1.3) (2023-05-16)

**Note:** Version bump only for package @gx-design/icon

## [3.1.2](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/icon@3.1.1...@gx-design/icon@3.1.2) (2023-05-15)

**Note:** Version bump only for package @gx-design/icon

## [3.1.1](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/icon@3.1.0...@gx-design/icon@3.1.1) (2023-05-15)

**Note:** Version bump only for package @gx-design/icon

# [3.1.0](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/icon@3.0.2...@gx-design/icon@3.1.0) (2023-05-11)

### Features

- **icon:** update icon-set e fix componenti ([7154ee0](https://gitlab.pepita.io/getrix/gx-design/commit/7154ee0675a224385d543e7d17123692c1bc499a))

## [3.0.2](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/icon@3.0.1...@gx-design/icon@3.0.2) (2023-05-10)

**Note:** Version bump only for package @gx-design/icon

## [3.0.2-alpha.0](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/icon@3.0.1...@gx-design/icon@3.0.2-alpha.0) (2023-05-10)

**Note:** Version bump only for package @gx-design/icon

## [3.0.1](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/icon@3.0.0...@gx-design/icon@3.0.1) (2023-05-03)

### Bug Fixes

- **icon-set:** update ([d211172](https://gitlab.pepita.io/getrix/gx-design/commit/d2111720beeac437530ccdbeabef5cf8f9af7e67))

# [3.0.0](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/icon@2.0.13...@gx-design/icon@3.0.0) (2023-04-13)

### Features

- **icons:** nuove icone ([f444215](https://gitlab.pepita.io/getrix/gx-design/commit/f44421598355b8d9de903a81f2ca6232887f780c))

### BREAKING CHANGES

- **icons:** changed icon sprite

# [3.0.0-alpha.0](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/icon@2.0.13...@gx-design/icon@3.0.0-alpha.0) (2023-04-05)

### Features

- **icons:** nuove icone ([6528540](https://gitlab.pepita.io/getrix/gx-design/commit/65285405393efb34dfdcc4329d99dd1ef41d70c1))

### BREAKING CHANGES

- **icons:** changed icon sprite

## [2.0.14-alpha.1](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/icon@2.0.14-alpha.0...@gx-design/icon@2.0.14-alpha.1) (2023-03-29)

**Note:** Version bump only for package @gx-design/icon

## [2.0.14-alpha.0](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/icon@2.0.13...@gx-design/icon@2.0.14-alpha.0) (2023-03-29)

**Note:** Version bump only for package @gx-design/icon

## [2.0.13](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/icon@2.0.12...@gx-design/icon@2.0.13) (2023-03-20)

**Note:** Version bump only for package @gx-design/icon

## [2.0.12](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/icon@2.0.11...@gx-design/icon@2.0.12) (2023-03-07)

**Note:** Version bump only for package @gx-design/icon

## [2.0.11](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/icon@2.0.10...@gx-design/icon@2.0.11) (2023-03-06)

**Note:** Version bump only for package @gx-design/icon

## [2.0.10](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/icon@2.0.9...@gx-design/icon@2.0.10) (2023-02-27)

**Note:** Version bump only for package @gx-design/icon

## [2.0.9](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/icon@2.0.8...@gx-design/icon@2.0.9) (2023-02-21)

**Note:** Version bump only for package @gx-design/icon

## [2.0.8](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/icon@2.0.7...@gx-design/icon@2.0.8) (2023-01-30)

**Note:** Version bump only for package @gx-design/icon

## [2.0.7](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/icon@2.0.6...@gx-design/icon@2.0.7) (2022-12-21)

**Note:** Version bump only for package @gx-design/icon

## [2.0.6](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/icon@2.0.5...@gx-design/icon@2.0.6) (2022-11-22)

**Note:** Version bump only for package @gx-design/icon

## [2.0.6-alpha.1](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/icon@2.0.6-alpha.0...@gx-design/icon@2.0.6-alpha.1) (2022-11-21)

**Note:** Version bump only for package @gx-design/icon

## [2.0.6-alpha.0](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/icon@2.0.5...@gx-design/icon@2.0.6-alpha.0) (2022-11-21)

**Note:** Version bump only for package @gx-design/icon

## [2.0.5](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/icon@2.0.5-alpha.0...@gx-design/icon@2.0.5) (2022-10-12)

### Bug Fixes

- **notification:** fix notify ([213000c](https://gitlab.pepita.io/getrix/gx-design/commit/213000c8bc11b83eb5d9ff401e51d57d76411548))

## [2.0.5-alpha.2](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/icon@2.0.5-alpha.0...@gx-design/icon@2.0.5-alpha.2) (2022-10-12)

### Bug Fixes

- **notification:** fix notify ([789498a](https://gitlab.pepita.io/getrix/gx-design/commit/789498ae821b217be4d078c71bd053221e529648))
- **notification:** fix notify ([766047c](https://gitlab.pepita.io/getrix/gx-design/commit/766047c45b4ebff6e9aa8985fc926e76a0fd59c0))
- **notification:** fix notify ([5d949cd](https://gitlab.pepita.io/getrix/gx-design/commit/5d949cd42a3e78c3c63ba78aa1d1f4595e6e3397))

## [2.0.5-alpha.0](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/icon@2.0.4...@gx-design/icon@2.0.5-alpha.0) (2022-10-10)

**Note:** Version bump only for package @gx-design/icon

## [2.0.4](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/icon@2.0.3...@gx-design/icon@2.0.4) (2022-09-19)

**Note:** Version bump only for package @gx-design/icon

## [2.0.3](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/icon@2.0.2...@gx-design/icon@2.0.3) (2022-09-07)

**Note:** Version bump only for package @gx-design/icon

## [2.0.2](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/icon@2.0.1...@gx-design/icon@2.0.2) (2022-08-24)

### Bug Fixes

- **ci:** pipeline ([4844b50](https://gitlab.pepita.io/getrix/gx-design/commit/4844b50e391c8e3f05e63e63cc7059c890974a04))

## [2.0.1](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/icon@2.0.1-alpha.4...@gx-design/icon@2.0.1) (2022-08-24)

### Bug Fixes

- **icon-tooltip:** version bump ([ee32e3e](https://gitlab.pepita.io/getrix/gx-design/commit/ee32e3ed6174fd91f540f1a977a40e78f682ccb6))

## [2.0.1-alpha.6](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/icon@2.0.1-alpha.5...@gx-design/icon@2.0.1-alpha.6) (2022-08-22)

**Note:** Version bump only for package @gx-design/icon

## [2.0.1-alpha.5](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/icon@2.0.1-alpha.4...@gx-design/icon@2.0.1-alpha.5) (2022-08-22)

**Note:** Version bump only for package @gx-design/icon

## [2.0.1-alpha.4](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/icon@2.0.1-alpha.3...@gx-design/icon@2.0.1-alpha.4) (2022-08-02)

### Bug Fixes

- **release:** pepita-command ([759db81](https://gitlab.pepita.io/getrix/gx-design/commit/759db814e4a8b790526eaaba5633cbfeee6fddc4))
- **release:** pepita-command ([0c2903a](https://gitlab.pepita.io/getrix/gx-design/commit/0c2903a346dad3f793e3091c741d415e1d02fb00))

## [2.0.1-alpha.3](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/icon@2.0.1-alpha.2...@gx-design/icon@2.0.1-alpha.3) (2022-08-02)

### Bug Fixes

- **release:** pepita-command ([6654ba2](https://gitlab.pepita.io/getrix/gx-design/commit/6654ba27f362a439bdcf991079596ccc8a4264f5))

## [2.0.1-alpha.2](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/icon@2.0.1-alpha.1...@gx-design/icon@2.0.1-alpha.2) (2022-08-01)

**Note:** Version bump only for package @gx-design/icon

## [2.0.1-alpha.1](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/icon@2.0.1-alpha.0...@gx-design/icon@2.0.1-alpha.1) (2022-08-01)

**Note:** Version bump only for package @gx-design/icon

## [2.0.1-alpha.0](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/icon@2.0.0...@gx-design/icon@2.0.1-alpha.0) (2022-07-27)

### Bug Fixes

- **ci:** ci fix ([2077d96](https://gitlab.pepita.io/getrix/gx-design/commit/2077d964c372bf1e517b9431716db357119ebfc8))

# 2.0.0 (2022-07-18)

# 2.0.0-alpha.15 (2022-07-12)

# 2.0.0-alpha.14 (2022-07-08)

# 2.0.0-alpha.13 (2022-07-06)

# 2.0.0-alpha.12 (2022-07-06)

**Note:** Version bump only for package @gx-design/icon

# [2.0.0-alpha.15](https://gitlab.pepita.io/getrix/gx-design/compare/v2.0.0-alpha.14...v2.0.0-alpha.15) (2022-07-12)

**Note:** Version bump only for package @gx-design/icon

# [2.0.0-alpha.14](https://gitlab.pepita.io/getrix/gx-design/compare/v2.0.0-alpha.13...v2.0.0-alpha.14) (2022-07-08)

**Note:** Version bump only for package @gx-design/icon

# [2.0.0-alpha.13](https://gitlab.pepita.io/getrix/gx-design/compare/v2.0.0-alpha.12...v2.0.0-alpha.13) (2022-07-06)

**Note:** Version bump only for package @gx-design/icon

# [2.0.0-alpha.12](https://gitlab.pepita.io/getrix/gx-design/compare/v1.2.3...v2.0.0-alpha.12) (2022-07-06)

**Note:** Version bump only for package @gx-design/icon

# [2.0.0-alpha.11](https://gitlab.pepita.io/getrix/gx-design/compare/v2.0.0-alpha.10...v2.0.0-alpha.11) (2022-06-23)

**Note:** Version bump only for package @gx-design/icon

# [2.0.0-alpha.10](https://gitlab.pepita.io/getrix/gx-design/compare/v2.0.0-alpha.9...v2.0.0-alpha.10) (2022-06-23)

**Note:** Version bump only for package @gx-design/icon

# [2.0.0-alpha.7](https://gitlab.pepita.io/getrix/gx-design/compare/v2.0.0-alpha.6...v2.0.0-alpha.7) (2022-06-22)

**Note:** Version bump only for package @gx-design/icon

# [2.0.0-alpha.6](https://gitlab.pepita.io/getrix/gx-design/compare/v2.0.0-alpha.5...v2.0.0-alpha.6) (2022-06-22)

**Note:** Version bump only for package @gx-design/icon

# [2.0.0-alpha.5](https://gitlab.pepita.io/getrix/gx-design/compare/v1.2.3...v2.0.0-alpha.5) (2022-06-22)

### Features

- lerna ([d2acfcb](https://gitlab.pepita.io/getrix/gx-design/commit/d2acfcb44f9ffb8b0044362be8d367721ae19dee))

### BREAKING CHANGES

- adds lerna repo management

# [2.0.0-alpha.4](https://gitlab.pepita.io/getrix/gx-design/compare/v1.2.3...v2.0.0-alpha.4) (2022-06-15)

### Features

- lerna ([29b12e6](https://gitlab.pepita.io/getrix/gx-design/commit/29b12e6e7d3f245d410915de3ceb7f90248f21b4))

### BREAKING CHANGES

- adds lerna repo management

# [2.0.0-alpha.3](https://gitlab.pepita.io/getrix/gx-design/compare/v2.0.0-alpha.2...v2.0.0-alpha.3) (2022-06-15)

### Features

- pretend to have a feat ([1c25c5c](https://gitlab.pepita.io/getrix/gx-design/commit/1c25c5c0bbfadb3ce0429d6bc9a50e1e504d5e12))

# [2.0.0-alpha.2](https://gitlab.pepita.io/getrix/gx-design/compare/v2.0.0-alpha.1...v2.0.0-alpha.2) (2022-06-15)

### Features

- pretend to have a feat ([35a3308](https://gitlab.pepita.io/getrix/gx-design/commit/35a330807b8f12c0c25f09fd8abee6cdb191fa78))

# [2.0.0-alpha.1](https://gitlab.pepita.io/getrix/gx-design/compare/v2.0.0-alpha.0...v2.0.0-alpha.1) (2022-06-15)

### Features

- remove yarn ([e62e6b4](https://gitlab.pepita.io/getrix/gx-design/commit/e62e6b44541dd938221ced63c13dc9b64b18ee7f))

# [2.0.0-alpha.0](https://gitlab.pepita.io/getrix/gx-design/compare/v1.2.3...v2.0.0-alpha.0) (2022-06-15)

### Features

- lerna ([6612f21](https://gitlab.pepita.io/getrix/gx-design/commit/6612f2107c71b52281b47052b3e994939cc0efe7))

### BREAKING CHANGES

- adds lerna repo management
