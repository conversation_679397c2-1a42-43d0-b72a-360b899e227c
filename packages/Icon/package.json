{"name": "@gx-design/icon", "version": "5.6.3", "description": "Gx Design Icon component", "source": "src/Icon.tsx", "main": "dist/index.js", "module": "dist/index.mjs", "peerDependencies": {"react": "*"}, "devDependencies": {"tsup": "^7.2.0"}, "dependencies": {"@gx-design/theme": "^1.4.0", "@pepita-fe/sprite-b2b": "^1.17.0", "clsx": "1.1.1"}, "scripts": {"build": "tsup", "watch": "tsup --watch"}, "repository": {"type": "git", "url": "********************:getrix/gx-design.git", "directory": "packages/Icon"}, "typings": "dist/index.d.ts", "volta": {"extends": "../../package.json"}}