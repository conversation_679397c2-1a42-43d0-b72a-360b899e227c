// ==========================================================================
// Icon - Components
// ==========================================================================
@use "@gx-design/theme/styles" as *;

// Colors map
$gx-icon-colors: (
  info: content-info,
  success: content-success,
  warning: content-warning,
  error: content-error,
);

.gx-icon {
  width: 1em;
  height: 1em;
  max-width: 100%;
  max-height: 100%;
  display: inline-block;
  vertical-align: middle;
  fill: currentColor;
  flex-shrink: 0;

  use {
    display: block;
    width: 100%;
    height: auto;
    margin: auto;
    fill: currentColor;
    overflow: hidden;
  }

  @each $gx-icon-typology, $color in $gx-icon-colors {
    &--#{$gx-icon-typology} {
      color: color(#{$color});
    }
  }
}

// container per icone con Tooltip o Popover
.gx-tip {
  display: inline-flex;
  font-size: 2rem;
}
