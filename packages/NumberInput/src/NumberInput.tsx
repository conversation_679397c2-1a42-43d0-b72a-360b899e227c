import React, {
  InputHTMLAttributes,
  forwardRef,
  ForwardRefRenderFunction,
} from "react";
import clsx from "clsx";
import { Tooltip } from "@gx-design/tooltip";
import { Button } from "@gx-design/button";
import { Icon } from "@gx-design/icon";
import { HelperText } from "@gx-design/helper-text";

export type NumberInputProps = {
  /**
   * The label title
   */
  label: string;
  /**
   * The tooltip helper text
   */
  tooltipHelper?: string;
  /**
   * Determine if label is visible to user
   */
  isLabelVisible?: boolean;
  /**
   * The error message
   */
  error?: string;
  /**
   * The event fired when user clicks on minus
   */
  onMinusClick: any;
  /**
   * The event fired when user clicks on plus
   */
  onPlusClick: any;
} & InputHTMLAttributes<HTMLInputElement>;

const NumberInputComponent: ForwardRefRenderFunction<
  HTMLInputElement,
  NumberInputProps
> = (
  {
    label,
    tooltipHelper,
    isLabelVisible = true,
    error,
    onMinusClick,
    onPlusClick,
    ...props
  },
  ref
) => {
  return (
    <div className="gx-input-wrapper">
      <label
        className={clsx("gx-label", {
          "gx-sr-only": !isLabelVisible,
        })}
      >
        {label}
        {props.required && <span className="gx-label__required">*</span>}
        {tooltipHelper && (
          <span className="gx-tip">
            <Tooltip position="top" text={tooltipHelper}>
              <Icon className="gx-icon--info" name="info-circle" />
            </Tooltip>
          </span>
        )}
      </label>
      <div className="gx-input-number-wrapper">
        <Button
          iconOnly
          disabled={props.disabled}
          onClick={!props.disabled && onMinusClick ? onMinusClick : null}
        >
          <Icon name="minus" />
        </Button>
        <input
          ref={ref}
          type="number"
          className={clsx("gx-input gx-input--withNumber", {
            "gx-input--negative": error,
          })}
          {...props}
        />
        <Button
          iconOnly
          disabled={props.disabled}
          onClick={!props.disabled && onPlusClick ? onPlusClick : null}
        >
          <Icon name="plus" />
        </Button>
      </div>
      {error && <HelperText text={error} style="error" />}
    </div>
  );
};

export const NumberInput = forwardRef(NumberInputComponent);
