html {
  font-size: 62.5%;
}
/*
  1. Use a more-intuitive box-sizing model.
*/
*,
*::before,
*::after {
  box-sizing: border-box;
}

/*
  2. Remove default margin
*/
* {
  margin: 0;
}

table {
  border-collapse: collapse;
  border-spacing: 0;
}

/*
  Typographic tweaks!
  4. Add accessible line-height
  5. Improve text rendering
*/
// TODO: da capire se importare ovunque
body {
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/*
  6. Improve media defaults
*/
img,
picture,
video,
canvas,
svg {
  display: block;
}

/*
  7. Remove built-in form typography styles
*/
input,
button,
textarea,
select {
  font: inherit;
}

/*
  8. Avoid text overflows
*/
p,
h1,
h2,
h3,
h4,
h5,
h6 {
  overflow-wrap: break-word;
}

ul {
  list-style: none;
  padding-left: 0;
}
