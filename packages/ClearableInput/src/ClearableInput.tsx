import React, {
  useRef,
  useEffect,
  InputHTMLAttributes,
  forwardRef,
  ForwardRefRenderFunction,
} from "react";
import { HelperText } from "@gx-design/helper-text";
import { Icon } from "@gx-design/icon";
import { Tooltip } from "@gx-design/tooltip";
import clsx from "clsx";

export type ClearableInputProps = {
  /**
   * The label title
   */
  label: string;
  /**
   * The tooltip helper text
   */
  tooltipHelper?: string;
  /**
   * Determine if label is visible to user
   */
  isLabelVisible?: boolean;
  /**
   * The error message
   */
  error?: string;
  /**
   * The onClearClick event
   */
  onClearClick: any;
} & InputHTMLAttributes<HTMLInputElement>;

const ClearableInputComponent: ForwardRefRenderFunction<
  HTMLInputElement,
  ClearableInputProps
> = (
  {
    label,
    tooltipHelper,
    isLabelVisible = true,
    onClearClick,
    error = "",
    ...props
  },
  ref
) => {
  const clearButton = useRef<HTMLButtonElement>(null);

  useEffect(() => {
    if (props.value !== "" && clearButton.current) {
      clearButton.current.style.display = "flex";
    } else if (props.value === "" && clearButton.current) {
      clearButton.current.style.display = "none";
    }
  }, [props.value]);

  return (
    <div className="gx-input-wrapper">
      <label
        htmlFor={props.id || undefined}
        className={clsx("gx-label", {
          "gx-sr-only": !isLabelVisible,
        })}
      >
        {label}
        {props.required && <span className="gx-label__required">*</span>}
        {tooltipHelper && (
          <span className="gx-tip">
            <Tooltip position="top" text={tooltipHelper}>
              <Icon className="gx-icon--info" name="info-circle" />
            </Tooltip>
          </span>
        )}
      </label>
      <div className="gx-input-clear-wrapper">
        <input
          ref={ref}
          className={clsx("gx-input", {
            "gx-input--negative": error,
          })}
          {...props}
        />
        <button
          ref={clearButton}
          onClick={onClearClick}
          className="gx-input-clear gx-input-clear--circled"
        >
          <Icon name="cross-circle--active" />
        </button>
      </div>
      {error && <HelperText text={error} style="error" />}
    </div>
  );
};

export const ClearableInput = forwardRef(ClearableInputComponent);
