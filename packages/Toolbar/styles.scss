// ==========================================================================
// Pagination Bar - Components/Toolbar
// ==========================================================================
@use "@gx-design/theme/styles" as *;
@use "@gx-design/tools/styles" as *;

.gx-toolbar {
  &__nav {
    padding: space(sm) space(md);
    border-bottom: 0.1rem solid color(border-main);
  }

  &__actions {
    display: flex;
    justify-content: space-between;
    padding: space(sm) space(md);
    border-bottom: 0.1rem solid color(border-main);
  }

  &__searchInputWrap {
    padding: space(sm) space(md);

    .gx-input-wrapper {
      flex-grow: 1;
    }
  }

  &__searchBar,
  &__openFilters {
    display: flex;
    align-items: center;
    flex-grow: 1;
  }

  &__searchBar {
    &Number {
      @include typography(body-small);
      flex-shrink: 0;
    }
  }

  &__openFilters {
    justify-content: flex-end;
    gap: space(sm);

    .gx-button + .gx-button {
      margin-left:0;
    }
  }

  &__searchResults {
    display: flex;
    justify-content: space-between;
    align-items: center;
    background-color: color(background-brand-alt);
    padding: space(sm) space(md);

    span {
      color: color(content-action);
    }
  }

  .gx-input-wrapper {
    &.is-open {
      display: block !important;
      position: absolute;
      @include z-index(base, 1);
      width: calc(100% - #{space(xl)});
    }
  }

  @include media("screen", ">=#{breakpoint(md)}") {
    &__nav {
      padding: 0;
      border-bottom: none;

      .gx-tabs {
        padding-left: space(xl);
        padding-right: space(xl);
      }
    }

    &__actions {
      padding-left: space(xl);
      padding-right: space(xl);

      .gx-input-wrapper {
        width: 100%;
        max-width: 40rem;
        margin-left: space(md);
      }
    }

    &__searchResults {
      padding-left: space(xl);
      padding-right: space(xl);
    }
  }
}

// TODO: renderlo indipendente o lasciarlo qui?
.gx-visibility-bar {
  margin-bottom: space(lg);

  > span {
    @include typography(body-tiny);
    margin-left: space(sm);

    & + .gx-badge {
      margin-left: space(md);
    }
  }
}
