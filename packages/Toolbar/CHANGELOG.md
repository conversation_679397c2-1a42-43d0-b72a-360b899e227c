# Change Log

All notable changes to this project will be documented in this file.
See [Conventional Commits](https://conventionalcommits.org) for commit guidelines.

## [5.4.27](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/toolbar@5.4.26...@gx-design/toolbar@5.4.27) (2025-09-08)

**Note:** Version bump only for package @gx-design/toolbar

## [5.4.26](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/toolbar@5.4.25...@gx-design/toolbar@5.4.26) (2025-07-24)

**Note:** Version bump only for package @gx-design/toolbar

## [5.4.25](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/toolbar@5.4.24...@gx-design/toolbar@5.4.25) (2025-07-22)

**Note:** Version bump only for package @gx-design/toolbar

## [5.4.24](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/toolbar@5.4.23...@gx-design/toolbar@5.4.24) (2025-07-17)

**Note:** Version bump only for package @gx-design/toolbar

## [5.4.23](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/toolbar@5.4.22...@gx-design/toolbar@5.4.23) (2025-07-09)

**Note:** Version bump only for package @gx-design/toolbar

## [5.4.22](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/toolbar@5.4.21...@gx-design/toolbar@5.4.22) (2025-07-08)

**Note:** Version bump only for package @gx-design/toolbar

## [5.4.21](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/toolbar@5.4.20...@gx-design/toolbar@5.4.21) (2025-07-07)

**Note:** Version bump only for package @gx-design/toolbar

## [5.4.20](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/toolbar@5.4.19...@gx-design/toolbar@5.4.20) (2025-07-04)

**Note:** Version bump only for package @gx-design/toolbar

## [5.4.19](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/toolbar@5.4.18...@gx-design/toolbar@5.4.19) (2025-06-24)

**Note:** Version bump only for package @gx-design/toolbar

## [5.4.18](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/toolbar@5.4.17...@gx-design/toolbar@5.4.18) (2025-06-09)

**Note:** Version bump only for package @gx-design/toolbar

## [5.4.17](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/toolbar@5.4.16...@gx-design/toolbar@5.4.17) (2025-05-30)

**Note:** Version bump only for package @gx-design/toolbar

## [5.4.16](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/toolbar@5.4.15...@gx-design/toolbar@5.4.16) (2025-05-23)

**Note:** Version bump only for package @gx-design/toolbar

## [5.4.15](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/toolbar@5.4.14...@gx-design/toolbar@5.4.15) (2025-05-16)

**Note:** Version bump only for package @gx-design/toolbar

## [5.4.14](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/toolbar@5.4.13...@gx-design/toolbar@5.4.14) (2025-05-14)

**Note:** Version bump only for package @gx-design/toolbar

## [5.4.13](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/toolbar@5.4.12...@gx-design/toolbar@5.4.13) (2025-04-16)

**Note:** Version bump only for package @gx-design/toolbar

## [5.4.12](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/toolbar@5.4.11...@gx-design/toolbar@5.4.12) (2025-04-02)

**Note:** Version bump only for package @gx-design/toolbar

## [5.4.11](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/toolbar@5.4.10...@gx-design/toolbar@5.4.11) (2025-03-21)

**Note:** Version bump only for package @gx-design/toolbar

## [5.4.10](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/toolbar@5.4.9...@gx-design/toolbar@5.4.10) (2025-02-14)

**Note:** Version bump only for package @gx-design/toolbar

## [5.4.9](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/toolbar@5.4.8...@gx-design/toolbar@5.4.9) (2025-02-11)

**Note:** Version bump only for package @gx-design/toolbar

## [5.4.8](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/toolbar@5.4.7...@gx-design/toolbar@5.4.8) (2025-01-15)

### Bug Fixes

- **vari:** button forms select ([a5e7b92](https://gitlab.pepita.io/getrix/gx-design/commit/a5e7b92c66081430ac5c1172d306e3ec8c7be6fe))

## [5.4.7](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/toolbar@5.4.6...@gx-design/toolbar@5.4.7) (2025-01-15)

**Note:** Version bump only for package @gx-design/toolbar

## [5.4.6](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/toolbar@5.4.5...@gx-design/toolbar@5.4.6) (2025-01-13)

**Note:** Version bump only for package @gx-design/toolbar

## [5.4.5](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/toolbar@5.4.4...@gx-design/toolbar@5.4.5) (2024-12-04)

**Note:** Version bump only for package @gx-design/toolbar

## [5.4.4](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/toolbar@5.4.3...@gx-design/toolbar@5.4.4) (2024-11-29)

**Note:** Version bump only for package @gx-design/toolbar

## [5.4.3](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/toolbar@5.4.2...@gx-design/toolbar@5.4.3) (2024-10-31)

**Note:** Version bump only for package @gx-design/toolbar

## [5.4.2](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/toolbar@5.4.1...@gx-design/toolbar@5.4.2) (2024-10-21)

**Note:** Version bump only for package @gx-design/toolbar

## [5.4.1](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/toolbar@5.4.0...@gx-design/toolbar@5.4.1) (2024-10-02)

**Note:** Version bump only for package @gx-design/toolbar

# [5.4.0](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/toolbar@5.3.0...@gx-design/toolbar@5.4.0) (2024-09-23)

### Features

- **css:** custom properties ([cbc56c0](https://gitlab.pepita.io/getrix/gx-design/commit/cbc56c0e3e675d85f92ad25ac135cce270f02b2b))

## [5.3.1-alpha.0](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/toolbar@5.3.0...@gx-design/toolbar@5.3.1-alpha.0) (2024-09-16)

**Note:** Version bump only for package @gx-design/toolbar

# [5.3.0](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/toolbar@5.2.2...@gx-design/toolbar@5.3.0) (2024-08-29)

### Features

- **colors:** pro new colors ([2103ea7](https://gitlab.pepita.io/getrix/gx-design/commit/2103ea76597b5ae75018d2d62947c6b01d613e82))

## [5.2.2](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/toolbar@5.2.1...@gx-design/toolbar@5.2.2) (2024-08-06)

### Bug Fixes

- **toolbar:** mobile filter button only icon and counter ([75328bd](https://gitlab.pepita.io/getrix/gx-design/commit/75328bdebde5bf9b816d267a02fe8088a62e5da1))

# [5.2.0](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/toolbar@5.1.3...@gx-design/toolbar@5.2.0) (2024-07-24)

### Features

- **toolbar:** add filters counter and background ([fd30a7f](https://gitlab.pepita.io/getrix/gx-design/commit/fd30a7fb292fa6553c97ad7499cc8a8785477c7d))

## [5.1.3](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/toolbar@5.1.2...@gx-design/toolbar@5.1.3) (2024-07-15)

**Note:** Version bump only for package @gx-design/toolbar

## [5.1.2](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/toolbar@5.1.1...@gx-design/toolbar@5.1.2) (2024-06-14)

**Note:** Version bump only for package @gx-design/toolbar

## [5.1.1](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/toolbar@5.1.0...@gx-design/toolbar@5.1.1) (2024-06-05)

**Note:** Version bump only for package @gx-design/toolbar

# [5.1.0](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/toolbar@5.0.6...@gx-design/toolbar@5.1.0) (2024-06-03)

### Features

- **Toolbar:** adding hideSearchInput optional prop ([d900451](https://gitlab.pepita.io/getrix/gx-design/commit/d9004517fa8e5ef1b98cb697285c4cc0bd10f546))

## [5.0.6](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/toolbar@5.0.5...@gx-design/toolbar@5.0.6) (2024-05-28)

**Note:** Version bump only for package @gx-design/toolbar

## [5.0.5](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/toolbar@5.0.4...@gx-design/toolbar@5.0.5) (2024-05-16)

**Note:** Version bump only for package @gx-design/toolbar

## [5.0.4](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/toolbar@5.0.3...@gx-design/toolbar@5.0.4) (2024-05-14)

**Note:** Version bump only for package @gx-design/toolbar

## [5.0.3](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/toolbar@5.0.2...@gx-design/toolbar@5.0.3) (2024-05-06)

**Note:** Version bump only for package @gx-design/toolbar

## [5.0.2](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/toolbar@5.0.1...@gx-design/toolbar@5.0.2) (2024-04-18)

### Bug Fixes

- **out-animation:** on exit handler ([c0c71d7](https://gitlab.pepita.io/getrix/gx-design/commit/c0c71d75cf5becac1278fa97aae9eec11000de5d))

## [5.0.2-alpha.1](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/toolbar@5.0.2-alpha.0...@gx-design/toolbar@5.0.2-alpha.1) (2024-04-17)

**Note:** Version bump only for package @gx-design/toolbar

## [5.0.2-alpha.0](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/toolbar@5.0.1...@gx-design/toolbar@5.0.2-alpha.0) (2024-04-16)

**Note:** Version bump only for package @gx-design/toolbar

## [5.0.1](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/toolbar@5.0.0...@gx-design/toolbar@5.0.1) (2024-04-08)

**Note:** Version bump only for package @gx-design/toolbar

# [5.0.0](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/toolbar@4.0.2...@gx-design/toolbar@5.0.0) (2024-03-26)

### Features

- **button:** style and variant changes ([1fbe519](https://gitlab.pepita.io/getrix/gx-design/commit/1fbe519808701c8a752b6ec8b36fff8a0bd82a9b))

### BREAKING CHANGES

- **button:** button style to variant

# [4.1.0-alpha.1](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/toolbar@4.1.0-alpha.0...@gx-design/toolbar@4.1.0-alpha.1) (2024-03-25)

**Note:** Version bump only for package @gx-design/toolbar

# [4.1.0-alpha.0](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/toolbar@4.0.1...@gx-design/toolbar@4.1.0-alpha.0) (2024-03-25)

### Features

- **button:** style and variant changes ([37c6c2b](https://gitlab.pepita.io/getrix/gx-design/commit/37c6c2b61fcbe48add378261292c61c60f918e60))

## [4.0.1](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/toolbar@4.0.0...@gx-design/toolbar@4.0.1) (2024-03-20)

**Note:** Version bump only for package @gx-design/toolbar

# [4.0.0](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/toolbar@3.4.0...@gx-design/toolbar@4.0.0) (2024-03-18)

- feat!: new architecture ([5572bfc](https://gitlab.pepita.io/getrix/gx-design/commit/5572bfc9c05f1cb4aa5a0e134d2771ac5d7243f0))

### BREAKING CHANGES

- new architecture

# [3.4.0](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/toolbar@3.3.7...@gx-design/toolbar@3.4.0) (2024-03-18)

**Note:** Version bump only for package @gx-design/toolbar

# [3.4.0-alpha.3](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/toolbar@3.4.0-alpha.2...@gx-design/toolbar@3.4.0-alpha.3) (2024-03-11)

**Note:** Version bump only for package @gx-design/toolbar

# [3.4.0-alpha.2](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/toolbar@3.4.0-alpha.1...@gx-design/toolbar@3.4.0-alpha.2) (2024-03-11)

**Note:** Version bump only for package @gx-design/toolbar

# [3.4.0-alpha.1](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/toolbar@3.4.0-alpha.0...@gx-design/toolbar@3.4.0-alpha.1) (2024-03-08)

**Note:** Version bump only for package @gx-design/toolbar

# [3.4.0-alpha.0](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/toolbar@3.3.5...@gx-design/toolbar@3.4.0-alpha.0) (2024-03-07)

### Features

- **scss:** alert and theme styles ([7c0102d](https://gitlab.pepita.io/getrix/gx-design/commit/7c0102d309fce10724944b1e2e355d391d84ebe7))

## [3.3.6-alpha.1](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/toolbar@3.3.6-alpha.0...@gx-design/toolbar@3.3.6-alpha.1) (2024-02-29)

**Note:** Version bump only for package @gx-design/toolbar

## [3.3.6-alpha.0](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/toolbar@3.3.5...@gx-design/toolbar@3.3.6-alpha.0) (2024-02-28)

**Note:** Version bump only for package @gx-design/toolbar

## [3.3.5](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/toolbar@3.3.4...@gx-design/toolbar@3.3.5) (2024-02-27)

**Note:** Version bump only for package @gx-design/toolbar

## [3.3.4](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/toolbar@3.3.3...@gx-design/toolbar@3.3.4) (2024-01-31)

**Note:** Version bump only for package @gx-design/toolbar

## [3.3.3](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/toolbar@3.3.2...@gx-design/toolbar@3.3.3) (2024-01-12)

**Note:** Version bump only for package @gx-design/toolbar

## [3.3.2](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/toolbar@3.3.1...@gx-design/toolbar@3.3.2) (2023-12-20)

**Note:** Version bump only for package @gx-design/toolbar

## [3.3.1](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/toolbar@3.3.1-alpha.0...@gx-design/toolbar@3.3.1) (2023-12-18)

**Note:** Version bump only for package @gx-design/toolbar

## [3.3.1-alpha.0](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/toolbar@3.3.0...@gx-design/toolbar@3.3.1-alpha.0) (2023-12-18)

**Note:** Version bump only for package @gx-design/toolbar

# [3.3.0](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/toolbar@3.2.3...@gx-design/toolbar@3.3.0) (2023-12-15)

### Features

- **toolbar:** add tests ([a484817](https://gitlab.pepita.io/getrix/gx-design/commit/a48481713e78355c6434f55063b1e34b500d98cf))

## [3.2.3](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/toolbar@3.2.2...@gx-design/toolbar@3.2.3) (2023-12-01)

**Note:** Version bump only for package @gx-design/toolbar

## [3.2.2](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/toolbar@3.2.1...@gx-design/toolbar@3.2.2) (2023-11-27)

**Note:** Version bump only for package @gx-design/toolbar

## [3.2.2-alpha.0](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/toolbar@3.2.1...@gx-design/toolbar@3.2.2-alpha.0) (2023-11-27)

**Note:** Version bump only for package @gx-design/toolbar

## [3.2.1](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/toolbar@3.2.0...@gx-design/toolbar@3.2.1) (2023-11-07)

**Note:** Version bump only for package @gx-design/toolbar

# [3.2.0](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/toolbar@3.1.4...@gx-design/toolbar@3.2.0) (2023-11-06)

### Features

- **badge:** new badge ([f378000](https://gitlab.pepita.io/getrix/gx-design/commit/f3780008c7723a224ad3b90dd8b26cf3c0304673))

## [3.1.4](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/toolbar@3.1.3...@gx-design/toolbar@3.1.4) (2023-10-27)

**Note:** Version bump only for package @gx-design/toolbar

## [3.1.3](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/toolbar@3.1.2...@gx-design/toolbar@3.1.3) (2023-10-25)

**Note:** Version bump only for package @gx-design/toolbar

## [3.1.3-alpha.0](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/toolbar@3.1.2...@gx-design/toolbar@3.1.3-alpha.0) (2023-10-25)

**Note:** Version bump only for package @gx-design/toolbar

## [3.1.2](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/toolbar@3.1.1...@gx-design/toolbar@3.1.2) (2023-10-23)

**Note:** Version bump only for package @gx-design/toolbar

## [3.1.1](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/toolbar@3.1.0...@gx-design/toolbar@3.1.1) (2023-10-02)

**Note:** Version bump only for package @gx-design/toolbar

# [3.1.0](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/toolbar@3.0.19...@gx-design/toolbar@3.1.0) (2023-09-27)

### Features

- **storybook:** upgrade to 7 ([1c0e5e9](https://gitlab.pepita.io/getrix/gx-design/commit/1c0e5e941dcf7b841d1b5d2a2825f66f7921276e))

## [3.0.19](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/toolbar@3.0.18...@gx-design/toolbar@3.0.19) (2023-09-08)

**Note:** Version bump only for package @gx-design/toolbar

## [3.0.18](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/toolbar@3.0.17...@gx-design/toolbar@3.0.18) (2023-08-28)

**Note:** Version bump only for package @gx-design/toolbar

## [3.0.17](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/toolbar@3.0.16...@gx-design/toolbar@3.0.17) (2023-08-23)

**Note:** Version bump only for package @gx-design/toolbar

## [3.0.16](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/toolbar@3.0.15...@gx-design/toolbar@3.0.16) (2023-07-27)

**Note:** Version bump only for package @gx-design/toolbar

## [3.0.15](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/toolbar@3.0.14...@gx-design/toolbar@3.0.15) (2023-07-26)

**Note:** Version bump only for package @gx-design/toolbar

## [3.0.14](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/toolbar@3.0.13...@gx-design/toolbar@3.0.14) (2023-07-17)

**Note:** Version bump only for package @gx-design/toolbar

## [3.0.14-alpha.1](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/toolbar@3.0.14-alpha.0...@gx-design/toolbar@3.0.14-alpha.1) (2023-07-12)

**Note:** Version bump only for package @gx-design/toolbar

## [3.0.14-alpha.0](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/toolbar@3.0.13...@gx-design/toolbar@3.0.14-alpha.0) (2023-07-12)

**Note:** Version bump only for package @gx-design/toolbar

## [3.0.13](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/toolbar@3.0.12...@gx-design/toolbar@3.0.13) (2023-06-22)

**Note:** Version bump only for package @gx-design/toolbar

## [3.0.12](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/toolbar@3.0.11...@gx-design/toolbar@3.0.12) (2023-06-19)

**Note:** Version bump only for package @gx-design/toolbar

## [3.0.11](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/toolbar@3.0.10...@gx-design/toolbar@3.0.11) (2023-05-31)

**Note:** Version bump only for package @gx-design/toolbar

## [3.0.10](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/toolbar@3.0.9...@gx-design/toolbar@3.0.10) (2023-05-30)

**Note:** Version bump only for package @gx-design/toolbar

## [3.0.9](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/toolbar@3.0.8...@gx-design/toolbar@3.0.9) (2023-05-16)

**Note:** Version bump only for package @gx-design/toolbar

## [3.0.8](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/toolbar@3.0.7...@gx-design/toolbar@3.0.8) (2023-05-15)

**Note:** Version bump only for package @gx-design/toolbar

## [3.0.7](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/toolbar@3.0.6...@gx-design/toolbar@3.0.7) (2023-05-15)

**Note:** Version bump only for package @gx-design/toolbar

## [3.0.6](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/toolbar@3.0.5...@gx-design/toolbar@3.0.6) (2023-05-11)

**Note:** Version bump only for package @gx-design/toolbar

## [3.0.5](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/toolbar@3.0.4...@gx-design/toolbar@3.0.5) (2023-05-10)

**Note:** Version bump only for package @gx-design/toolbar

## [3.0.5-alpha.0](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/toolbar@3.0.4...@gx-design/toolbar@3.0.5-alpha.0) (2023-05-10)

**Note:** Version bump only for package @gx-design/toolbar

## [3.0.4](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/toolbar@3.0.3...@gx-design/toolbar@3.0.4) (2023-05-03)

**Note:** Version bump only for package @gx-design/toolbar

## [3.0.3](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/toolbar@3.0.2...@gx-design/toolbar@3.0.3) (2023-04-19)

### Bug Fixes

- **toolbar:** cross icon ([26d5124](https://gitlab.pepita.io/getrix/gx-design/commit/26d5124e2754ff82cdd351d875e8f2d30b73efb9))

## [3.0.2](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/toolbar@3.0.1...@gx-design/toolbar@3.0.2) (2023-04-18)

**Note:** Version bump only for package @gx-design/toolbar

## [3.0.1](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/toolbar@3.0.0...@gx-design/toolbar@3.0.1) (2023-04-13)

**Note:** Version bump only for package @gx-design/toolbar

# [3.0.0](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/toolbar@2.2.0...@gx-design/toolbar@3.0.0) (2023-04-13)

### Features

- **icons:** nuove icone ([f444215](https://gitlab.pepita.io/getrix/gx-design/commit/f44421598355b8d9de903a81f2ca6232887f780c))

### BREAKING CHANGES

- **icons:** changed icon sprite

# [3.0.0-alpha.0](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/toolbar@2.2.0...@gx-design/toolbar@3.0.0-alpha.0) (2023-04-05)

### Features

- **icons:** nuove icone ([6528540](https://gitlab.pepita.io/getrix/gx-design/commit/65285405393efb34dfdcc4329d99dd1ef41d70c1))

### BREAKING CHANGES

- **icons:** changed icon sprite

# [2.2.0](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/toolbar@2.2.0-alpha.1...@gx-design/toolbar@2.2.0) (2023-04-03)

**Note:** Version bump only for package @gx-design/toolbar

# [2.2.0-alpha.1](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/toolbar@2.2.0-alpha.0...@gx-design/toolbar@2.2.0-alpha.1) (2023-04-03)

### Features

- **toolbar:** toolbar quick filters ([d3390bb](https://gitlab.pepita.io/getrix/gx-design/commit/d3390bbdff2d1e2ee2412499bedb7197560d0742))

# [2.2.0-alpha.0](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/toolbar@2.1.19...@gx-design/toolbar@2.2.0-alpha.0) (2023-04-03)

### Features

- **toolbar:** toolbar quick filters ([428b879](https://gitlab.pepita.io/getrix/gx-design/commit/428b87962ffcad39e123a74e6efc016402cba190))
- **toolbar:** toolbar quick filters ([b688dcc](https://gitlab.pepita.io/getrix/gx-design/commit/b688dcc608ec84ea20e67da9e3c39b9fedec4822))

## [2.1.19](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/toolbar@2.1.18...@gx-design/toolbar@2.1.19) (2023-03-31)

## [2.1.19-alpha.1](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/toolbar@2.1.19-alpha.0...@gx-design/toolbar@2.1.19-alpha.1) (2023-03-29)

**Note:** Version bump only for package @gx-design/toolbar

## [2.1.19-alpha.0](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/toolbar@2.1.18...@gx-design/toolbar@2.1.19-alpha.0) (2023-03-29)

**Note:** Version bump only for package @gx-design/toolbar

## [2.1.18](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/toolbar@2.1.17...@gx-design/toolbar@2.1.18) (2023-03-28)

**Note:** Version bump only for package @gx-design/toolbar

## [2.1.17](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/toolbar@2.1.17-alpha.0...@gx-design/toolbar@2.1.17) (2023-03-24)

**Note:** Version bump only for package @gx-design/toolbar

## [2.1.17-alpha.0](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/toolbar@2.1.16...@gx-design/toolbar@2.1.17-alpha.0) (2023-03-23)

**Note:** Version bump only for package @gx-design/toolbar

## [2.1.16](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/toolbar@2.1.15...@gx-design/toolbar@2.1.16) (2023-03-20)

**Note:** Version bump only for package @gx-design/toolbar

## [2.1.15](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/toolbar@2.1.14...@gx-design/toolbar@2.1.15) (2023-03-20)

**Note:** Version bump only for package @gx-design/toolbar

## [2.1.14](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/toolbar@2.1.13...@gx-design/toolbar@2.1.14) (2023-03-15)

### Bug Fixes

- **toolbar:** adds effect dependency in order to recreate event listener handler ([7045052](https://gitlab.pepita.io/getrix/gx-design/commit/70450523acd15e086bcef63a4f8073e202f0e039))

## [2.1.13](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/toolbar@2.1.12...@gx-design/toolbar@2.1.13) (2023-03-07)

**Note:** Version bump only for package @gx-design/toolbar

## [2.1.12](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/toolbar@2.1.11...@gx-design/toolbar@2.1.12) (2023-03-06)

**Note:** Version bump only for package @gx-design/toolbar

## [2.1.11](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/toolbar@2.1.10...@gx-design/toolbar@2.1.11) (2023-02-27)

**Note:** Version bump only for package @gx-design/toolbar

## [2.1.10](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/toolbar@2.1.9...@gx-design/toolbar@2.1.10) (2023-02-21)

**Note:** Version bump only for package @gx-design/toolbar

## [2.1.9](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/toolbar@2.1.8...@gx-design/toolbar@2.1.9) (2023-02-07)

**Note:** Version bump only for package @gx-design/toolbar

## [2.1.8](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/toolbar@2.1.7...@gx-design/toolbar@2.1.8) (2023-01-30)

**Note:** Version bump only for package @gx-design/toolbar

## [2.1.7](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/toolbar@2.1.7-alpha.0...@gx-design/toolbar@2.1.7) (2023-01-23)

**Note:** Version bump only for package @gx-design/toolbar

## [2.1.7-alpha.0](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/toolbar@2.1.6...@gx-design/toolbar@2.1.7-alpha.0) (2023-01-23)

**Note:** Version bump only for package @gx-design/toolbar

## [2.1.6](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/toolbar@2.1.5...@gx-design/toolbar@2.1.6) (2023-01-18)

**Note:** Version bump only for package @gx-design/toolbar

## [2.1.5](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/toolbar@2.1.4...@gx-design/toolbar@2.1.5) (2023-01-12)

### Bug Fixes

- **toolbar:** toolbar mobile select active ([efb7082](https://gitlab.pepita.io/getrix/gx-design/commit/efb7082a7f6ad21f6a4ad9338dbea490733d125f))

## [2.1.4](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/toolbar@2.1.3...@gx-design/toolbar@2.1.4) (2023-01-11)

**Note:** Version bump only for package @gx-design/toolbar

## [2.1.3](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/toolbar@2.1.2...@gx-design/toolbar@2.1.3) (2022-12-21)

**Note:** Version bump only for package @gx-design/toolbar

## [2.1.2](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/toolbar@2.1.1...@gx-design/toolbar@2.1.2) (2022-12-06)

**Note:** Version bump only for package @gx-design/toolbar

## [2.1.1](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/toolbar@2.1.1-alpha.1...@gx-design/toolbar@2.1.1) (2022-11-23)

**Note:** Version bump only for package @gx-design/toolbar

## [2.1.1-alpha.1](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/toolbar@2.1.1-alpha.0...@gx-design/toolbar@2.1.1-alpha.1) (2022-11-22)

### Bug Fixes

- **toolbar:** on keypress ([4391dd0](https://gitlab.pepita.io/getrix/gx-design/commit/4391dd034b77dac9f9c227d8ad7119ac5fdca348))

## [2.1.1-alpha.0](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/toolbar@2.1.0...@gx-design/toolbar@2.1.1-alpha.0) (2022-11-22)

### Bug Fixes

- **toolbar:** hasfilter prop ([f5c039f](https://gitlab.pepita.io/getrix/gx-design/commit/f5c039ff9d8712646f86022004541ec613d5b61f))
- **toolbar:** hasfilter prop ([107db13](https://gitlab.pepita.io/getrix/gx-design/commit/107db13cb9053ef8c7609b6b32a7a428c986e978))
- **toolbar:** input ref ([4cb7c32](https://gitlab.pepita.io/getrix/gx-design/commit/4cb7c32e3a5930d60e6de9e98d82360143a3fead))

# 2.1.0 (2022-11-22)

### Features

- **toolbar:** toolbar component ([3f29904](https://gitlab.pepita.io/getrix/gx-design/commit/3f299048638d9fb5100435976e9db64ace275dd5))

# [2.1.0-alpha.1](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/toolbar@2.1.0-alpha.0...@gx-design/toolbar@2.1.0-alpha.1) (2022-11-21)

### Bug Fixes

- **toolbar:** on enter keyboard event fix ([77b3e3c](https://gitlab.pepita.io/getrix/gx-design/commit/77b3e3c2c3cca2f706ec844ffc6006c529a3b22d))

# 2.1.0-alpha.0 (2022-11-21)

### Features

- **toolbar:** toolbar component ([3f29904](https://gitlab.pepita.io/getrix/gx-design/commit/3f299048638d9fb5100435976e9db64ace275dd5))
- **toolbar:** toolbar progress ([506a44f](https://gitlab.pepita.io/getrix/gx-design/commit/506a44f1be8e93b468af98f554094217bc3ebc4b))
