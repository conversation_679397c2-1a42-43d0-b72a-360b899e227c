import React, {
  ReactElement,
  createContext,
  useState,
  useEffect,
  useRef,
} from "react";
import clsx from "clsx";
import { Icon } from "@gx-design/icon";
import { Tabs, TabsItem } from "@gx-design/tabs";
import { ButtonInput } from "@gx-design/button-input";
import { Dropdown } from "@gx-design/dropdown";
import { Button } from "@gx-design/button";
import { Select } from "@gx-design/select";
import { ActionList, ActionListItem } from "@gx-design/action-list";
import { useToolbarContext } from "./useToolbarContext";
import { useMediaMatch } from "@gx-design/use-media-match";

export type SortItem = {
  label: string;
  key: string;
};

export type NavigationItem = {
  label: string;
  value: string;
  active: boolean;
  counter?: number | null;
};

export type CounterProps = {
  item: Pick<NavigationItem, "active" | "counter">;
};

export type NavigationProps = {
  /**
   * Navigation tabs content object.
   */
  navigationItems: NavigationItem[];
};

export type ToolbarProps = {
  children: ReactElement | Array<ReactElement>;
  /**
   * Indicates if filters are available
   */
  hasFilters?: boolean;
  /**
   * Indicates if a filters action was performed, if it is a number it will be shown as counter in the filters button
   */
  isFiltersSearchActive: boolean | number;
  /**
   * UI labels object.
   */
  labels: { [key: string]: string };
  /**
   * Action to perform when a navigation tab is clicked.
   */
  onNavigateAction: (value: NavigationItem["value"]) => void;
  /**
   * Action to perform on filter button click.
   */
  onShowFiltersAction: () => void;
  /**
   * Action to perform when the remove filters button is clicked.
   */
  onRemoveFiltersAction: () => void;
  /**
   * Action to perform when the input text search button is clicked.
   */
  onInputSearchAction: (value: string) => void;
  /**
   * Action to perform when the reset button is clicked. The search input text will be blanked.
   */
  onResetInputSearchAction: () => void;
  /**
   * Search total results count.
   */
  searchItemsCount: number;
  /**
   * Text already present into the search input.
   */
  searchedText: string;
  /**
   * Element rendered next to filters button that handles a quick filter if present (this will not be rendered in Mobile)
   */
  quickFilter?: ReactElement;

  /**
   * If true the search input will be hidden
   */
  hideSearchInput?: boolean;
};

export type ActionsProps = {
  /**
   * Sorting config parameters
   */
  sortingConfig?: {
    /**
     * Current sorting.
     */
    currentSortKey: string;
    /**
     * Current sorting direction.
     */
    currentSortDirection: string;
    /**
     * Sorting elements.
     */
    sortItems: SortItem[];
    /**
     * Action to perform on sort item click
     */
    onSortAction: (key: string, direction: string) => void;
    /**
     * If true the sorting button will be shown on desktop breakpoints
     */
    showSortingOnDesktop?: boolean;
    /**
     * Indicates if a sorting action was performed
     */
    isSortingActive?: boolean;
  };
};

export type ToolbarContextType =
  | Omit<
      ToolbarProps,
      "onNavigateAction" | "onResetInputSearchAction" | "children"
    > & {
      handleResetInputSearch: () => void;
      handleTabNavigation: (value: string) => void;
      inputSearchValue: ToolbarProps["searchedText"];
      setInputSearchValue: React.Dispatch<
        React.SetStateAction<ToolbarProps["searchedText"]>
      >;
    };

const DEFAULT_LABELS = {
  filterAction: "Filter",
  filters: "filters",
  searchInputPlaceholder: "Insert",
  remove: "Remove",
  removeFiltersAction: "Remove filters",
  resetAction: "Reset",
  result: "result",
  resultPlural: "results",
  resultFor: "result for",
  resultsFor: "results for",
  searchAction: "Search",
  ascendingSorting: "ascending",
  descendingSorting: "descending",
  sortingAction: "Sort",
};

const normalizeLanguage = (language: string) =>
  language.substring(0, 2).toLowerCase();

const getLanguage = () => {
  return normalizeLanguage(document.documentElement.lang) || "it";
};

export const ToolbarContext = createContext<ToolbarContextType | null>(null);

export const Toolbar = ({
  children,
  hasFilters = true,
  isFiltersSearchActive = false,
  searchedText = "",
  labels = DEFAULT_LABELS,
  onNavigateAction,
  onShowFiltersAction,
  onRemoveFiltersAction,
  onInputSearchAction,
  onResetInputSearchAction,
  searchItemsCount,
  quickFilter,
  hideSearchInput,
}: ToolbarProps) => {
  const [inputSearchValue, setInputSearchValue] =
    useState<ToolbarProps["searchedText"]>(searchedText);

  const handleResetInputSearch = () => {
    setInputSearchValue("");
    onResetInputSearchAction && onResetInputSearchAction();
  };

  const handleTabNavigation = (value: string) => {
    setInputSearchValue("");
    onNavigateAction && onNavigateAction(value);
  };

  const toolbarContext: ToolbarContextType = {
    hasFilters,
    handleResetInputSearch,
    handleTabNavigation,
    inputSearchValue,
    isFiltersSearchActive,
    labels,
    onShowFiltersAction,
    onRemoveFiltersAction,
    onInputSearchAction,
    searchedText,
    searchItemsCount,
    setInputSearchValue,
    quickFilter,
    hideSearchInput,
  };

  useEffect(() => setInputSearchValue(searchedText), [searchedText]);

  return (
    <ToolbarContext.Provider value={toolbarContext}>
      <div className="gx-toolbar">{children}</div>
    </ToolbarContext.Provider>
  );
};

const Counter: React.FC<CounterProps> = ({ item }) => {
  if (item.counter == null) {
    return <div className="gx-skeleton"></div>;
  }

  return <span>({item.counter.toLocaleString(getLanguage())})</span>;
};

export const ToolbarNavigation: React.FC<NavigationProps> = ({
  navigationItems,
}) => {
  const { handleTabNavigation } = useToolbarContext();
  const activeItem = navigationItems.find((item) => item.active === true);

  return (
    <div className="gx-toolbar__nav">
      <Select
        label=""
        isLabelVisible={false}
        options={navigationItems.map(({ label, value, counter }) => ({
          label: `${label} ${counter ? "(" + counter + ")" : ""}`,
          value,
        }))}
        value={activeItem ? activeItem.value : ""}
        onChange={(evt) => handleTabNavigation(evt.target.value)}
        className="gx-is-hidden-md-up"
      />
      <Tabs className="gx-is-hidden-sm-down" withSpacing>
        {navigationItems.map((navigationItem: NavigationItem) => (
          <TabsItem
            key={`tab_item_${navigationItem.value}`}
            text={navigationItem.label}
            active={navigationItem.active}
            onClick={() => handleTabNavigation(navigationItem.value)}
            endElement={
              navigationItem.counter !== undefined ? (
                <Counter item={navigationItem} />
              ) : undefined
            }
          />
        ))}
      </Tabs>
    </div>
  );
};

export const ToolbarActions: React.FC<ActionsProps> = ({ sortingConfig }) => {
  const {
    hasFilters,
    inputSearchValue,
    isFiltersSearchActive,
    labels,
    onShowFiltersAction,
    onRemoveFiltersAction,
    onInputSearchAction,
    searchItemsCount,
    setInputSearchValue,
    quickFilter,
    hideSearchInput,
  } = useToolbarContext();
  const [visibileSearchInputRow, setVisibleSearchInputRow] =
    useState<boolean>(false);
  const resultsLabel: string =
    searchItemsCount === 1 ? labels.result : labels.resultPlural;
  const isDesktop = useMediaMatch("largeDesktop");
  const isMobile = useMediaMatch("small-screen");

  const handleSearchInputRowVisibility = (visibility: boolean) => {
    setVisibleSearchInputRow(visibility);
  };

  const inputRef = useRef<HTMLInputElement>(null);

  useEffect(() => {
    if (inputRef && inputRef.current) {
      const keyDownHandler = (event: KeyboardEvent) => {
        if (event.key === "Enter" && inputRef?.current?.value) {
          event.preventDefault();
          onInputSearchAction(inputRef?.current?.value.toString());
        }
      };

      inputRef?.current?.addEventListener("keydown", keyDownHandler);

      return () => {
        inputRef?.current?.removeEventListener("keydown", keyDownHandler);
      };
    }
  }, [onInputSearchAction]);

  const isFilteSearchActiveABoolean =
    typeof isFiltersSearchActive === "boolean";
  const isFilteSearchActiveANumber = typeof isFiltersSearchActive === "number";
  return (
    <>
      <div className="gx-toolbar__actions">
        <div className="gx-toolbar__searchBar">
          <span className="gx-toolbar__searchBarNumber gx-text-light">
            {searchItemsCount} {resultsLabel}{" "}
          </span>
          {hideSearchInput ? null : (
            <ButtonInput
              label=""
              ref={inputRef}
              data-testid="searchInput"
              className="gx-is-hidden-sm-down"
              isLabelVisible={false}
              buttonContent={labels.searchAction}
              placeholder={labels.searchInputPlaceholder}
              value={inputSearchValue}
              onChange={(event) => setInputSearchValue(event.target.value)}
              onButtonClick={() => onInputSearchAction(inputSearchValue)}
            />
          )}
        </div>
        <div className="gx-toolbar__openFilters">
          {quickFilter && isDesktop && quickFilter}
          {!hideSearchInput && (
            <Button
              className="gx-is-hidden-md-up"
              onClick={() =>
                handleSearchInputRowVisibility(!visibileSearchInputRow)
              }
              iconOnly
            >
              <Icon name={!visibileSearchInputRow ? "search" : "cross"} />
            </Button>
          )}

          {sortingConfig && (
            <Dropdown
              buttonClassName={clsx({
                "is-selected": sortingConfig?.isSortingActive || false,
                "gx-is-hidden-md-up": !sortingConfig?.showSortingOnDesktop,
              })}
              buttonIsIconOnly={
                sortingConfig?.showSortingOnDesktop ? isMobile : true
              }
              buttonContent={
                <>
                  <Icon name="order" />
                  {!isMobile && sortingConfig?.showSortingOnDesktop && (
                    <span>
                      {labels?.sortingAction || DEFAULT_LABELS.sortingAction}
                    </span>
                  )}
                </>
              }
              position="bottomRight"
            >
              <ActionList key="sorting_asc_action_list">
                {sortingConfig.sortItems.map((sortItem) => (
                  <ActionListItem
                    key={`action_list_item_asc_${sortItem.key}`}
                    active={
                      !!sortingConfig.currentSortDirection &&
                      !!sortingConfig.currentSortKey &&
                      sortItem.key === sortingConfig.currentSortKey &&
                      sortingConfig.currentSortDirection === "ASC"
                    }
                    text={`${sortItem.label} ${labels.ascendingSorting}`}
                    onClick={() =>
                      sortingConfig.onSortAction(sortItem.key, "ASC")
                    }
                  />
                ))}
              </ActionList>
              <ActionList key="sorting_desc_action_list">
                {sortingConfig.sortItems.map((sortItem) => (
                  <ActionListItem
                    key={`action_list_item_asc_${sortItem.key}`}
                    active={
                      !!sortingConfig.currentSortDirection &&
                      !!sortingConfig.currentSortKey &&
                      sortItem.key === sortingConfig.currentSortKey &&
                      sortingConfig.currentSortDirection === "DESC"
                    }
                    text={`${sortItem.label} ${labels.descendingSorting}`}
                    onClick={() =>
                      sortingConfig.onSortAction(sortItem.key, "DESC")
                    }
                  />
                ))}
              </ActionList>
            </Dropdown>
          )}
          {hasFilters && (
            <>
              {isMobile ? (
                <Button
                  iconOnly={isFilteSearchActiveABoolean}
                  className={clsx({
                    "is-selected": isFiltersSearchActive,
                  })}
                  onClick={onShowFiltersAction}
                >
                  <Icon name="sliders" />

                  {isFilteSearchActiveANumber && isFiltersSearchActive > 0 ? (
                    <span>{`(${isFiltersSearchActive})`}</span>
                  ) : null}
                </Button>
              ) : (
                <Button
                  className={clsx({
                    "is-selected": isFiltersSearchActive,
                  })}
                  onClick={onShowFiltersAction}
                >
                  <Icon name="sliders" />
                  <span>
                    {labels.filterAction}
                    {isFilteSearchActiveANumber
                      ? ` (${isFiltersSearchActive})`
                      : ""}
                  </span>
                </Button>
              )}
              {isFiltersSearchActive && (
                <Button variant="ghost" onClick={onRemoveFiltersAction}>
                  <span>
                    {labels.remove}{" "}
                    <span className="gx-is-hidden-sm-down">
                      {labels.filters}
                    </span>
                  </span>
                </Button>
              )}
            </>
          )}
        </div>
      </div>
      <div
        className={clsx("gx-toolbar__searchInputWrap gx-is-hidden-md-up", {
          "gx-is-hidden": !visibileSearchInputRow,
        })}
      >
        <ButtonInput
          label={labels.searchAction}
          className="gx-is-hidden-md-up"
          isLabelVisible={false}
          buttonContent={labels.searchAction}
          placeholder={labels.searchInputPlaceholder}
          value={inputSearchValue}
          onChange={(event) => setInputSearchValue(event.target.value)}
          onButtonClick={() => onInputSearchAction(inputSearchValue)}
        />
      </div>
    </>
  );
};

export const ToolbarResults: React.FC<{}> = () => {
  const { handleResetInputSearch, searchedText, labels, searchItemsCount } =
    useToolbarContext();

  if (!searchedText) {
    return null;
  }

  const resultsLabel: string =
    searchItemsCount === 1 ? labels.resultFor : labels.resultsFor;

  return (
    <div className="gx-toolbar__searchResults">
      <div>
        {searchItemsCount} {resultsLabel.toLocaleLowerCase()}{" "}
        <span>“{searchedText}”</span>
      </div>
      <Button variant="ghost" onClick={handleResetInputSearch}>
        <span>{labels.resetAction}</span>
      </Button>
    </div>
  );
};

Toolbar.Navigation = ToolbarNavigation;
Toolbar.Actions = ToolbarActions;
Toolbar.Results = ToolbarResults;
