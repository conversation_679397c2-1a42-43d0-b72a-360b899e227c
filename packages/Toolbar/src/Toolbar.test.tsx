import { ComponentProps } from "react";
import { afterEach, expect, it, vi } from "vitest";
import { render, screen } from "../../../test/utilities";
import {
  Toolbar,
  ToolbarActions,
  ToolbarNavigation,
  ToolbarResults,
  type ToolbarProps,
} from "./Toolbar";

const labels = {
  filterAction: "Filter",
  filters: "filters",
  searchInputPlaceholder: "Insert",
  remove: "Remove",
  removeFiltersAction: "Remove filters",
  resetAction: "Reset",
  result: "result",
  resultPlural: "results",
  resultFor: "result for",
  resultsFor: "results for",
  searchAction: "Search",
  ascendingSorting: "ascending",
  descendingSorting: "descending",
};

const onNavigateAction = vi.fn();
const onShowFiltersAction = vi.fn();
const onRemoveFiltersAction = vi.fn();
const onInputSearchAction = vi.fn();
const onResetInputSearchAction = vi.fn();

const defaultProps: ToolbarProps = {
  children: <div>...</div>,
  hasFilters: true,
  isFiltersSearchActive: false,
  labels,
  onNavigateAction,
  onShowFiltersAction,
  onRemoveFiltersAction,
  onInputSearchAction,
  onResetInputSearchAction,
  searchItemsCount: 10,
  searchedText: "",
  hideSearchInput: false,
};

const toolbarNavigationDefaultProps: ComponentProps<typeof ToolbarNavigation> =
  {
    navigationItems: [
      {
        active: true,
        counter: 10,
        label: "Attivi",
        value: "attivi",
      },
      {
        active: false,
        counter: 1000,
        label: "Archiviati",
        value: "archiviati",
      },
    ],
  };

afterEach(() => {
  vi.clearAllMocks();
});

it("renders the toolbar", () => {
  render(
    <Toolbar {...defaultProps}>
      <ToolbarNavigation {...toolbarNavigationDefaultProps} />
      <ToolbarActions />
      <ToolbarResults />
    </Toolbar>
  );

  expect(screen.getByText("Attivi")).toBeInTheDocument();
  expect(screen.getByText("Archiviati")).toBeInTheDocument();
});

it("renders the toolbar without default search text", () => {
  const { baseElement } = render(
    <Toolbar {...defaultProps} searchedText={""}>
      <ToolbarNavigation {...toolbarNavigationDefaultProps} />
      <ToolbarActions />
      <ToolbarResults />
    </Toolbar>
  );

  const inputElement = baseElement.querySelector('[placeholder="Insert"]');

  expect(inputElement).toHaveProperty("placeholder", "Insert");
  expect(inputElement).toHaveValue("");
});

it("renders the toolbar and each tab could be selected", async () => {
  const { user } = render(
    <Toolbar {...defaultProps}>
      <ToolbarNavigation {...toolbarNavigationDefaultProps} />
      <ToolbarActions />
      <ToolbarResults />
    </Toolbar>
  );

  await user.selectOptions(screen.getByRole("combobox"), "archiviati");
  expect(onNavigateAction).toHaveBeenCalledWith("archiviati");
  expect(onNavigateAction).toHaveBeenCalledTimes(1);

  await user.selectOptions(screen.getByRole("combobox"), "attivi");
  expect(onNavigateAction).toHaveBeenCalledWith("attivi");
  expect(onNavigateAction).toHaveBeenCalledTimes(2);
});

it("renders the toolbar with sort direction and click a direction", async () => {
  const onSortAction = vi.fn();

  const { baseElement, user } = render(
    <Toolbar {...defaultProps}>
      <ToolbarNavigation {...toolbarNavigationDefaultProps} />
      <ToolbarActions
        sortingConfig={{
          currentSortDirection: "ASC",
          onSortAction,
          currentSortKey: "name",
          sortItems: [
            {
              label: "name",
              key: "name",
            },
            {
              label: "surname",
              key: "name",
            },
          ],
        }}
      />
      <ToolbarResults />
    </Toolbar>
  );

  const [_searchButton, directionButton] = baseElement.querySelectorAll(
    ".gx-toolbar__openFilters button"
  );

  await user.click(directionButton);
  await user.click(screen.getByText("surname ascending"));

  expect(onSortAction).toHaveBeenCalledWith("name", "ASC");
});

it("click on reset", async () => {
  const onSortAction = vi.fn();

  const { user } = render(
    <Toolbar
      {...defaultProps}
      hasFilters
      searchedText="Ricerca"
      isFiltersSearchActive
    >
      <ToolbarNavigation {...toolbarNavigationDefaultProps} />
      <ToolbarActions
        sortingConfig={{
          currentSortDirection: "name",
          onSortAction,
          currentSortKey: "ASC",
          sortItems: [
            {
              label: "name",
              key: "name",
            },
            {
              label: "surname",
              key: "name",
            },
          ],
        }}
      />
      <ToolbarResults />
    </Toolbar>
  );

  await user.click(screen.getByRole("button", { name: labels.resetAction }));

  expect(onResetInputSearchAction).toHaveBeenCalledTimes(1);
});

it("type inside the input and search", async () => {
  const { user, baseElement } = render(
    <Toolbar {...defaultProps}>
      <ToolbarNavigation {...toolbarNavigationDefaultProps} />
      <ToolbarActions />
      <ToolbarResults />
    </Toolbar>
  );

  const searchInput = baseElement.querySelector(".gx-input-button-wrapper")
    ?.firstChild as HTMLInputElement;

  expect(searchInput).toBeInTheDocument();
  expect(searchInput).toHaveValue("");

  await user.type(searchInput, "test");
  expect(searchInput).toHaveValue("test");

  // 🫠 this could be improved
  const [_, buttonElement] = screen.getAllByRole("button", {
    name: labels.searchAction,
  });
  expect(buttonElement).toBeInTheDocument();
  await user.click(buttonElement);

  expect(onInputSearchAction).toHaveBeenCalledWith("test");
  expect(onInputSearchAction).toHaveBeenCalledTimes(1);
});

it("should display counter loader", async () => {
  const { baseElement } = render(
    <Toolbar {...defaultProps}>
      <ToolbarNavigation
        navigationItems={[
          { active: false, label: "elemento", value: "1", counter: null },
        ]}
      />
      <ToolbarActions />
      <ToolbarResults />
    </Toolbar>
  );

  expect(baseElement.querySelector(".gx-skeleton")).toBeInTheDocument();
});

it("type inside the input and search using the enter button", async () => {
  const { user, baseElement } = render(
    <Toolbar {...defaultProps}>
      <ToolbarNavigation {...toolbarNavigationDefaultProps} />
      <ToolbarActions />
      <ToolbarResults />
    </Toolbar>
  );

  const searchInput = baseElement.querySelector(".gx-input-button-wrapper")
    ?.firstChild as HTMLInputElement;

  expect(searchInput).toBeInTheDocument();
  expect(searchInput).toHaveValue("");

  await user.type(searchInput, "test");
  expect(searchInput).toHaveValue("test");

  await user.keyboard("{enter}");
  expect(onInputSearchAction).toHaveBeenCalledWith("test");
});

it("should hide the search input if hideSearchInput is true", async () => {
  render(
    <Toolbar {...defaultProps} hideSearchInput>
      <ToolbarNavigation {...toolbarNavigationDefaultProps} />
      <ToolbarActions />
      <ToolbarResults />
    </Toolbar>
  );

  expect(screen.queryByTestId("searchInput")).toBeNull();
});
