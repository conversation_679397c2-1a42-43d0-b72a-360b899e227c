{"name": "@gx-design/toolbar", "version": "5.4.27", "description": "Gx Design Toolbar component", "source": "src/Toolbar.tsx", "main": "dist/index.js", "module": "dist/index.mjs", "peerDependencies": {"react": "*"}, "devDependencies": {"tsup": "^7.2.0"}, "dependencies": {"@gx-design/action-list": "^3.2.2", "@gx-design/button": "^5.3.13", "@gx-design/button-input": "^5.2.19", "@gx-design/dropdown": "^5.3.0", "@gx-design/icon": "^5.6.3", "@gx-design/select": "^5.3.19", "@gx-design/tabs": "^3.2.3", "@gx-design/tag": "^5.2.13", "@gx-design/theme": "^1.4.0", "@gx-design/use-media-match": "^3.0.2", "clsx": "1.1.1"}, "scripts": {"build": "tsup", "watch": "tsup --watch"}, "repository": {"type": "git", "url": "********************:getrix/gx-design.git", "directory": "packages/Toolbar"}, "typings": "dist/index.d.ts", "volta": {"extends": "../../package.json"}}