{"name": "@gx-design/pagination-bar", "version": "5.2.17", "description": "Gx Design PaginationBar component", "source": "src/PaginationBar.tsx", "main": "dist/index.js", "module": "dist/index.mjs", "peerDependencies": {"react": "*"}, "devDependencies": {"tsup": "^7.2.0"}, "dependencies": {"@gx-design/button": "^5.3.13", "@gx-design/icon": "^5.6.3", "@gx-design/pager": "^5.2.16", "@gx-design/theme": "^1.4.0", "@pepita/babelfish": "^1.5.0", "clsx": "1.1.1"}, "scripts": {"build": "tsup", "watch": "tsup --watch"}, "repository": {"type": "git", "url": "********************:getrix/gx-design.git", "directory": "packages/PaginationBar"}, "typings": "dist/index.d.ts", "volta": {"extends": "../../package.json"}}