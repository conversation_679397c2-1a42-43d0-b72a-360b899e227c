import { afterEach, describe, expect, it, vi } from "vitest";
import { render, screen } from "../../../test/utilities";
import { PaginationDropDown, PaginationBar } from "./PaginationBar";
import { useGetChildrenByType } from "./useGetChildrenByType";

const options = [10, 20, 30];
const resultString = "results";
const onResultsChange = vi.fn();

afterEach(() => {
  vi.clearAllMocks();
});

it("should render the component with default options", () => {
  render(
    <PaginationDropDown
      onResultsChange={onResultsChange}
      resultString={resultString}
      value={10}
    />
  );

  const selectElement = screen.getByRole("combobox");

  expect(selectElement).toHaveDisplayValue("10 results");
  expect(screen.getByText("30 results")).toBeInTheDocument();
  expect(screen.getByText("50 results")).toBeInTheDocument();
});

it("should render the component with custom options", () => {
  render(
    <PaginationDropDown
      onResultsChange={onResultsChange}
      options={[5, 15, 25]}
      resultString={resultString}
      value={10}
    />
  );

  expect(screen.getByText("5 results")).toBeInTheDocument();
  expect(screen.getByText("15 results")).toBeInTheDocument();
  expect(screen.getByText("25 results")).toBeInTheDocument();
});

it("should call onResultsChange when an option is selected", async () => {
  const { user } = render(
    <PaginationDropDown
      onResultsChange={onResultsChange}
      options={options}
      resultString={resultString}
      value={10}
    />
  );

  const selectElement = screen.getByRole("combobox");

  await user.selectOptions(selectElement, "20");

  expect(onResultsChange).toHaveBeenCalledWith(20);
});

it("should render the PaginationBar", () => {
  render(
    <PaginationBar
      currentResults={10}
      totalResults={100}
      separatorString={"of"}
      resultString={"results"}
    ></PaginationBar>
  );

  expect(screen.getByText("1 - 10 of 100 results")).toBeInTheDocument();
});

it("should render the PaginationBar with results combobox", () => {
  const onResultChange = vi.fn();

  render(
    <PaginationBar
      currentResults={10}
      totalResults={100}
      separatorString={"of"}
      resultString={"results"}
    >
      <PaginationBar.DropDown
        onResultsChange={onResultChange}
        resultString="results"
      ></PaginationBar.DropDown>
    </PaginationBar>
  );

  expect(screen.getByText("1 - 10 of 100 results")).toBeInTheDocument();
  expect(screen.getByRole("combobox")).toBeInTheDocument();
});

it("should render the PaginationBar with results combobox and the pager", async () => {
  const onResultChange = vi.fn();
  const onPageChange = vi.fn();

  const { user } = render(
    <PaginationBar
      currentResults={30}
      totalResults={90}
      separatorString={"of"}
      resultString={"results"}
    >
      <PaginationBar.DropDown
        onResultsChange={onResultChange}
        resultString="results"
        value={10}
      ></PaginationBar.DropDown>
      <PaginationBar.Pager
        activePage={2}
        maxPagesToShow={3}
        onPageClick={onPageChange}
        totalPages={12}
      />
    </PaginationBar>
  );

  expect(screen.getByText("31 - 60 of 90 results")).toBeInTheDocument();
  expect(screen.getByRole("combobox")).toBeInTheDocument();

  await user.click(screen.getByRole("button", { name: "3" }));

  expect(onPageChange).toHaveBeenCalledWith(3);
});

it("should render the PaginationBar with results combobox and the undeterministic pager", async () => {
  const onResultChange = vi.fn();
  const onLoadMoreClick = vi.fn();

  const { user } = render(
    <PaginationBar
      currentResults={30}
      totalResults={90}
      separatorString={"of"}
      resultString={"results"}
    >
      <PaginationBar.DropDown
        onResultsChange={onResultChange}
        resultString="results"
        value={30}
      ></PaginationBar.DropDown>
      <PaginationBar.PaginationLoadMore
        buttonText="Load more"
        onLoadMoreClick={onLoadMoreClick}
      />
    </PaginationBar>
  );

  expect(screen.getByText("1 - 30 of 90 results")).toBeInTheDocument();
  expect(screen.getByRole("combobox")).toBeInTheDocument();

  await user.click(screen.getByRole("button", { name: "Load more" }));

  expect(onLoadMoreClick).toHaveBeenCalled();
});

it("should render the PaginationBar with results offset > results", async () => {
  const onResultChange = vi.fn();
  const onPageChange = vi.fn();

  const { user } = render(
    <PaginationBar
      currentResults={30}
      totalResults={90}
      separatorString={"of"}
      resultString={"results"}
    >
      <PaginationBar.DropDown
        onResultsChange={onResultChange}
        resultString="results"
        value={10}
      ></PaginationBar.DropDown>
      <PaginationBar.Pager
        activePage={4}
        maxPagesToShow={3}
        onPageClick={onPageChange}
        totalPages={12}
      />
    </PaginationBar>
  );

  expect(screen.getByText("91 - 90 of 90 results")).toBeInTheDocument();
  expect(screen.getByRole("combobox")).toBeInTheDocument();

  await user.click(screen.getByRole("button", { name: "2" }));
  expect(onPageChange).toHaveBeenCalledWith(2);
});

describe("function useGetChildrenByType", () => {
  it("should return null if children is null", () => {
    const getChildrenByType = useGetChildrenByType(null);
    const result = getChildrenByType("div");
    expect(result).toBeNull();
  });

  it("should return undefined if children is an empty array", () => {
    const getChildrenByType = useGetChildrenByType([]);
    const result = getChildrenByType("div");
    expect(result).toBeUndefined();
  });

  it("should return false if children does not contain the specified component", () => {
    const getChildrenByType = useGetChildrenByType(
      <div>
        <span>hello</span>
      </div>
    );
    const result = getChildrenByType("button");
    expect(result).toBe(false);
  });

  it("not match any element nested in another element", () => {
    const getChildrenByType = useGetChildrenByType(
      <div>
        <button>click me</button>
        <button>no, click me</button>
      </div>
    );
    const result = getChildrenByType("button");
    expect(result).not.toEqual(<button>click me</button>);
    expect(result).toBe(false);
  });

  it("should return the first element that matches the specified component with the specified props", () => {
    const getChildrenByType = useGetChildrenByType([
      <span key={1} id="btn1">
        span
      </span>,
      <button key={2} id="btn2">
        button
      </button>,
      <div key={3}>hello</div>,
    ]);

    const result = getChildrenByType("div", { id: "totally-invented" }); // he doesn't care about the props matching the element
    expect(result).toEqual(
      <div id="totally-invented" key={3}>
        hello
      </div>
    ); // just because it's the first element that matches the component
  });

  it("should return the specified element if children is not an array", () => {
    const getChildrenByType = useGetChildrenByType(
      <button id="btn1">click me</button>
    );
    const result = getChildrenByType("button");
    expect(result).toEqual(<button id="btn1">click me</button>);
  });

  it("should return the specified element with the specified props if children is not an array", () => {
    const getChildrenByType = useGetChildrenByType(
      <button id="btn1">click me</button>
    );
    const result = getChildrenByType("button", { id: "btn2" });
    expect(result).toEqual(<button id="btn2">click me</button>);
  });
});
