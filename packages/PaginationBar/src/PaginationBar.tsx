import { JSX } from "react";
import React from "react";
import { Pager, PagerProps } from "@gx-design/pager";
import { Icon } from "@gx-design/icon";
import { Button } from "@gx-design/button";
import { useGetChildrenByType } from "./useGetChildrenByType";

export type IPaginationLoadMore = {
  /** <PERSON><PERSON> fired when user clicks on loadmore button */
  onLoadMoreClick: () => void;
  /** Text inside the load more button */
  buttonText: string;
};

export type IPaginationDropDownProps = {
  /** <PERSON><PERSON> fired when range of results change */
  onResultsChange: (value: number) => void;
  /** Array of numbers that indicates range of visible results */
  options?: number[];
  /** Current value of visible results */
  value?: number;
  /** String that renders near the results value */
  resultString: string;
};

export type PaginationBarProps = {
  /** Total number of results */
  totalResults: number;
  /** Current number of visible results */
  currentResults: number;
  /** Subcomponents to render inside PaginationBar */
  children?: JSX.Element | JSX.Element[];
  /** Strings to separate current visible range */
  separatorString: string;
  /** String that renders near the results value */
  resultString: string;
};

export const PaginationLoadMore: React.FC<IPaginationLoadMore> = ({
  onLoadMoreClick,
  buttonText,
}) => {
  return (
    <Button onClick={onLoadMoreClick}>
      <Icon name="circular-arrow-right" />
      <span>{buttonText}</span>
    </Button>
  );
};

export const PaginationDropDown: React.FC<IPaginationDropDownProps> = ({
  onResultsChange,
  options,
  resultString,
  value,
}) => {
  const dropDownOptions = options || [10, 30, 50];

  return (
    <div className="gx-input-wrapper">
      <div className="gx-select gx-select--native">
        <select
          className="gx-select__nativeControl"
          onChange={(ev) => {
            if (onResultsChange) {
              onResultsChange(parseInt(ev.target.value));
            }
          }}
          value={value}
        >
          {dropDownOptions.map((resultOpt) => (
            <option key={`pager-dd-${resultOpt}`} value={resultOpt}>
              {resultOpt} {resultString}
            </option>
          ))}
        </select>
        <Icon name="arrow-down" />
      </div>
    </div>
  );
};

export const PaginationBar: React.FC<PaginationBarProps> & {
  DropDown: React.FC<IPaginationDropDownProps>;
  PaginationLoadMore: React.FC<IPaginationLoadMore>;
  Pager: React.FC<PagerProps>;
} = ({
  totalResults,
  currentResults,
  separatorString,
  resultString,
  children,
}) => {
  const getChildrenByType = useGetChildrenByType(children);

  const ChildPager = getChildrenByType(Pager);
  const ChildLoadMore = getChildrenByType(PaginationLoadMore);
  const ChildDropDown = getChildrenByType(PaginationDropDown);

  const startOffset =
    ChildPager && (ChildPager.props.activePage - 1) * currentResults + 1;
  const endItemIndex =
    ChildPager && ChildPager.props.activePage * currentResults;
  return (
    <div className="gx-paginationBar">
      <div className="gx-paginationBar__results">
        {ChildDropDown}
        {ChildPager ? (
          <div className="gx-paginationBar__resultsRange">
            <span>
              {startOffset} -{" "}
              {endItemIndex > totalResults ? totalResults : endItemIndex}{" "}
              {separatorString} {totalResults} {resultString}
            </span>
          </div>
        ) : (
          <div className="gx-paginationBar__resultsRange">
            <span>
              1 - {currentResults} {separatorString} {totalResults}{" "}
              {resultString}
            </span>
          </div>
        )}
      </div>
      <div className="gx-paginationBar__pagination">
        {(ChildPager && ChildPager) || null}
        {!ChildPager && ChildLoadMore && ChildLoadMore}
      </div>
    </div>
  );
};

PaginationBar.DropDown = PaginationDropDown;
PaginationBar.PaginationLoadMore = PaginationLoadMore;
PaginationBar.Pager = Pager;
