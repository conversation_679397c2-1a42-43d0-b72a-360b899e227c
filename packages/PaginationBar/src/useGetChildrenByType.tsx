import * as React from "react";

export const useGetChildrenByType = (children: any) => {
  return (component: React.FC, props?: { [key: string]: any }) => {
    if (!children) return null;

    if (Array.isArray(children)) {
      const element = children.find((itm) => itm.type === component);

      if (props) {
        return element && React.cloneElement(element, props);
      }

      return element;
    } else {
      if (props) {
        return (
          children.type === component && React.cloneElement(children, props)
        );
      }

      return children.type === component && children;
    }
  };
};
