// ==========================================================================
// Pagination Bar - Components/Pagination Bar
// ==========================================================================
@use "@gx-design/theme/styles" as *;
@use "@gx-design/tools/styles" as *;

.gx-paginationBar {
  display: flex;
  justify-content: space-between;
  padding: space(md) space(xl);
  border-top: 0.1rem solid color(border-main);

  @include media("<#{breakpoint(sm)}") {
    padding: space(md);

    .gx-pager .gx-button:not(:first-of-type, :last-of-type) {
      display: none;
    }
  }

  &__results {
    display: flex;
    align-items: center;

    &Range {
      margin-left: space(md);
      color: color(content-low);

      @include media("<#{breakpoint(sm)}") {
        display: none;
      }

      span + span {
        margin-left: space(sm);
      }
    }
  }
}
