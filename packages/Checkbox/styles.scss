@use "@gx-design/theme/styles" as *;

$gx-checkbox: "gx-checkbox";
$gx-radio-check-size: 2rem;
$gx-radio-hover-size: 3.2rem;

// -------------------------------
// Checkbox
// -------------------------------
.#{$gx-checkbox} {
  position: relative;
  display: inline-flex;
  min-height: 2.4rem;
  margin-bottom: 0;
  cursor: pointer;
  gap: 0 0.8rem;

  &__nativeControl {
    position: absolute;
    appearance: none;
    z-index: 0;
    left: -(($gx-radio-hover-size - $gx-radio-check-size) / 2);
    top: -(($gx-radio-hover-size - $gx-radio-check-size) / 2);
    width: $gx-radio-hover-size;
    height: $gx-radio-hover-size;
    display: block;
    margin: 0;
    border-radius: radius(rounded);
    background-color: rgba(black, 0.8);
    outline: none;
    opacity: 0;
    transform: scale(1);
    pointer-events: none;
    transition: opacity 0.3s, transform 0.2s;
  }

  &:hover {
    > input {
      opacity: 0.1;

      &:focus {
        opacity: 0.1;
      }
    }
  }

  &__check {
    position: absolute;
    top: -0.2rem;
    right: 0;
    bottom: 0;
    left: 0;
    width: 1.2rem;
    height: 0.6rem;
    margin: auto;
    border-color: color(background-main);
    border-style: solid solid none none;
    border-width: 0.2rem;
    opacity: 0;
    transform: rotate(-225deg) scale(0);
    transition: opacity 0.18s ease-in-out;
  }

  &__minus {
    position: absolute;
    top: 50%;
    margin-top: -0.1rem;
    left: 50%;
    margin-left: -0.6rem;
    display: block;
    width: 1.2rem;
    height: 0.2rem;
    background-color: color(background-main);
    border-radius: radius(rounded);
    transform: scale(0);
    transition: opacity 0.18s ease-in-out;
    opacity: 0;
  }

  &__control {
    flex-shrink: 0;
    position: relative;
    width: 2rem;
    height: 2rem;
    vertical-align: top;
    background-color: color(background-main);
    border: solid 0.2rem color(border-selectable);
    border-radius: radius(sm);
    transition: border-color 0.2s;

    &::before {
      content: "";
      position: absolute;
      left: 0;
      top: 0;
      width: 100%;
      height: 100%;
      background-color: color(background-brand);
      transform: scale(0);
      opacity: 0;
      transition: opacity 0.3s, transform 0.2s;
    }
  }

  &--error {
    .#{$gx-checkbox}__control {
      background-color: color(background-error);
      border-color: color(border-error);
    }
  }

  input {
    &:checked,
    &:indeterminate {
      & + .#{$gx-checkbox}__control {
        border-color: color(background-brand);

        &::before {
          transform: scale(1);
          opacity: 1;
        }

        .#{$gx-checkbox}__check {
          opacity: 1;
          transform: scale(1) rotate(-225deg);
          // animation: checkAnimaton 0.3s ease-in 0s 1 normal none;
        }

        .#{$gx-checkbox}__minus {
          opacity: 1;
          transform: scale(1);
          // animation: minusAnimation 0.3s ease-in 0s 1 normal none;
        }
      }
    }
  }

  &__text {
    margin-top: -0.2rem;
  }

  &--reversed {
    flex-direction: row-reverse;
    justify-content: flex-end;

    .#{$gx-checkbox} {
      &__control {
        margin-left: auto;
      }

      &__text {
        margin-left: 0;
      }

      &__icon {
        margin-left: 0;
      }
    }
  }

  &--fullWidth {
    display: flex;
  }

  &.is-disabled {
    cursor: not-allowed;
    opacity: 0.5;

    input {
      display: none;
    }
  }

  // Checkbox button
  &--button {
    align-items: center;
    border: 0.1rem solid color(border-main);
    border-radius: radius(md);
    height: 4rem;
    padding: 0 space(md);
    background-color: color(background-main);
    position: relative;
    cursor: pointer;

    .#{$gx-checkbox}__nativeControl {
      display: none;
    }

    .#{$gx-checkbox}__text {
      margin-top: 0;
    }

    &.gx-checkbox--checked {
      background-color: color(background-selected);
      border-color: color(border-selected);

      .#{$gx-checkbox}__text {
        color: color(content-selected);
      }
    }

    .#{$gx-checkbox}__control {
      align-self: center;
    }

    &Spaced {
      display: flex;
      justify-content: space-between;

      .#{$gx-checkbox}__control {
        margin: 0;
      }
    }

    &.is-disabled {
      background-color: color(background-alt);
    }

    &:hover:not(.is-disabled) {
      background-color: color(background-alt);
    }
  }

  // Checkbox chip
  &--chip {
    align-items: center;
    border: 0.1rem solid color(border-main);
    border-radius: radius(md);
    height: 3.2rem;
    padding: 0 space(sm);
    background-color: color(background-main);
    position: relative;
    cursor: pointer;
    @include typography(body-small);

    &:has(input:checked) {
      background-color: color(background-selected);
      border-color: color(border-selected);
      color: color(content-selected);
    }

    .#{$gx-checkbox}__icon {
      @include icon-size(sm);
    }

    .#{$gx-checkbox}__control {
      display: none;
    }

    .#{$gx-checkbox}__nativeControl {
      display: none;
    }

    .#{$gx-checkbox}__text {
      margin-top: 0;
    }

    &.gx-checkbox--checked {
      background-color: color(background-selected);
      border-color: color(border-selected);

      .#{$gx-checkbox}__text {
        color: color(content-selected);
      }
    }

    .#{$gx-checkbox}__control {
      align-self: center;
    }

    &Spaced {
      display: flex;
      justify-content: space-between;

      .#{$gx-checkbox}__control {
        margin: 0;
      }
    }

    &.is-disabled {
      background-color: color(background-alt);
    }

    &:hover:not(.is-disabled) {
      background-color: color(background-alt);
    }
  }

  &__icon {
    @include icon-size(md);
  }
}

// animazione, per ora non è da pubblicare
// @keyframes minusAnimation {
//   0% {
//     transform: scale(0);
//   }

//   50% {
//     transform: scale(1.2);
//   }

//   100% {
//     transform: scale(1);
//   }
// }

// @keyframes checkAnimaton {
//   0% {
//     transform: scale(0) rotate(-210deg);
//   }

//   50% {
//     transform: scale(1.2) rotate(-240deg);
//   }

//   100% {
//     transform: scale(1) rotate(-225deg);
//   }
// }
