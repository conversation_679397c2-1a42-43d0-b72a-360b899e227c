import { HelperText } from "@gx-design/helper-text";
import { Icon, IconProps } from "@gx-design/icon";
import useForkedRefs from "@gx-design/use-forked-refs";
import clsx from "clsx";
import React, {
  ForwardRefRenderFunction,
  InputHTMLAttributes,
  forwardRef,
  useEffect,
  useRef,
} from "react";

export type CheckboxProps = {
  /**
   * The label title, `ReactNode` is accepted
   */
  label?: React.ReactNode;
  /**
   * If true, checkbox will be into an "indeterminate" state (showing a "minus" icon inside); internal input attribute will be changed accordingly; shold not be used when `checked==={true}`.
   */
  indeterminate?: boolean;
  /**
   The Checkbox variant
  */
  variant?: "button" | "chip";
  /**
   * Determine if checkbox has an icon
   */
  icon?: IconProps["name"];
  /**
   * Determine whether the order of elements should be reversed.
   */
  isReversed?: boolean;
  /**
   * Determine if it should be full width
   */
  isFullWidth?: boolean;
  /**
   * The error message
   */
  error?: string;
} & InputHTMLAttributes<HTMLInputElement>;

const CheckboxComponent: ForwardRefRenderFunction<
  HTMLInputElement,
  CheckboxProps
> = (
  {
    label,
    error = "",
    variant,
    isReversed = false,
    isFullWidth = false,
    icon,
    indeterminate = false,
    ...props
  },
  ref
) => {
  const innerRef = useRef<HTMLInputElement>(null);
  const checkboxRef = useForkedRefs(innerRef, ref);

  useEffect(() => {
    if (innerRef.current) {
      innerRef.current.indeterminate = indeterminate;
    }
  }, [indeterminate]);

  return (
    <>
      <label
        htmlFor={props.id}
        className={clsx("gx-checkbox", props.className, {
          [`gx-checkbox--${variant}`]: variant,
          "gx-checkbox--reversed": isReversed,
          "gx-checkbox--fullWidth": isFullWidth,
          "gx-checkbox--error": error,
          "is-disabled": props.disabled,
        })}
      >
        <input
          ref={checkboxRef}
          className="gx-checkbox__nativeControl"
          type="checkbox"
          {...props}
        />
        <div className="gx-checkbox__control">
          {indeterminate ? (
            <span className="gx-checkbox__minus"></span>
          ) : (
            <span className="gx-checkbox__check"></span>
          )}
        </div>
        {label && <span className="gx-checkbox__text">{label}</span>}
        {icon && <Icon className="gx-checkbox__icon" name={icon} />}
      </label>
      {error && <HelperText text={error} style="error" />}
    </>
  );
};

export const Checkbox = forwardRef(CheckboxComponent);
