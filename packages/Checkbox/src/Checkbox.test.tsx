/* eslint-disable testing-library/no-node-access */
import { describe, it, expect, vi } from "vitest";
import { render, screen } from "@testing-library/react";
import userEvent from "@testing-library/user-event";
import { Checkbox } from "./Checkbox";

vi.mock("@gx-design/use-forked-refs", async () => {
  const actual = await vi.importActual<any>("@gx-design/use-forked-refs");
  return actual.default;
});

describe("Checkbox Component", () => {
  const args = {
    id: "check1",
    name: "check1",
    label: "Test label",
  };

  it("renders correctly with label", () => {
    render(<Checkbox {...args} />);
    expect(screen.getByLabelText("Test label")).toBeInTheDocument();
  });

  const variants = [
    { variant: "chip", expectedClass: "gx-checkbox--chip" },
    { variant: "button", expectedClass: "gx-checkbox--button" },
  ] as const;

  it.each(variants)(
    "renders with the correct class for variant '$variant'",
    ({ variant, expectedClass }) => {
      render(<Checkbox {...args} variant={variant} />);

      const checkboxContainer = screen
        .getByLabelText("Test label")
        .closest("label");

      expect(checkboxContainer).toHaveClass(expectedClass);

      variants
        .filter((v) => v.variant !== variant)
        .forEach((v) =>
          expect(checkboxContainer).not.toHaveClass(v.expectedClass)
        );
    }
  );

  it("renders with error message when error prop is provided", () => {
    const errorMessage = "This is an error message";
    render(<Checkbox error={errorMessage} {...args} />);
    expect(screen.getByText(errorMessage)).toBeInTheDocument();
    const checkbox = screen.getByLabelText("Test label");
    const label = checkbox.closest("label");
    expect(label).toHaveClass("gx-checkbox--error");
  });

  it("toggles checked state on click", async () => {
    render(<Checkbox {...args} />);
    const checkbox = screen.getByLabelText("Test label");

    expect(checkbox).not.toBeChecked();

    await userEvent.click(checkbox);
    expect(checkbox).toBeChecked();

    await userEvent.click(checkbox);
    expect(checkbox).not.toBeChecked();
  });

  it("does not toggle when disabled", async () => {
    render(<Checkbox disabled {...args} />);
    const checkbox = screen.getByLabelText("Test label");

    expect(checkbox).toBeDisabled();
    expect(checkbox).not.toBeChecked();

    await userEvent.click(checkbox);
    expect(checkbox).not.toBeChecked();
  });

  it("renders with indeterminate state and shows minus icon", () => {
    render(<Checkbox {...args} indeterminate={true} />);

    const minusIcon = screen
      .getByRole("checkbox")
      .parentElement?.querySelector(".gx-checkbox__minus");

    expect(minusIcon).toBeInTheDocument();
  });
});
