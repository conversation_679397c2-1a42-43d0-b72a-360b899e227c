import { expect, it, vi } from "vitest";
import { render, screen } from "../../../test/utilities";
import { Modal } from "./Modal";
import React from "react";

// Check if the Modal component renders correctly with all elements
it("renders the Modal open with all elements", () => {
  const mockOnClose = vi.fn();
  render(
    <Modal
      isOpen={true}
      title={"Test Modal"}
      onClose={mockOnClose}
      closeAction={true}
      headerActions={<button>Header Action</button>}
      footer="default"
      size={"large"}
      bodyHeight={"default"}
      className={"modal-custom-class"}
      labels={{ closeLabel: "Close Modal", confirmLabel: "Confirm Modal" }}
    >
      <div>Modal Content</div>
    </Modal>
  );

  const openModal = screen.getByTestId("dialog");
  expect(openModal).toBeInTheDocument();
  expect(openModal).toHaveClass("gx-modal");
  expect(openModal).toHaveClass("gx-modal--large");
  expect(openModal).toHaveClass("modal-custom-class");
  expect(screen.getByTestId("modal-title")).toBeInTheDocument();
  expect(screen.getByTestId("modal-close")).toBeInTheDocument();
  expect(screen.getByTestId("modal-body")).toBeInTheDocument();
  expect(screen.getByTestId("modal-footer")).toBeInTheDocument();
  expect(screen.getByTestId("modal-header")).toBeInTheDocument();
  expect(screen.getByTestId("modal-dialog")).toBeInTheDocument();
  expect(screen.getByTestId("modal-content")).toBeInTheDocument();
  expect(screen.getByTestId("modal-header")).toHaveTextContent("Test Modal");
  expect(screen.getByTestId("modal-header")).toHaveTextContent("Header Action");
  expect(screen.getByTestId("modal-body")).toHaveTextContent("Modal Content");
  expect(screen.getByTestId("modal-footer")).toHaveTextContent("Close Modal");
  expect(screen.getByTestId("modal-footer")).toHaveTextContent("Confirm Modal");
});

// Check if the Modal component renders correctly closed
it("renders the Modal closed", () => {
  render(<Modal isOpen={false} title={""} onClose={() => {}} />);

  // Assert that the modal is not in the document
  const modal = screen.queryByRole("dialog");
  expect(modal).not.toBeInTheDocument();
});

// Check if the Modal component renders correctly open and simulates closing it
it("renders the Modal open with close action", () => {
  const mockOnClose = vi.fn();
  render(<Modal isOpen={true} title={""} onClose={mockOnClose} />);

  // Simulate closing the modal
  const closeButton = screen.getByTestId("modal-close");
  expect(closeButton).toBeInTheDocument();
  closeButton.click();

  expect(mockOnClose).toHaveBeenCalledTimes(1);
});

// Check if the Modal component renders correctly with custom footer
it("renders the Modal open with custom footer", () => {
  const mockOnClose = vi.fn();
  render(
    <Modal
      isOpen={true}
      title={"Test Modal"}
      onClose={mockOnClose}
      footer={<button>Custom Footer Action</button>}
    >
      <div>Modal Content</div>
    </Modal>
  );
  expect(screen.getByTestId("modal-footer")).toBeInTheDocument();
  expect(screen.getByTestId("modal-footer")).toHaveTextContent(
    "Custom Footer Action"
  );
});

// Check if the Modal component closes when the escape key is pressed
it("renders the Modal and checks if escape key closes the modal", () => {
  const mockOnClose = vi.fn();
  render(
    <Modal isOpen={true} title={"Test Modal"} onClose={mockOnClose}>
      <div>Modal Content</div>
    </Modal>
  );

  // Simulate pressing the Escape key
  const event = new KeyboardEvent("keydown", { key: "Escape" });
  document.dispatchEvent(event);

  // Assert that the mock function was called
  expect(mockOnClose).toHaveBeenCalledTimes(1);
});
