import React, { ReactElement, useEffect } from "react";
import { But<PERSON> } from "@gx-design/button";
import { Icon } from "@gx-design/icon";
import { createPortal } from "react-dom";
import clsx from "clsx";

const DEFAULT_LABELS = {
  closeLabel: "Cancel",
  confirmLabel: "Confirm",
};

export type ModalProps = {
  isOpen: boolean;
  title: string | ReactElement;
  onClose: () => void;
  onConfirm?: () => void;
  headerActions?: ReactElement;
  closeAction?: boolean;
  size?: "small" | "large" | "fullScreen";
  bodyHeight?: "default" | "minHeight" | "maxHeight";
  className?: string;
  footer?: "default" | ReactElement;
  labels?: {
    closeLabel: string;
    confirmLabel: string;
  };
  children: React.ReactNode;
};

// Leaving there for future...

// export type IModalContext = {
//   showModal: React.Dispatch<
//     React.SetStateAction<ReactElement<ModalProps> | false>
//   >;
// };

// export const ModalContext = createContext<IModalContext | null>(null);

// export const useModalContext = () => {
//   const context = useContext(ModalContext);
//   if (!context) {
//     throw new Error("useModalContext must be used within a ModalProvider");
//   }
//   return context;
// };

// export const ModalProvider: React.FC = ({ children }) => {
//   const [modal, showModal] = useState<ReactElement<ModalProps> | false>(false);

//   return (
//     <ModalContext.Provider value={{ showModal }}>
//       {children}
//       {modal && createPortal(modal, document.body)}
//     </ModalContext.Provider>
//   );
// };

export const Modal: React.FC<React.PropsWithChildren<ModalProps>> =
  React.forwardRef<HTMLDivElement, ModalProps>(
    (
      {
        isOpen = false,
        title,
        onClose,
        onConfirm,
        closeAction = true,
        headerActions,
        footer,
        size = "large",
        bodyHeight = "default",
        className,
        children,
        labels = DEFAULT_LABELS,
      },
      ref
    ) => {
      const isEscKey = (evt: KeyboardEvent) =>
        evt.key === "Escape" || evt.code === "Escape";

      const handleKeyDown = (evt: KeyboardEvent) => {
        if (closeAction && onClose && isEscKey(evt)) {
          onClose();
        }
      };

      // Scroll lock
      useEffect(() => {
        if (!isOpen) {
          return;
        }

        document.body.classList.add("gx-modal-open");

        return () => {
          document.body.classList.remove("gx-modal-open");
        };
      }, [isOpen]);

      // ESC key event listener
      useEffect(() => {
        if (!isOpen) {
          return;
        }

        document.addEventListener("keydown", handleKeyDown, false);

        return () => {
          document.removeEventListener("keydown", handleKeyDown, false);
        };
      }, [isOpen]);

      return (
        <>
          {isOpen &&
            createPortal(
              <div
                ref={ref}
                data-testid="dialog"
                aria-modal
                className={clsx(
                  "gx-modal",
                  className,
                  size && `gx-modal--${size}`
                )}
                // TODO: FIXME: this was added to not break tests into MLS
                role="dialog"
              >
                <div className="gx-modal__dialog" data-testid="modal-dialog">
                  <div
                    className="gx-modal__content"
                    data-testid="modal-content"
                  >
                    <div
                      className="gx-modal__header"
                      data-testid="modal-header"
                    >
                      <h4 className="gx-modal__title" data-testid="modal-title">
                        {title}
                      </h4>
                      {headerActions && headerActions}
                      {closeAction && (
                        <Button
                          variant="ghost"
                          className="gx-modal__close"
                          iconOnly
                          onClick={onClose}
                          data-testid="modal-close"
                        >
                          <Icon name="cross" />
                        </Button>
                      )}
                    </div>
                    <div
                      className={clsx(
                        "gx-modal__body",
                        className,
                        bodyHeight !== "default" &&
                          `gx-modal__body--${bodyHeight}`
                      )}
                      data-testid="modal-body"
                    >
                      {children}
                    </div>
                    {footer && (
                      <div
                        className="gx-modal__footer"
                        data-testid="modal-footer"
                      >
                        {footer === "default" ? (
                          <>
                            {closeAction && (
                              <Button variant="ghost" onClick={onClose}>
                                {labels.closeLabel}
                              </Button>
                            )}
                            <Button variant="accent" onClick={onConfirm}>
                              {labels.confirmLabel}
                            </Button>
                          </>
                        ) : (
                          footer
                        )}
                      </div>
                    )}
                  </div>
                </div>
              </div>,
              document.body
            )}
        </>
      );
    }
  );

Modal.displayName = "Modal";
