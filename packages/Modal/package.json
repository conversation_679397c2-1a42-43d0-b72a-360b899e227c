{"name": "@gx-design/modal", "version": "5.3.20", "description": "Gx Design Modal component", "source": "src/Modal.tsx", "main": "dist/index.js", "module": "dist/index.mjs", "peerDependencies": {"react": "*", "react-dom": "*"}, "dependencies": {"@gx-design/button": "^5.3.13", "@gx-design/icon": "^5.6.3", "@gx-design/theme": "^1.4.0", "clsx": "1.1.1"}, "devDependencies": {"tsup": "^7.2.0"}, "scripts": {"build": "tsup", "watch": "tsup --watch"}, "repository": {"type": "git", "url": "********************:getrix/gx-design.git", "directory": "packages/Modal"}, "typings": "dist/index.d.ts", "volta": {"extends": "../../package.json"}}