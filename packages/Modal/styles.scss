// ==========================================================================
// Modal - Components/Modal
// ==========================================================================
@use "@gx-design/theme/styles" as *;
@use "@gx-design/tools/styles" as *;

$header-height: 6.4rem;
$footer-height: 7.2rem;

$modalSize: (
  small: 40rem,
  large: 80rem,
  fullScreen: 100%,
);

// -------------------------------------------------------
// Utility class to append to body when modal is open
// -------------------------------------------------------
.gx-modal-open {
  overflow: hidden;

  .gtx-ask-help {
    display: none;
  }
}

// -------------------------------------------------------
// Modal styling
// -------------------------------------------------------
.gx-modal {
  $modal: &;

  position: fixed;
  width: 100%;
  top: 0;
  left: 0;
  bottom: 0;
  right: 0;
  @include z-index(modal);
  overflow-x: hidden;
  overflow-y: auto;
  background-color: rgba(color(background-reversed, true), 0.5);

  @each $modalType, $size in $modalSize {
    &--#{$modalType} {
      @if ($modalType != "fullScreen") {
        #{$modal}__content {
          @if ($modalType == "small") {
            width: 100%;
            @include media("screen", ">=#{breakpoint(sm)}") {
              width: $size;
            }
          } @else {
            width: $size;
          }
        }
      } @else {
        #{$modal}__dialog {
          margin: 0;
          height: $size;
        }
        #{$modal}__content {
          width: $size;
          height: $size;
        }
      }
    }
  }

  &__dialog {
    display: flex;
    justify-content: center;
    height: 100%;
  }

  &__content {
    position: relative;
    @include z-index(modal, 1);
    background-color: color(background-main);
    max-width: 100%;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
  }

  &__header {
    display: flex;
    align-items: center;
    position: relative;
    height: 4.8rem;
    padding: 0 space(2xl) 0 space(xs);
    border-bottom: 0.1rem solid color(border-main);
  }

  &__back {
    margin-left: -#{space(sm)};
  }

  &__title {
    text-overflow: ellipsis;
    overflow: hidden;
    white-space: nowrap;
    padding-right: space(md);
    padding-left: space(sm);
    color: color(content-high);
    @include typography(title-2);
  }

  &__close {
    background-color: transparent;
    position: absolute;
    right: 0;
    top: 50%;
    transform: translateY(-50%);
    border-radius: 0;

    &:hover {
      background-image: linear-gradient(
        to top,
        rgba(color(background-reversed, true), 0.06),
        rgba(color(background-reversed, true), 0.06)
      );
    }
  }

  &__body {
    padding: space(md);
    flex: 1;
    overflow-y: auto;

    &Loading {
      position: relative;
      width: 100%;
      min-height: 16rem;
    }

    &--onEdge {
      padding: 0;
    }
  }

  &__footer {
    display: flex;
    justify-content: flex-end;
    align-items: center;
    padding: space(md);
    border-top: 0.1rem solid color(border-main);
  }

  @include media("screen", ">=#{breakpoint(sm)}") {
    &--fullScreen {
      #{$modal}__body {
        flex: 1;
      }
    }

    &__dialog {
      align-items: center;
      height: auto;
      min-height: calc(100% - #{space(2xl) * 2});
      margin: space(2xl) auto;

      #{$modal}:has(#{$modal}__body--maxHeight) & {
        height: calc(100% - #{space(2xl) * 2});
      }
    }

    &__content {
      width: 60rem;
      height: auto;
    }

    &__header {
      height: $header-height;
      padding: 0 space(3xl) 0 space(sm);
    }

    &__title {
      @include typography(title-1);
      padding-left: space(lg);
    }

    &__back {
      margin-left: -#{space(lg)};
    }

    &__close {
      flex-shrink: 0;
      right: space(sm);
    }

    &__body {
      padding: space(md) space(xl);

      &--maxHeight {
        max-height: calc(
          100vh - #{$header-height + $footer-height} - #{space(3xl)}
        );
      }

      &--minHeight {
        min-height: 20rem;
      }
    }

    &__footer {
      padding: space(md) space(xl);
    }
  }
}
