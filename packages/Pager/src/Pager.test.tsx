import { it, expect, describe, vi } from "vitest";
import { Pager, generatePageItems, range } from "./Pager";
import { render } from "../../../test/utilities";
import React from "react";

describe("range", () => {
  it("returns an array of numbers within the given range", () => {
    expect(range(1, 5)).toEqual([1, 2, 3, 4, 5]);
    expect(range(0, 3)).toEqual([0, 1, 2, 3]);
    expect(range(-2, 2)).toEqual([-2, -1, 0, 1, 2]);
  });

  it.fails("returns an empty array if start is greater than end", () => {
    expect(range(5, 1)).toEqual([]);
  });
});

describe("generatePageItems", () => {
  it("should return an array used to display the pagination", () => {
    expect(generatePageItems(6, 10, 3)).toEqual([1, "...", 5, 6, 7, "...", 10]);
    expect(generatePageItems(6, 10, 1)).toEqual([1, "...", 6, "...", 10]);
    expect(generatePageItems(3, 10, 3)).toEqual([1, 2, 3, 4, "...", 10]);
  });

  it("should throw error if current page is greater than total pages", () => {
    expect(() => generatePageItems(6, 5, 3)).toThrowError(
      "Invalid array length" // <- FIXME: this is not the error we want to throw
    );
  });
});

const renderPager = (props: { activePage: number } = { activePage: 6 }) => {
  const onPageClickMock = vi.fn();

  const pager = render(
    <Pager
      activePage={props.activePage}
      maxPagesToShow={3}
      onPageClick={onPageClickMock}
      totalPages={10}
    />
  );

  return {
    onPageClickMock,
    ...pager,
  };
};

it("should render the pager", () => {
  const { getByText, getAllByText } = renderPager();

  expect(getByText("1")).toBeInTheDocument();
  expect(getAllByText("...")).toHaveLength(2);
  expect(getByText("5")).toBeInTheDocument();
  expect(getByText("6")).toBeInTheDocument();
  expect(getByText("7")).toBeInTheDocument();
  expect(getByText("10")).toBeInTheDocument();
});

it("should call onPageClick when clicking on the arrows", async () => {
  const { getAllByRole, user, onPageClickMock } = renderPager();

  const buttons = getAllByRole("button");
  await user.click(buttons[0]);
  expect(onPageClickMock).toHaveBeenCalledWith(5);

  await user.click(buttons[buttons.length - 1]);
  expect(onPageClickMock).toHaveBeenCalledWith(7);
});

it("should call onPageClick when clicking on the page number", async () => {
  const { user, getByRole, onPageClickMock } = renderPager();

  await user.click(getByRole("button", { name: "7" }));
  expect(onPageClickMock).toHaveBeenCalledWith(7);
});

it("should not call onPageClick when clicking on the active page", async () => {
  const { user, getByRole, onPageClickMock } = renderPager();

  await user.click(getByRole("button", { name: "6" }));
  expect(onPageClickMock).not.toHaveBeenCalledWith(6);
});

it("should render the correct number of pages", () => {
  const { getAllByRole } = renderPager();
  const buttons = getAllByRole("button");
  expect(buttons).toHaveLength(9); // why 9 and not 7? because of the 2 arrow buttons
});

it("should disable the previous button on the first page", () => {
  const { getAllByRole } = renderPager({ activePage: 1 });
  const prevButton = getAllByRole("button")[0];
  expect(prevButton).toBeDisabled();
});

it("should disable the next button on the last page", () => {
  const { getAllByRole } = renderPager({ activePage: 10 });
  const buttons = getAllByRole("button");
  const nextButton = buttons[buttons.length - 1];
  expect(nextButton).toBeDisabled();
});

it("should call onPageClick when clicking on a page button", async () => {
  const { user, getByRole, onPageClickMock } = renderPager();
  const pageButton = getByRole("button", { name: "7" });
  await user.click(pageButton);
  expect(onPageClickMock).toHaveBeenCalledWith(7);
});

it("should not call onPageClick when clicking on the active page", async () => {
  const { user, getByRole, onPageClickMock } = renderPager();
  const activePageButton = getByRole("button", { name: "6" });
  await user.click(activePageButton);
  expect(onPageClickMock).not.toHaveBeenCalled();
});
