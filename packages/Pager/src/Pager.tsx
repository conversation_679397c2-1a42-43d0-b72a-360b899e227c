import React from "react";
import { Icon } from "@gx-design/icon";
import { Button } from "@gx-design/button";

export type PagerProps = {
  /**
    Number of total pages
  */
  totalPages: number;
  /**
    Current active page
  */
  activePage: number;
  /**
   * Number of pages to ideally show nearby, if current page is in the middle of the range;
   * this number includes the current page and nearly siblings, and will change accordingly
   * to show a reasonable ui.
   */
  maxPagesToShow: 1 | 3 | 5;
  /**
    <PERSON><PERSON> to call if a page is clicked
  */
  onPageClick: (pageNum: number) => void;
};

type PagerItem = number | "...";

export const range = (start: number, end: number) => {
  return Array(end - start + 1)
    .fill(0)
    .map((_, idx) => start + idx);
};

export const generatePageItems = (
  current: number,
  total: number,
  maxToShow: number
): PagerItem[] => {
  const coefficent = Math.trunc(maxToShow / 2);

  const generateNextItems = (): PagerItem[] => {
    if (total === current) return [];
    const tmp: number[] =
      coefficent === 1
        ? current === 1
          ? // always showing at least 2 pages after page 1 if current is 1
            [current + 1, current + 2]
          : [current + 1]
        : range(current + 1, current + coefficent);

    if (!tmp.length) {
      // here if maxToShow === 1
      if (total - current > 2) return ["...", total];
      else return range(current + 1, total);
    }
    const lastNear = tmp.slice(-1)[0];
    if (total - lastNear > 2) return [...tmp, "...", total];
    else return range(current + 1, total);
  };

  const generatePrevItems = (): PagerItem[] => {
    if (total === 1 || current === 1) return [];
    const tmp: number[] =
      coefficent === 1
        ? current === total
          ? // always showing at least 2 pages before last page if current is last page
            [current - 2, current - 1]
          : [current - 1]
        : range(current - coefficent, current - 1);

    if (!tmp.length) {
      // here if maxToShow === 1
      if (current - 1 > 2) return [1, "..."];
      else return range(1, current - 1);
    }
    const firstNear = tmp[0];
    if (firstNear - 1 > 2) return [1, "...", ...tmp];
    else return range(1, current - 1);
  };

  return [...generatePrevItems(), current, ...generateNextItems()];
};

export const Pager: React.FC<PagerProps> = ({
  totalPages,
  activePage,
  maxPagesToShow,
  onPageClick,
}) => (
  <div className="gx-pager gx-buttonGroup">
    <Button
      iconOnly
      onClick={() => onPageClick(activePage - 1)}
      variant="ghost"
      disabled={activePage === 1}
    >
      <Icon name="arrow-left" />
    </Button>
    {generatePageItems(activePage, totalPages, maxPagesToShow).map(
      (page, i) => {
        return (
          <Button
            onClick={
              typeof page === "number" && activePage !== page
                ? () => onPageClick(page)
                : undefined
            }
            key={`pager_item_${page}_${i}`}
            variant={activePage === page ? "accent" : "ghost"}
            disabled={typeof page === "string"}
          >
            {page}
          </Button>
        );
      }
    )}
    <Button
      iconOnly
      onClick={() => onPageClick(activePage + 1)}
      disabled={activePage === totalPages}
      variant="ghost"
    >
      <Icon name="arrow-right" />
    </Button>
  </div>
);
