// ==========================================================================
// Pager - Pager/Components
// ==========================================================================
@use "@gx-design/theme/styles" as *;

$gx-pager: "gx-pager";

.#{$gx-pager} {
  display: flex;
  justify-content: center;

  .gx-button {
    min-width: 4rem;
    padding: 0;

    &--link {
      &:not(:first-child):not(:last-child) {
        color: color(content-selectable);
        background-color: color(background-selectable);
      }
    }

    &--primary {
      color: color(content-selected);
      background-color: color(background-selected);
    }
  }
}
