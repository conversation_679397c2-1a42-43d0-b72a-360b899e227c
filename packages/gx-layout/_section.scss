// ==========================================================================
// Layout - Section
// ==========================================================================
@use "@gx-design/theme/styles" as theme;

.gx-section {
  & + & {
    padding-top: theme.space(xl);
    margin-top: theme.space(sm);
    border-top: 0.1rem solid theme.color(border-main);
  }

  .gx-title-1 {
    display: flex;
    align-items: center;
    margin-bottom: theme.space(lg);

    > * + * {
      margin-left: theme.space(sm);
    }
  }

  &--paddingBottom {
    padding-bottom: theme.space(md);
  }
}
