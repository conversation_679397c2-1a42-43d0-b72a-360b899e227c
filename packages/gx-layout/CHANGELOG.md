# Change Log

All notable changes to this project will be documented in this file.
See [Conventional Commits](https://conventionalcommits.org) for commit guidelines.

## [1.3.3](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/layout@1.3.2...@gx-design/layout@1.3.3) (2025-09-08)

**Note:** Version bump only for package @gx-design/layout

## [1.3.2](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/layout@1.3.1...@gx-design/layout@1.3.2) (2025-01-15)

**Note:** Version bump only for package @gx-design/layout

## [1.3.1](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/layout@1.3.0...@gx-design/layout@1.3.1) (2024-10-31)

**Note:** Version bump only for package @gx-design/layout

# [1.3.0](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/layout@1.2.0...@gx-design/layout@1.3.0) (2024-09-23)

### Features

- **css:** custom properties ([cbc56c0](https://gitlab.pepita.io/getrix/gx-design/commit/cbc56c0e3e675d85f92ad25ac135cce270f02b2b))

## [1.2.1-alpha.0](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/layout@1.2.0...@gx-design/layout@1.2.1-alpha.0) (2024-09-16)

**Note:** Version bump only for package @gx-design/layout

# [1.2.0](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/layout@1.1.2...@gx-design/layout@1.2.0) (2024-08-29)

### Features

- **colors:** pro new colors ([2103ea7](https://gitlab.pepita.io/getrix/gx-design/commit/2103ea76597b5ae75018d2d62947c6b01d613e82))

# [1.2.0-alpha.0](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/layout@1.1.2...@gx-design/layout@1.2.0-alpha.0) (2024-07-26)

### Features

- **colors:** pro new colors ([d88639f](https://gitlab.pepita.io/getrix/gx-design/commit/d88639f983895257b905dfad2fb65db3e75efee0))

## [1.1.3-alpha.0](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/layout@1.1.2...@gx-design/layout@1.1.3-alpha.0) (2024-07-22)

**Note:** Version bump only for package @gx-design/layout

## [1.1.2](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/layout@1.1.1...@gx-design/layout@1.1.2) (2024-03-26)

**Note:** Version bump only for package @gx-design/layout

## [1.1.1](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/layout@1.1.0...@gx-design/layout@1.1.1) (2024-03-20)

**Note:** Version bump only for package @gx-design/layout

# 1.1.0 (2024-03-18)

**Note:** Version bump only for package @gx-design/layout

# [1.1.0-alpha.4](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/layout@1.1.0-alpha.3...@gx-design/layout@1.1.0-alpha.4) (2024-03-11)

**Note:** Version bump only for package @gx-design/layout

# [1.1.0-alpha.3](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/layout@1.1.0-alpha.2...@gx-design/layout@1.1.0-alpha.3) (2024-03-11)

**Note:** Version bump only for package @gx-design/layout

# [1.1.0-alpha.2](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/layout@1.1.0-alpha.1...@gx-design/layout@1.1.0-alpha.2) (2024-03-11)

**Note:** Version bump only for package @gx-design/layout

# [1.1.0-alpha.1](https://gitlab.pepita.io/getrix/gx-design/compare/@gx-design/layout@1.1.0-alpha.0...@gx-design/layout@1.1.0-alpha.1) (2024-03-11)

**Note:** Version bump only for package @gx-design/layout

# 1.1.0-alpha.0 (2024-03-07)

### Features

- **styles:** general ([fc4d50a](https://gitlab.pepita.io/getrix/gx-design/commit/fc4d50a8c23c848f36e9a3c9831cea4606eff985))
