// ==========================================================================
// Layout - Grid
// ==========================================================================
@use "@gx-design/theme/styles" as theme;
@use "@gx-design/tools/styles" as tools;

// Set the number of columns you want to use on your layout.
$flexboxgrid-grid-columns: 12 !default;
// Set the gutter between columns.
$flexboxgrid-gutter-width: 3.2rem !default;
// Set a margin for the container sides.
$flexboxgrid-outer-margin: 3.2rem !default;
// Create or remove breakpoints for your project
// Syntax:
// name SIZErem,
$flexboxgrid-breakpoints: sm theme.emConverter(768), md theme.emConverter(1024),
  lg theme.emConverter(1280), xlg theme.emConverter(1440) !default;
$flexboxgrid-max-width: 120rem !default;

//
// -- Stop editing -- //
//

$gutter-compensation: $flexboxgrid-gutter-width * 0.5 * -1;
$half-gutter-width: $flexboxgrid-gutter-width * 0.5;

.wrapper {
  max-width: $flexboxgrid-max-width;
  margin: 0 auto;
}

.container-fluid {
  margin-right: auto;
  margin-left: auto;
  padding-right: $flexboxgrid-outer-margin;
  padding-left: $flexboxgrid-outer-margin;
}

.gx-row {
  display: flex;
  flex-grow: 0;
  flex-shrink: 1;
  flex-basis: auto;
  flex-direction: row;
  flex-wrap: wrap;
  margin-right: $gutter-compensation;
  margin-left: $gutter-compensation;
}

.gx-row.gx-reverse {
  flex-direction: row-reverse;
}

.gx-col.gx-reverse {
  flex-direction: column-reverse;
}

@mixin flexboxgrid-sass-col-common {
  // split @include flex(0, 0, auto) into individual props
  flex-grow: 0;
  flex-shrink: 0;

  // we leave @include flex-basis(auto) out of common because
  // in some spots we need it and some we dont
  // more why here: https://github.com/kristoferjoseph/flexboxgrid/issues/126

  padding-right: $half-gutter-width;
  padding-left: $half-gutter-width;
}

$name: xs;
.gx-col-#{$name} {
  @include flexboxgrid-sass-col-common;
  flex-basis: auto;
}
.gx-pushtop-#{$name} {
  margin-top: theme.space(lg);
}
@for $i from 1 through $flexboxgrid-grid-columns {
  .gx-col-#{$name}-#{$i} {
    @include flexboxgrid-sass-col-common;
    flex-basis: calc(100% / #{$flexboxgrid-grid-columns} * #{$i});
    max-width: 100% / $flexboxgrid-grid-columns * $i;
  }
}
@for $i from 0 through $flexboxgrid-grid-columns {
  .gx-col-#{$name}-offset-#{$i} {
    @include flexboxgrid-sass-col-common;
    @if $i == 0 {
      margin-left: 0;
    } @else {
      margin-left: 100% / $flexboxgrid-grid-columns * $i;
    }
  }
}
.gx-col-#{$name} {
  flex-grow: 1;
  flex-basis: 0;
  max-width: 100%;
}
.gx-start-#{$name} {
  justify-content: flex-start;
  text-align: left;
}

.gx-center-#{$name} {
  justify-content: center;
  text-align: center;
}

.gx-end-#{$name} {
  justify-content: flex-end;
  text-align: right;
}

.gx-top-#{$name} {
  align-items: flex-start;
}

.gx-middle-#{$name} {
  align-items: center;
}

.gx-bottom-#{$name} {
  align-items: flex-end;
}

.gx-around-#{$name} {
  justify-content: space-around;
}

.gx-between-#{$name} {
  justify-content: space-between;
}

.gx-first-#{$name} {
  order: -1;
}

.gx-last-#{$name} {
  order: 1;
}

@each $breakpoint in $flexboxgrid-breakpoints {
  $name: nth($breakpoint, 1);
  $size: nth($breakpoint, 2);
  @media (min-width: $size) {
    .gx-col-#{$name} {
      @include flexboxgrid-sass-col-common;
      flex-basis: auto;
    }
    @for $i from 1 through $flexboxgrid-grid-columns {
      .gx-col-#{$name}-#{$i} {
        @include flexboxgrid-sass-col-common;
        flex-basis: calc(100% / #{$flexboxgrid-grid-columns} * #{$i});
        max-width: 100% / $flexboxgrid-grid-columns * $i;
      }

      // Usefull when there are 2 elements side by side: one has a label and the other has not
      .gx-pushtop-#{$name} {
        margin-top: theme.space(lg);
      }
    }
    @for $i from 0 through $flexboxgrid-grid-columns {
      .gx-col-#{$name}-offset-#{$i} {
        @include flexboxgrid-sass-col-common;
        @if $i == 0 {
          margin-left: 0;
        } @else {
          margin-left: 100% / $flexboxgrid-grid-columns * $i;
        }
      }
    }
    .gx-col-#{$name} {
      flex-grow: 1;
      flex-basis: 0;
      max-width: 100%;
    }
    .gx-start-#{$name} {
      justify-content: flex-start;
      text-align: left;
    }

    .gx-center-#{$name} {
      justify-content: center;
      text-align: center;
    }

    .gx-end-#{$name} {
      justify-content: flex-end;
      text-align: right;
    }

    .gx-top-#{$name} {
      align-items: flex-start;
    }

    .gx-middle-#{$name} {
      align-items: center;
    }

    .gx-bottom-#{$name} {
      align-items: flex-end;
    }

    .gx-around-#{$name} {
      justify-content: space-around;
    }

    .gx-between-#{$name} {
      justify-content: space-between;
    }

    .gx-first-#{$name} {
      order: -1;
    }

    .gx-last-#{$name} {
      order: 1;
    }
  }
}

.gx-box-row {
  margin-bottom: theme.space(lg);

  &__grow {
    flex-grow: 1;
  }

  &--spaceBetween {
    display: flex;
    justify-content: space-between;
    align-items: center;

    > *:first-child {
      width: 100%;
    }

    > * + * {
      margin-left: theme.space(md);
    }
  }
}
