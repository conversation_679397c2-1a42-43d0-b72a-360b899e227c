import React, { InputHTMLAttributes, forwardRef } from "react";
import { Icon, IconProps } from "@gx-design/icon";
import { Tooltip } from "@gx-design/tooltip";
import { HelperText } from "@gx-design/helper-text";
import clsx from "clsx";

export type AddonInputProps = {
  /**
   * The label title
   */
  label: string;
  /**
   * The tooltip helper text
   */
  tooltipHelper?: string;
  /**
   * Determine if label is visible to user
   */
  isLabelVisible?: boolean;
  /**
   * The error message
   */
  error?: string;
  /**
   * Configuration for the addon (icon or text)
   */
  addon: AddonIconOptions | AddonTextOptions;
} & InputHTMLAttributes<HTMLInputElement>;

// Type definition for icon addon options
type AddonIconOptions = {
  position: "left" | "right";
  type: "icon";
  value: IconProps["name"];
};

// Type definition for text addon options
type AddonTextOptions = {
  position: "left" | "right";
  type: "text";
  value: string;
};

export const AddonInput = forwardRef<HTMLInputElement, AddonInputProps>(
  (
    {
      label,
      tooltipHelper,
      isLabelVisible = true,
      error = "",
      addon,
      ...props
    },
    ref
  ) => {
    const renderAddon = () => {
      return (
        <div
          className={clsx("gx-input-addon", {
            "gx-input-addon--withIcon": addon.type === "icon",
          })}
        >
          {addon.type === "icon" && <Icon name={addon.value} />}
          {addon.type === "text" && <>{addon.value}</>}
        </div>
      );
    };

    return (
      <div className="gx-input-wrapper">
        <label
          htmlFor={props.id}
          className={clsx("gx-label", {
            "gx-sr-only": !isLabelVisible,
          })}
        >
          {label}
          {props.required && <span className="gx-label__required">*</span>}
          {tooltipHelper && (
            <span className="gx-tip">
              <Tooltip position="top" text={tooltipHelper}>
                <Icon className="gx-icon--info" name="info-circle" />
              </Tooltip>
            </span>
          )}
        </label>
        <div
          className={clsx("gx-input-addon-wrapper", {
            "gx-input-addon-wrapper--negative": error,
          })}
        >
          {addon.position === "left" && renderAddon()}
          <input
            ref={ref}
            className={clsx("gx-input", "gx-input--withAddon", {
              "gx-input--negative": error,
            })}
            {...props}
          />
          {addon.position === "right" && renderAddon()}
        </div>
        {error && <HelperText text={error} style="error" />}
      </div>
    );
  }
);

AddonInput.displayName = "AddonInput";
