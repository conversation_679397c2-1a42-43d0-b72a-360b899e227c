{"name": "@gx-design/addon-input", "version": "5.2.16", "description": "Gx Design AddonInput component", "source": "src/AddonInput.tsx", "main": "dist/index.js", "module": "dist/index.mjs", "peerDependencies": {"react": "*"}, "devDependencies": {"tsup": "^7.2.0"}, "dependencies": {"@gx-design/helper-text": "^5.2.14", "@gx-design/icon": "^5.6.3", "@gx-design/tooltip": "^3.2.4", "clsx": "1.1.1"}, "scripts": {"build": "tsup", "watch": "tsup --watch"}, "repository": {"type": "git", "url": "********************:getrix/gx-design.git", "directory": "packages/AddonInput"}, "typings": "dist/index.d.ts", "volta": {"extends": "../../package.json"}}