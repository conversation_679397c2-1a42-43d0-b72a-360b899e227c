// ==========================================================================
// Table data - Components/Table data
// ==========================================================================
@use "@gx-design/theme/styles" as *;
@use "@gx-design/tools/styles" as *;

.gx-table-data {
  width: 100%;
  @include typography(body-small);

  &Wrap {
    overflow-y: auto;
  }

  td,
  th {
    padding: space(sm);

    &:first-child {
      padding-left: 0;
    }

    &:last-child {
      padding-right: 0;
    }
  }

  th {
    text-align: left;
    color: color(content-medium);
    text-transform: uppercase;
    @include typography(body-tiny);
  }

  tr {
    border-bottom: 0.1rem solid color(border-main);
  }

  &__summary {
    td:first-child {
      padding-right: space(md);
      color: color(content-high);
      text-align: right;
      text-transform: uppercase;
      @include typography(body-tiny);
    }
  }
}
