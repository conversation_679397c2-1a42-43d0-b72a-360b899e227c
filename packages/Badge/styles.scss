// ==========================================================================
// Badges - Components
// ==========================================================================
@use "sass:map";
@use "@gx-design/theme/styles" as *;

$gx-badge: "gx-badge";
$gx-badge-size: 2.4rem;

// Colors map
$gx-badge-types: (
  reversed: (
    color: "content-reversed",
    background-color: "background-reversed",
  ),
  brand: (
    color: "content-reversed",
    background-color: "background-brand",
  ),
  promotion: (
    color: "content-reversed",
    background-color: "background-notification",
  ),
  success: (
    color: "content-success",
    background-color: "background-success",
    border-color: "border-success",
    contrast: true,
  ),
  success-high: (
    color: "content-reversed",
    background-color: "background-success-high",
  ),
  warning: (
    color: "content-warning",
    background-color: "background-warning",
    border-color: "border-warning",
    contrast: true,
  ),
  warning-high: (
    color: "content-reversed",
    background-color: "background-warning-high",
  ),
  error: (
    color: "content-error",
    background-color: "background-error",
    border-color: "border-error",
    contrast: true,
  ),
  error-high: (
    color: "content-reversed",
    background-color: "background-error-high",
  ),
  info: (
    color: "content-info",
    background-color: "background-info",
    border-color: "border-info",
    contrast: true,
  ),
  info-high: (
    color: "content-reversed",
    background-color: "background-info-high",
  ),
);

.#{$gx-badge} {
  display: inline-flex;
  justify-content: center;
  align-items: center;
  min-width: $gx-badge-size;
  height: $gx-badge-size;
  padding-left: space(sm);
  padding-right: space(sm);
  border: 0.1rem solid color(border-main);
  border-radius: radius(sm);
  color: color(content-high);
  background-color: color(background-alt);
  flex-shrink: 0;
  white-space: nowrap;
  @include typography(body-small);

  & + & {
    margin-left: space(md);
  }

  svg {
    @include icon-size(xs);
  }

  * + * {
    margin-left: space(sm);
  }

  &--iconOnly {
    padding-left: 0;
    padding-right: 0;
  }

  @each $gx-badge-type, $colors in $gx-badge-types {
    &--#{$gx-badge-type} {
      color: color(map.get($colors, "color"));
      background-color: color(map.get($colors, "background-color"));
      border-color: transparent;

      @if (map.get($colors, "contrast")) {
        span,
        &.#{$gx-badge}--iconOnly svg {
          color: color(content-high);
        }
      }

      @if (map.get($colors, "border-color")) {
        border: 0.1rem solid color(map.get($colors, "border-color"));
      }
    }
  }

  //TO-DO: andrebbe spostato?
  // badge visibility
  $visibility-colors: (
    showcase: #377fa6,
    vetrina: #377fa6,
    star: #e3af3f,
    top: #987703,
    premium: #666666,
    sky: #13aaec,
    secret: #373c47,
  );

  $visibility-colors-lu: (
    showcase: #e66221,
    vetrina: #e66221,
    star: #13aaec,
    top: #13aaec,
    premium: #00577d,
    sky: #e66221,
    secret: #373c47,
  );

  &[class*="visibility-"] {
    color: color(content-accent);
    text-transform: uppercase;
  }

  @each $name, $color in $visibility-colors {
    &.visibility-#{$name} {
      background-color: $color;
      border-color: $color;
    }
  }

  @each $name, $color in $visibility-colors-lu {
    .app-domain-immotoppro-lu &.visibility-#{$name} {
      background-color: $color;
      border-color: $color;
    }
  }
}
