import React, { JSX } from "react";
import { Icon, IconProps } from "@gx-design/icon";
import clsx from "clsx";

export type BadgeProps = {
  /**
   The style of the Badge
  */
  style?:
    | "reversed"
    | "brand"
    | "promotion"
    | "success"
    | "success-high"
    | "warning"
    | "warning-high"
    | "error"
    | "error-high"
    | "info"
    | "info-high";
  /**
   The text inside the badge
  */
  text?: string;
  /**
   Determine if badge has an icon on the left side
  */
  icon?: IconProps["name"];
  /**
  Custom class names
  */
  className?: any;
  /**
    Icon class name
  */
  iconClassName?: string;
} /**
    At least one of text and icon must be declared.
  */ & ({ text: string } | { icon: IconProps["name"] }) &
  JSX.IntrinsicElements["div"];

export const Badge: React.FC<BadgeProps> = React.forwardRef<
  HTMLDivElement,
  BadgeProps
>(({ style, text, icon, className, iconClassName, ...rest }, ref) => {
  // Check that at least one of text or icon is defined
  if (!text && !icon) {
    return null;
  }

  // Check if only icon is defined (and the text is not defined).
  const isIconOnly = !!icon && !text;

  return (
    <div
      ref={ref}
      className={clsx(
        "gx-badge",
        isIconOnly && "gx-badge--iconOnly",
        style && `gx-badge--${style}`,
        className
      )}
      {...rest}
    >
      {icon && (
        <Icon name={icon} className={`${iconClassName && iconClassName}`} />
      )}
      {text && <span>{text}</span>}
    </div>
  );
});

Badge.displayName = "Badge";
