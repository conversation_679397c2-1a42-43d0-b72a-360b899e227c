import React from "react";
import { render, screen } from "../../../test/utilities";
import { Badge } from "./Badge";
import { it, expect } from "vitest";

it("renders the badge with the correct text", () => {
  render(<Badge text="This is a badge" />);
  const badge = screen.getByText("This is a badge");
  expect(badge).toBeInTheDocument();
});

const styles = [
  "reversed",
  "brand",
  "promotion",
  "success",
  "success-high",
  "warning",
  "warning-high",
  "error",
  "error-high",
  "info",
  "info-high",
];

styles.forEach((style) => {
  it(`renders the badge with the correct style class for ${style}`, () => {
    render(<Badge text={style} style={style} />);
    const badge = screen.getByText(style);
    expect(badge.parentElement).toHaveClass(`gx-badge--${style}`);
  });
});

it("renders the badge with an icon only", () => {
  render(<Badge icon="check" />);
  const icon = document.querySelector(".gx-icon");
  expect(icon.parentElement).toHaveClass("gx-badge--iconOnly");
});

it("renders the badge with both icon and text", () => {
  render(<Badge text="Info" icon="info" />);
  const badge = screen.getByText("Info");
  const icon = document.querySelector(".gx-icon");
  expect(badge).toBeInTheDocument();
  expect(icon).toBeInTheDocument();
});

it("applies custom className if provided", () => {
  render(<Badge text="Badge with custom class" className="custom-class" />);
  const badge = screen.getByText("Badge with custom class");
  expect(badge.parentElement).toHaveClass("custom-class");
});
