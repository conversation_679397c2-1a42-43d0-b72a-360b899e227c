import React from "react";
import clsx from "clsx";
import { Tooltip } from "@gx-design/tooltip";
import { Icon } from "@gx-design/icon";

export type LabelProps = React.PropsWithChildren<{
  /**
    Custom class names
  */
  className?: string;
  /**
   * Determine if label is visible
   */
  isVisible?: boolean;
  /**
   * Show required asterisk
   */
  required?: boolean;
  /**
   * The 'for' attribute for the label
   */
  htmlFor?: string;
  /**
   * Tooltip helper text
   */
  tooltipText?: string;
}>;

export const Label: React.FC<LabelProps> = ({
  className,
  children,
  isVisible = true,
  required = false,
  tooltipText,
  htmlFor,
}) => {
  return (
    <label
      htmlFor={htmlFor}
      className={clsx(
        "gx-label",
        {
          "gx-sr-only": !isVisible,
          "gx-label--required": required,
        },
        className
      )}
    >
      {children}
      {tooltipText && (
        <span className="gx-tip">
          <Tooltip position="top" text={tooltipText}>
            <Icon className="gx-icon--info" name="info-circle" />
          </Tooltip>
        </span>
      )}
    </label>
  );
};
