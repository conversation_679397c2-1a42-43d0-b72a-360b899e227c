import { expect, it } from "vitest";
import { render, screen } from "../../../test/utilities";
import { Label } from "./Label";

it("renders label text", () => {
  render(<Label>Username</Label>);
  expect(screen.getByText("Username")).toBeInTheDocument();
});

it("applies 'gx-sr-only' class when isVisible is false", () => {
  render(<Label isVisible={false}>Hidden Label</Label>);
  const labelElement = screen.getByText("Hidden Label");
  expect(labelElement).toHaveClass("gx-sr-only");
});

it("adds an asterisk when required is true", () => {
  render(<Label required>Required field</Label>);
  const labelElement = screen.getByText("Required field");
  expect(labelElement).toBeInTheDocument();
  expect(labelElement).toHaveClass("gx-label--required");
});

it("renders tooltip helper text when provided", async () => {
  const { baseElement, user } = render(
    <Label tooltipText="This is the tooltip">Username</Label>
  );
  expect(screen.getByText("Username")).toBeInTheDocument();

  const tooltipIcon = baseElement.querySelector(".gx-icon--info");
  expect(tooltipIcon).toBeInTheDocument();

  if (tooltipIcon) {
    await user.hover(tooltipIcon);
    const tooltipElement = await screen.findByText("This is the tooltip");
    expect(tooltipElement).toBeInTheDocument();
  } else {
    expect.fail("Icon element not found");
  }
});

it("renders with the correct htmlFor attribute", () => {
  render(<Label htmlFor="username-input">Username</Label>);
  const labelElement = screen.getByText("Username");
  expect(labelElement).toHaveAttribute("for", "username-input");
});

it("renders with the correct custom class", () => {
  render(<Label className="custom-class">Username</Label>);
  const labelElement = screen.getByText("Username");
  expect(labelElement).toHaveClass("custom-class");
});
