// ==========================================================================
// Label - Label/Components
// ==========================================================================
@use "@gx-design/theme/styles" as *;
@use "@gx-design/tools/styles" as *;

.gx-label {
  display: inline-flex;
  gap: space(xs);
  @include typography(body-small);
  font-weight: 600;
  margin-bottom: space(xs);
  align-items: center;
  color: color(content-high);

  // This is necessary to have the asterisk before the tooltip.
  > * {
    order: 2;
  }

  small {
    @include typography(body-tiny);
    margin-left: space(xs);
    display: inline-flex;
  }

  // This indicates that input is required
  // TODO: Remove when the Label component is included in all the components that should use it
  &__required {
    color: color(content-error);
  }

  &--required {
    &::after {
      content: "*";
      order: 1;
      color: color(content-error);
    }
  }

  // Label is an icon instead of text
  &--icon {
    margin-bottom: 0;
  }

  &--withEndElement {
    display: flex;
    align-items: center;
  }
}
