// ==========================================================================
// Tabs - Components/Tabs
// ==========================================================================
@use "@gx-design/theme/styles" as *;

.gx-tabs {
  display: flex;
  box-shadow: inset 0 -0.1rem 0 0 color(border-main);
  @include typography(body-small);

  &-content {
    display: none;

    &--active {
      display: block;
    }
  }

  &--withSpacing {
    padding: 0 space(xl);
  }

  &__item {
    display: flex;
    align-items: center;
    cursor: pointer;
    height: 4rem;
    padding: space(sm) space(md);

    color: color(content-selectable);
    //
    * + * {
      margin-left: space(sm);
    }

    .gx-icon {
      @include icon-size(sm);
    }

    &--active {
      color: color(content-selected);
      box-shadow: inset 0 -0.3rem 0 0 color(content-selected);
    }

    &:hover {
      color: color(content-selected);
    }

    .gx-skeleton {
      width: 2.4rem;
      height: 1.6rem;
      display: inline-block;
      border-radius: radius(md);
    }
  }
}
