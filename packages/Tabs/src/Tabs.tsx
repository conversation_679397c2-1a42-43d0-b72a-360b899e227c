import React, { ReactElement } from "react";
import clsx from "clsx";

export type TabsProps = {
  /**
   Custom class name
  */
  className?: string;
  /**
   Has horizontal spacing
  */
  withSpacing?: boolean;
  /**
   The array of tabs items
  */
  children: ReactElement<TabsItemProps> | Array<ReactElement<TabsItemProps>>;
};

export type TabsItemProps = {
  text: string;
  startElement?: ReactElement;
  active: boolean;
  endElement?: ReactElement;
  onClick?: any;
};

export const Tabs: React.FC<TabsProps> = ({
  children,
  className,
  withSpacing,
}) => {
  return (
    <div
      className={clsx("gx-tabs", className, {
        "gx-tabs--withSpacing": withSpacing,
      })}
      data-testid="gx-tabs"
    >
      {children}
    </div>
  );
};

export const TabsItem: React.FC<TabsItemProps> = ({
  text,
  startElement,
  active,
  endElement,
  onClick,
}) => {
  return (
    <div
      onClick={onClick && onClick}
      className={clsx("gx-tabs__item", {
        "gx-tabs__item--withStartElement": startElement,
        "gx-tabs__item--withEndElement": endElement,
        "gx-tabs__item--active": active,
      })}
    >
      {startElement && startElement}
      <span>{text}</span>
      {endElement && endElement}
    </div>
  );
};
