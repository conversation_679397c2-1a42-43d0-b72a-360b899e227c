import { describe, expect, it, vi } from "vitest";
import { render, screen } from "../../../test/utilities";
import React from "react";
import { Tabs, TabsProps, TabsItem } from "./Tabs";
import { Icon } from "../../Icon";

describe("Tabs", () => {
  it("should render TabsItem with default props", () => {
    const props: TabsProps = {
      children: [
        <TabsItem
          text="Tab 1"
          active={true}
          key="tab1"
          startElement={
            <Icon name="poi-restaurant" data-testid="ic-restaurant" />
          }
        />,
        <TabsItem text="Tab 2" active={false} key="tab2" />,
        <TabsItem
          text="Tab 3"
          active={false}
          key="tab3"
          endElement={<Icon name="poi-market" data-testid="ic-market" />}
        />,
      ],
    };
    render(<Tabs {...props} />);
    expect(screen.getByTestId("gx-tabs")).toBeInTheDocument();
    expect(screen.getByTestId("gx-tabs")).toHaveClass("gx-tabs");
    expect(screen.getByTestId("gx-tabs")).not.toHaveClass(
      "gx-tabs--withSpacing"
    );
    const textTab1 = screen.getByText("Tab 1");
    const textTab2 = screen.getByText("Tab 2");
    const textTab3 = screen.getByText("Tab 3");
    expect(textTab1).toBeInTheDocument();
    expect(textTab2).toBeInTheDocument();
    expect(textTab3).toBeInTheDocument();
    const tab1Element = textTab1.closest(".gx-tabs__item");
    const tab2Element = textTab2.closest(".gx-tabs__item");
    const tab3Element = textTab3.closest(".gx-tabs__item");
    expect(tab1Element).toHaveClass("gx-tabs__item--active");
    expect(tab2Element).not.toHaveClass("gx-tabs__item--active");
    expect(tab3Element).not.toHaveClass("gx-tabs__item--active");
    expect(tab1Element).toHaveClass("gx-tabs__item--withStartElement");
    expect(tab2Element).not.toHaveClass("gx-tabs__item--withStartElement");
    expect(tab2Element).not.toHaveClass("gx-tabs__item--withEndElement");
    expect(tab3Element).toHaveClass("gx-tabs__item--withEndElement");
    expect(screen.getByTestId("ic-restaurant")).toBeInTheDocument();
    expect(screen.getByTestId("ic-market")).toBeInTheDocument();
  });

  it("should apply custom classname to Tabs", () => {
    const props: TabsProps = {
      className: "custom-class",
      children: [<TabsItem text="Tab 1" active={true} key="tab1" />],
    };
    render(<Tabs {...props} />);
    expect(screen.getByTestId("gx-tabs")).toHaveClass("custom-class");
  });

  it("should apply spacing class when withSpacing is true", () => {
    const props: TabsProps = {
      withSpacing: true,
      children: [<TabsItem text="Tab 1" active={true} key="tab1" />],
    };
    render(<Tabs {...props} />);
    expect(screen.getByTestId("gx-tabs")).toHaveClass("gx-tabs--withSpacing");
  });

  it("should handle click events on TabsItem", () => {
    const onClickMock = vi.fn();
    const props: TabsProps = {
      children: [
        <TabsItem
          text="Tab 1"
          active={true}
          key="tab1"
          onClick={onClickMock}
        />,
      ],
    };
    render(<Tabs {...props} />);
    const tab1Element = screen.getByText("Tab 1");
    tab1Element.click();
    expect(onClickMock).toHaveBeenCalledTimes(1);
  });
});
