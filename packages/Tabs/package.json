{"name": "@gx-design/tabs", "version": "3.2.3", "description": "Gx Design Tabs component", "source": "src/Tabs.tsx", "main": "dist/index.js", "module": "dist/index.mjs", "peerDependencies": {"react": "*"}, "devDependencies": {"tsup": "^7.2.0"}, "dependencies": {"@gx-design/theme": "^1.4.0", "clsx": "1.1.1"}, "scripts": {"build": "tsup", "watch": "tsup --watch"}, "repository": {"type": "git", "url": "********************:getrix/gx-design.git", "directory": "packages/Tabs"}, "typings": "dist/index.d.ts", "volta": {"extends": "../../package.json"}}