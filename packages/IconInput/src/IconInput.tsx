import React from "react";
import { HelperText } from "@gx-design/helper-text";
import { Input } from "@gx-design/input";
import { Icon, IconProps } from "@gx-design/icon";
import clsx from "clsx";

export type IconInputProps = {
  icon?: IconProps["name"];
  isVerticalCentered: boolean;
  error?: string;
  variant?: string;
};

export const IconInput: React.FC<React.PropsWithChildren<IconInputProps>> = ({
  children = <Input label="IconInput" isLabelVisible={false} />,
  icon,
  isVerticalCentered = false,
  error = "",
  variant,
}) => {
  return (
    <div
      className={clsx("gx-icon-input", {
        "gx-icon-input--verticalCentered": isVerticalCentered,
      })}
    >
      <div className="gx-icon-input__icon">
        {icon ? <Icon name={icon} className={`gx-text-${variant}`} /> : "--"}
      </div>
      <div className="gx-input-wrapper">
        {children}
        {error && <HelperText text={error} style="error" />}
      </div>
    </div>
  );
};
