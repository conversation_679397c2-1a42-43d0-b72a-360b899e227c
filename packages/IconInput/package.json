{"name": "@gx-design/icon-input", "version": "5.2.18", "description": "Gx Design IconInput component", "source": "src/IconInput.tsx", "main": "dist/index.js", "module": "dist/index.mjs", "peerDependencies": {"react": "*"}, "devDependencies": {"tsup": "^7.2.0"}, "dependencies": {"@gx-design/helper-text": "^5.2.14", "@gx-design/input": "^5.3.15", "@gx-design/tag": "^5.2.13", "@gx-design/theme": "^1.4.0", "clsx": "1.1.1"}, "scripts": {"build": "tsup", "watch": "tsup --watch"}, "repository": {"type": "git", "url": "********************:getrix/gx-design.git", "directory": "packages/IconInput"}, "typings": "dist/index.d.ts", "volta": {"extends": "../../package.json"}}