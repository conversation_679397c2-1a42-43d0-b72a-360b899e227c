import "@testing-library/jest-dom/vitest";
import { vi } from "vitest";

// see https://developer.mozilla.org/en-US/docs/Web/API/Window/matchMedia
// solution from https://github.com/chakra-ui/chakra-ui/discussions/6664#discussioncomment-5161926
const matchMediaMock = vi.fn((query: unknown) => ({
  matches: false,
  media: query,
  onchange: null,
  addListener: vi.fn(),
  removeListener: vi.fn(),
  addEventListener: vi.fn(),
  removeEventListener: vi.fn(),
  dispatchEvent: vi.fn(),
}));

vi.stubGlobal("matchMedia", matchMediaMock);
