![Name](./documentation/images/immobiliare-labs.png)

# Gx Design System

[![Storybook](https://cdn.jsdelivr.net/gh/storybookjs/brand@main/badge/badge-storybook.svg)](https://getrix.pages.pepita.io/gx-design/)
[![Commitizen friendly](https://img.shields.io/badge/commitizen-friendly-brightgreen.svg?style=flat-square)](http://commitizen.github.io/cz-cli/)
[![pipeline status](https://gitlab.pepita.io/pepita/pepita-frontend/renovate/badges/develop/pipeline.svg)](https://gitlab.pepita.io/getrix/gx-design/commits/master)
[![Link to gestionale-fe-packages-gx-design in Backstage | Immobiliare Labs, Component: gestionale-fe-packages-gx-design](https://backstage.pepita.io/api/badges/entity/default/component/gestionale-fe-packages-gx-design/badge/pingback "Link to gestionale-fe-packages-gx-design in Backstage | Immobiliare Labs")](https://backstage.pepita.io/catalog/default/component/gestionale-fe-packages-gx-design)
[![Entity lifecycle badge, lifecycle: production](https://backstage.pepita.io/api/badges/entity/default/component/gestionale-fe-packages-gx-design/badge/lifecycle "Entity lifecycle badge")](https://backstage.pepita.io/catalog/default/component/gestionale-fe-packages-gx-design)
[![Entity owner badge, owner: b2b-web](https://backstage.pepita.io/api/badges/entity/default/component/gestionale-fe-packages-gx-design/badge/owner "Entity owner badge")](https://backstage.pepita.io/catalog/default/component/gestionale-fe-packages-gx-design)
[![Entity docs badge, docs: gestionale-fe-packages-gx-design](https://backstage.pepita.io/api/badges/entity/default/component/gestionale-fe-packages-gx-design/badge/docs "Entity docs badge")](https://backstage.pepita.io/catalog/default/component/gestionale-fe-packages-gx-design/docs)

## :books: Documentation

- [Install](.backstage/docs/Install.md)
- [Usage and Structure breakdown](.backstage/docs/Usage.md)
- [How to publish](.backstage/docs/HowToPublish.md)
- [Where it's published](.backstage/docs/WhereIsPublished.md)
- [Issues](.backstage/docs/Issues.md)
- [Changelog](./CHANGELOG.md)
- [Reference team](.backstage/docs/Team.md)
- [Credits](.backstage/docs/Credits.md)

## :package: Packages

| Package                           | Description                                                                                                                                                 |
| --------------------------------- | ----------------------------------------------------------------------------------------------------------------------------------------------------------- |
| `@gx-design/accordion`            | Accordion is an expandable table for showing nested hierarchies of information.                                                                             |
| `@gx-design/action-list`          | The Action List is a list component, the items of the list can contain icons or other elements. Action list are almost always wrapped with a `gx-dropdown`. |
| `@gx-design/addon-input`          | Addon inputs are inputs with left or right indicators, they can be icons or text and usually add additional informations about the input itself.            |
| `@gx-design/alert`                | An alert is an in-line message that attracts the user's attention without interrupting the user's task.                                                     |
| `@gx-design/avatar`               | Avatars are used in user tables and in the main header and contains the user profile image.                                                                 |
| `@gx-design/badge`                | To be used when critical information or an error must be communicated -> highlighted                                                                        |
| `@gx-design/button`               | Buttons allow users to take actions, and make choices, with a single tap.                                                                                   |
| `@gx-design/button-input`         | Button inputs are used to directly associate an action with a value in an input.                                                                            |
| `@gx-design/checkbox`             | Checkboxes are used for a list of options where the user may select multiple options, including all or none.                                                |
| `@gx-design/clearable-input`      | Clearable inputs are inputs that shows a button to reset themselves when they are filled with a value.                                                      |
| `@gx-design/dropdown`             | A dropdown menu displays a list of actions or options to a user.                                                                                            |
| `@gx-design/empty-state`          | An empty state appears when there is no data to display and describes what the user can do next.                                                            |
| `@gx-design/energetic-tag`        | Energetic Tags are used to display the Energetic category of a property.                                                                                    |
| `@gx-design/helper-text`          | An helper text is an element that provides instructions to the user after he filled an input.                                                               |
| `@gx-design/icon`                 | An icon is a visual representation of a command, device, directory, or common action.                                                                       |
| `@gx-design/icon-input`           | IconInput is used when you want to use an icon as label                                                                                                     |
| `@gx-design/illustration`         | Simple illustration component.                                                                                                                              |
| `@gx-design/input`                | A text field that allows a user to write or edit text.                                                                                                      |
| `@gx-design/list`                 | The List is a list component, the items of the list can contain icons or other elements.                                                                    |
| `@gx-design/modal`                | A Modal is a type of overlay that can be used for confirming actions, asking for disambiguation, and presenting small forms.                                |
| `@gx-design/notification-badge`   | NotificationBadge are used in user tables and in the main header and contains the user profile image.                                                       |
| `@gx-design/number-input`         | A number field that allows a user to write or edit a number.                                                                                                |
| `@gx-design/pager`                | The Pagination component enables the user to select a specific page from a range of pages.                                                                  |
| `@gx-design/pagination-bar`       | PaginationBar is used at the end of a Table that has more than 1 page of results.                                                                           |
| `@gx-design/popover`              | A Popover can be used to display some content on top of another.                                                                                            |
| `@gx-design/radio`                | Use radio buttons when the user needs to see all available options. If available options can be collapsed, consider using a Select component                |
| `@gx-design/select`               | Select allows users to make a single selection from a list of options.                                                                                      |
| `@gx-design/snackbar`             | A Snackbar is a short message that temporarily appears above the content to communicate the response of an action and/or show error warnings.               |
| `@gx-design/summary-item`         | SummaryItem component is used to present pairs of related information: a label and his value.                                                               |
| `@gx-design/tabs`                 | Tabs make it easy to explore and switch between different views.                                                                                            |
| `@gx-design/tag`                  | The Tag represent an element with a keyword that help label, organize, and categorize.                                                                      |
| `@gx-design/textarea`             | A text area lets users enter long form text which spans over multiple lines.                                                                                |
| `@gx-design/toolbar`              | A toolbar provides convenient access to frequently used commands and controls that perform actions relevant to the current view.                            |
| `@gx-design/tooltip`              | Tooltips show contextual help or information about specific components when a user hovers on them.                                                          |
| `@gx-design/use-forked-refs`      | A hook that can combine two refs(mutable or callbackRefs) into a single callbackRef                                                                         |
| `@gx-design/use-media-match`      | A hook to manage renders based on viewport size                                                                                                             |
| `@gx-design/use-on-click-outside` | A hook to run a function when a click happens outside of a given element                                                                                    |

## :beetle: Issues

You found a bug or need a new feature? Please <a href="https://gitlab.pepita.io/getrix/gx-design/-/issues/new" target="_blank">open an issue.</a>

## :handshake: Contributing

See [guide](https://gitlab.pepita.io/getrix/gx-design/-/blob/develop/CONTRIBUTING.md).

## :busts_in_silhouette: Reference Team

B2B Frontend Team.
