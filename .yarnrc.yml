nodeLinker: node-modules
npmPublishAccess: restricted
npmPublishRegistry: https://npm.pepita.io
npmRegistries:
  https://npm.pepita.io:
    npmAuthToken: /kBK/nSAH7tE9MTaVUQo0A4SKMECMJQwhd6t0Z4jo48M/AT84H6tudkpZpuSRJGEkh9msVOLoX6nA23ASZZM003cKjnBWw+wCioHV4TREs/TBu9z1By0tGWK7DmsciLozqaHzBpfSXT+0rL2w49w1ndVMVI5FaoQP44FCSCw48q2Zbp17TZGlcS/iR9aIoZG
npmRegistryServer: https://npm.pepita.io
plugins:
  - path: .yarn/plugins/@yarnpkg/plugin-interactive-tools.cjs
    spec: "@yarnpkg/plugin-interactive-tools"
  - path: .yarn/plugins/@yarnpkg/plugin-workspace-tools.cjs
    spec: "@yarnpkg/plugin-workspace-tools"
  - path: .yarn/plugins/@yarnpkg/plugin-typescript.cjs
    spec: "@yarnpkg/plugin-typescript"
yarnPath: .yarn/releases/yarn-3.8.0.cjs
