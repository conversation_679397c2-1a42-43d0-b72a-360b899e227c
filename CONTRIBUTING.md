# Contributing

These are a few guidelines for contributing to this project.

## Requirements

[Volta](https://docs.volta.sh/guide/)

## Install

```bash
# clone repository
$ <NAME_EMAIL>:getrix/gx-design.git
$ cd gx-design

# Install dependencies
$ yarn install

# Build packages
$ yarn build

# Start deveopment server with storybook
$ yarn storybook
```

## Develop

Now you can start working on packages, which are located under

```bash
packages/[componentName]
```

Wait for builder to compile code and watch changes directly on Storybook interface.
