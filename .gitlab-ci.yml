image: $CI_REGISTRY/qa/docker-images/node:20.11.0-buster

stages:
  - install-dependencies
  - test-linting
  - build
  - test
  - review
  - release
  - publish

# Install packages
install:
  stage: install-dependencies
  script:
    - yarn install
  artifacts:
    expire_in: 4h
    paths:
      - node_modules

# Check for code errors with <PERSON><PERSON>lint
test:
  stage: test-linting
  script:
    - yarn lint

# Run tests
include:
    - project: "pepita/pepita-frontend/shared-tools"
      ref: "@pepita-tools/vitest-gitlab-reporter@1.0.0"
      file: "ci/templates/vitest-gitlab-reporter.latest.gitlab-ci.yml"

test:
  extends:
    - .vitest-gitlab-reporter
  stage: test
  script:
    - yarn test run
  dependencies:
    - install
    - build

# Test if one of the following commands fail
build:
  stage: build
  script:
    - yarn run build
  artifacts:
    when: always
    expire_in: 1h
    paths:
      - "packages/*/dist"

# Deploy to: http://getrix.pages.rete.farm/gx-design/
pages:
  stage: publish
  script:
    # Prepare the storybok build
    - yarn build-storybook
    # Store static content into /public which is used by Gitlab pages to publish the folder
    - mv storybook-static public/
  only:
    - develop
  dependencies:
    - install
    - build
  artifacts:
    when: always
    expire_in: 1h
    paths:
      - public

### REVIEW
.sweepea_variables: &sweepea_variables
  K8S_DOMAIN: kube.dev.rm.ns.farm

.review_filters: &review_filters
  except:
    - master
    - tags

.sweepea_start_template: &sweepea_start_template
  script:
    - echo "Docker build and push image"
    - docker login -u gitlab-ci-token -p $CI_JOB_TOKEN $CI_REGISTRY
    - docker build --pull -f docker/sweepea/Dockerfile -t ${DOCKER_BUILD_IMAGE_TAG} .
    - docker push ${DOCKER_BUILD_IMAGE_TAG}
  after_script:
    - echo "Deploy a review app on k8s https://sp-${CI_PROJECT_NAME}-${CI_COMMIT_REF_SLUG}.${K8S_DOMAIN}"
    - >
      curl -X POST \
        -F "version"=v1.0.1 \
        -F "project=${CI_PROJECT_NAME}" \
        -F "branch=${CI_COMMIT_REF_SLUG}" \
        -F "image=${DOCKER_BUILD_IMAGE_TAG}" \
        http://sweepea.${K8S_DOMAIN}/deploy
  tags:
    - shell
    - docker
    - build

sweepea-review-deploy:
  stage: review
  image: $CI_REGISTRY/qa/docker-images/common/deployment-tools:1.0
  variables:
    DOCKER_BUILD_IMAGE_TAG: ${CI_REGISTRY_IMAGE}/sweepea-review-apps:${CI_COMMIT_REF_SLUG}
    <<: *sweepea_variables
  <<: *sweepea_start_template
  environment:
    name: sweepea-review-apps/${CI_PROJECT_NAME}-${CI_COMMIT_REF_SLUG}
    url: https://sp-${CI_PROJECT_NAME}-${CI_COMMIT_REF_SLUG}.${K8S_DOMAIN}
    on_stop: sweepea-review-stop
  dependencies:
    - build
  when: manual
  <<: *review_filters

sweepea-review-stop:
  stage: review
  image: $CI_REGISTRY/qa/docker-images/common/deployment-tools:1.0
  variables:
    GIT_STRATEGY: none
    <<: *sweepea_variables
  script:
    - echo "Remove review app from k8s"
    - >
      curl -X POST \
        -F "version"=v1.0.1 \
        -F "project=${CI_PROJECT_NAME}" \
        -F "branch=${CI_COMMIT_REF_SLUG}" \
        http://sweepea.${K8S_DOMAIN}/stop
  environment:
    name: sweepea-review-apps/${CI_PROJECT_NAME}-${CI_COMMIT_REF_SLUG}
    action: stop
  dependencies: []
  when: manual
  <<: *review_filters

prerelease:
  stage: release
  image: $CI_REGISTRY/pepita/automation/pepita-release:v22.10.4
  variables:
    GIT_STRATEGY: clone
  script:
    - pepita-release git checkout $CI_COMMIT_REF_NAME
    - pepita-release git pull origin $CI_COMMIT_REF_NAME
    - yarn --immutable
    - yarn run build-publish
    - yarn run next
    - pepita-release gitlab:note || true # if no release is created this will fail
    - pepita-release notify:release --monorepo=independant
    - pepita-release git push origin $CI_COMMIT_REF_NAME
  except:
    - tags
  when: manual

release:
  stage: release
  image: $CI_REGISTRY/pepita/automation/pepita-release:v22.10.4
  variables:
    GIT_STRATEGY: clone
  script:
    - pepita-release git checkout $CI_COMMIT_REF_NAME
    - pepita-release git pull origin $CI_COMMIT_REF_NAME
    - yarn --immutable
    - yarn run build-publish
    - yarn run release
    - pepita-release gitlab:note || true # if no release is created this will fail
    - pepita-release notify:release --monorepo=independant
    - pepita-release git push origin $CI_COMMIT_REF_NAME
    - pepita-release git:backmerge --from=master --to=develop
  only:
    - master
  except:
    - tags
  when: manual
