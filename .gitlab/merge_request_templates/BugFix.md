/assign @ciabatta @vvitale
/assign me
/reviewer @b2b/frontend

## Bug Fix | Argomento del commit

Breve descrizione tecnica della merge request

---

| Q               |                                       A                                       |
| --------------- | :---------------------------------------------------------------------------: |
| New feature?    |                             :white_large_square:                              |
| Bug fix?        |                            :ballot_box_with_check:                            |
| BC breaks?[^1]  |                             :white_large_square:                              |
| Deprecations?   |                             :white_large_square:                              |
| Ticket/Task[^2] | - <!-- esempio: [DEV-1234](https://indomio.atlassian.net/browse/DEV-1234) --> |
| Doc PR[^3]      |     - <!-- esempio: [DOC](https://indomio.atlassian.net/l/cp/abcdef) -->      |

[^1]: Indicare se rompe la compatibilità non solo con le versioni dello stesso software ma anche con quello dipendente
[^2]: Riferimento al ticket sul sistema di ticketing o alla issue/task/user-story/epica di Jira
[^3]: Link ad attività su team correlate e pagine di documentazione sempre su team
