# Change Log

All notable changes to this project will be documented in this file.
See [Conventional Commits](https://conventionalcommits.org) for commit guidelines.

# [2.0.0-alpha.15](https://gitlab.pepita.io/getrix/gx-design/compare/v2.0.0-alpha.14...v2.0.0-alpha.15) (2022-07-12)


### Bug Fixes

* **lock:** yarn lock ([af9827d](https://gitlab.pepita.io/getrix/gx-design/commit/af9827d79c4e2a723478dc91b0c6de5493bebec2))





# [2.0.0-alpha.14](https://gitlab.pepita.io/getrix/gx-design/compare/v2.0.0-alpha.13...v2.0.0-alpha.14) (2022-07-08)


### Bug Fixes

* **lock:** yarn ([9f0f388](https://gitlab.pepita.io/getrix/gx-design/commit/9f0f388a9bcf0b0767a134d2fa32747087a49421))
* **publish:** remove force publish + iconset fix ([137cf32](https://gitlab.pepita.io/getrix/gx-design/commit/137cf324c922c3530ccf4b31f638b2165fc905d6))





# [2.0.0-alpha.13](https://gitlab.pepita.io/getrix/gx-design/compare/v2.0.0-alpha.12...v2.0.0-alpha.13) (2022-07-06)


### Bug Fixes

* **build:** build script before publish ([1ab960d](https://gitlab.pepita.io/getrix/gx-design/commit/1ab960d002d1539f161f18e0cb03ec8e0f99f0ba))
* **build:** build script before publish ([16863c2](https://gitlab.pepita.io/getrix/gx-design/commit/16863c2b62bbc4f87cae253a26876268ba6fde46))
* **lock:** yarn lock ([ffb6084](https://gitlab.pepita.io/getrix/gx-design/commit/ffb6084af286f800f25d17a4a16e389d3d23b3b2))





# [2.0.0-alpha.12](https://gitlab.pepita.io/getrix/gx-design/compare/v1.2.3...v2.0.0-alpha.12) (2022-07-06)


### Bug Fixes

* **css:** removed additional css ([19333a3](https://gitlab.pepita.io/getrix/gx-design/commit/19333a30c005740c0fd7089b826fc6f54fbc71dc))
* **lock:** yarn lock ([c908c63](https://gitlab.pepita.io/getrix/gx-design/commit/c908c63fbcf31d6675716a631bf93fb9f10e7fc3))
* **lock:** yarn lock fix ([6c728ef](https://gitlab.pepita.io/getrix/gx-design/commit/6c728ef37af8be9f25eabd684abd3d9e7369cccd))


### Features

* **badge:** badge changes ([dca56a5](https://gitlab.pepita.io/getrix/gx-design/commit/dca56a50a14f6a18b51dbb14051fe749de99422c))
* **pager:** pager and pagination bar ([e8657c5](https://gitlab.pepita.io/getrix/gx-design/commit/e8657c54e5b7037059362046045a42ea7a23ca0b))





# [2.0.0-alpha.11](https://gitlab.pepita.io/getrix/gx-design/compare/v2.0.0-alpha.10...v2.0.0-alpha.11) (2022-06-23)

**Note:** Version bump only for package gx-design





# [2.0.0-alpha.10](https://gitlab.pepita.io/getrix/gx-design/compare/v2.0.0-alpha.9...v2.0.0-alpha.10) (2022-06-23)

**Note:** Version bump only for package gx-design





# [2.0.0-alpha.9](https://gitlab.pepita.io/getrix/gx-design/compare/v2.0.0-alpha.8...v2.0.0-alpha.9) (2022-06-23)

**Note:** Version bump only for package gx-design





# [2.0.0-alpha.8](https://gitlab.pepita.io/getrix/gx-design/compare/v2.0.0-alpha.7...v2.0.0-alpha.8) (2022-06-22)

**Note:** Version bump only for package gx-design





# [2.0.0-alpha.7](https://gitlab.pepita.io/getrix/gx-design/compare/v2.0.0-alpha.6...v2.0.0-alpha.7) (2022-06-22)


### Bug Fixes

* **yarn:** removed private ([61fd5d8](https://gitlab.pepita.io/getrix/gx-design/commit/61fd5d8c3690e96d47a915aed157df285b2052b0))
* **yarn:** yarn registry ([f0e8ddb](https://gitlab.pepita.io/getrix/gx-design/commit/f0e8ddb2dd91af5b1629e123330c4613b78defb3))
* **yarn:** yarn version ([3a99047](https://gitlab.pepita.io/getrix/gx-design/commit/3a9904734eab997aacc3e32c2e3048dac7d0b818))





# [2.0.0-alpha.6](https://gitlab.pepita.io/getrix/gx-design/compare/v2.0.0-alpha.5...v2.0.0-alpha.6) (2022-06-22)

**Note:** Version bump only for package gx-design





# [2.0.0-alpha.5](https://gitlab.pepita.io/getrix/gx-design/compare/v1.2.3...v2.0.0-alpha.5) (2022-06-22)


### Bug Fixes

* **descriptions:** fixed some descriptions ([f0e1cb6](https://gitlab.pepita.io/getrix/gx-design/commit/f0e1cb6c6fb41e799a74aed67844b944c5cba4df))
* **energetic-tag:** enrgetic tag name ([c6d4150](https://gitlab.pepita.io/getrix/gx-design/commit/c6d415000fabe29654343b816c79abeadf8a5b2c))
* **popover:** popover props descriptions ([b4d5fda](https://gitlab.pepita.io/getrix/gx-design/commit/b4d5fdacd94e9072613324e36db9b464b667791c))
* **popover:** story documentation ([e53f489](https://gitlab.pepita.io/getrix/gx-design/commit/e53f4898d2e4eb1422838da9a22f0824b090b39f))
* **storybook:** some fixes to stories ([32693f0](https://gitlab.pepita.io/getrix/gx-design/commit/32693f0dc2f7e36c25850c7bb00864b484610adb))


### Features

* **action-list:** action list as package ([6374ee4](https://gitlab.pepita.io/getrix/gx-design/commit/6374ee4502191823c9ddb6b13cbfedb9bcfc468c))
* **action-list:** props decription ([d9117e0](https://gitlab.pepita.io/getrix/gx-design/commit/d9117e0215a00e42979977864b644faa9ae3b231))
* **alert:** alert component ([1d253e9](https://gitlab.pepita.io/getrix/gx-design/commit/1d253e9f32b7d5a398e0086748c3ad2bdbde45f3))
* **badge:** badge as package ([a359640](https://gitlab.pepita.io/getrix/gx-design/commit/a359640c26feab8638cfe04b905745d24c01c798))
* **badge:** badge changes ([dca56a5](https://gitlab.pepita.io/getrix/gx-design/commit/dca56a50a14f6a18b51dbb14051fe749de99422c))
* **common:** update common ([b8cea4c](https://gitlab.pepita.io/getrix/gx-design/commit/b8cea4cb54498d93bfa0a2033e6735b2424ce4b3))
* **helper-text:** helper text as package ([deb05ed](https://gitlab.pepita.io/getrix/gx-design/commit/deb05ed490382c9a7b762c230390a76ce9a3842f))
* lerna ([d2acfcb](https://gitlab.pepita.io/getrix/gx-design/commit/d2acfcb44f9ffb8b0044362be8d367721ae19dee))
* **popover:** popover as package done ([2f47847](https://gitlab.pepita.io/getrix/gx-design/commit/2f47847caa5d2848ffd46b878f4889c006a44ac3))
* **popover:** popover start ([62f324a](https://gitlab.pepita.io/getrix/gx-design/commit/62f324afdd7b3ec1f73a26f97a79f8d222e63809))
* **tabs:** tabs as package ([0fc7816](https://gitlab.pepita.io/getrix/gx-design/commit/0fc7816f5bdd226c7fa5869e2e6d96ac7cc7f10f))


### BREAKING CHANGES

* adds lerna repo management





# [2.0.0-alpha.4](https://gitlab.pepita.io/getrix/gx-design/compare/v1.2.3...v2.0.0-alpha.4) (2022-06-15)


### Features

* **alert:** alert component ([1d253e9](https://gitlab.pepita.io/getrix/gx-design/commit/1d253e9f32b7d5a398e0086748c3ad2bdbde45f3))
* **badge:** badge changes ([dca56a5](https://gitlab.pepita.io/getrix/gx-design/commit/dca56a50a14f6a18b51dbb14051fe749de99422c))
* lerna ([29b12e6](https://gitlab.pepita.io/getrix/gx-design/commit/29b12e6e7d3f245d410915de3ceb7f90248f21b4))


### BREAKING CHANGES

* adds lerna repo management





# [2.0.0-alpha.3](https://gitlab.pepita.io/getrix/gx-design/compare/v2.0.0-alpha.2...v2.0.0-alpha.3) (2022-06-15)


### Features

* pretend to have a feat ([1c25c5c](https://gitlab.pepita.io/getrix/gx-design/commit/1c25c5c0bbfadb3ce0429d6bc9a50e1e504d5e12))





# [2.0.0-alpha.2](https://gitlab.pepita.io/getrix/gx-design/compare/v2.0.0-alpha.1...v2.0.0-alpha.2) (2022-06-15)


### Features

* pretend to have a feat ([35a3308](https://gitlab.pepita.io/getrix/gx-design/commit/35a330807b8f12c0c25f09fd8abee6cdb191fa78))





# [2.0.0-alpha.1](https://gitlab.pepita.io/getrix/gx-design/compare/v2.0.0-alpha.0...v2.0.0-alpha.1) (2022-06-15)


### Features

* remove yarn ([e62e6b4](https://gitlab.pepita.io/getrix/gx-design/commit/e62e6b44541dd938221ced63c13dc9b64b18ee7f))





# [2.0.0-alpha.0](https://gitlab.pepita.io/getrix/gx-design/compare/v1.2.3...v2.0.0-alpha.0) (2022-06-15)


### Features

* **alert:** alert component ([1d253e9](https://gitlab.pepita.io/getrix/gx-design/commit/1d253e9f32b7d5a398e0086748c3ad2bdbde45f3))
* **badge:** badge changes ([dca56a5](https://gitlab.pepita.io/getrix/gx-design/commit/dca56a50a14f6a18b51dbb14051fe749de99422c))
* lerna ([6612f21](https://gitlab.pepita.io/getrix/gx-design/commit/6612f2107c71b52281b47052b3e994939cc0efe7))
* using yarnrc ([36c6c32](https://gitlab.pepita.io/getrix/gx-design/commit/36c6c327d87e0ed1dc7719d051198649796a2be6))


### BREAKING CHANGES

* adds lerna repo management





# Changelog

All notable changes to this project will be documented in this file. See [standard-version](https://github.com/conventional-changelog/standard-version) for commit guidelines.

### [1.2.3](https://gitlab.pepita.io/getrix/gx-design/compare/v1.2.2...v1.2.3) (2022-05-30)


### Bug Fixes

* **scss:** remved bootstrap and add reset ([7c15556](https://gitlab.pepita.io/getrix/gx-design/commit/7c1555623f7126c11de31bfe43e1300375ded57e))

### [1.2.2](https://gitlab.pepita.io/getrix/gx-design/compare/v1.2.1...v1.2.2) (2022-05-24)


### Bug Fixes

* **scss:** css imports ([193e3e1](https://gitlab.pepita.io/getrix/gx-design/commit/193e3e14f30a0d1fa65c8971158e399c9ef14dd8))

### [1.2.1](https://gitlab.pepita.io/getrix/gx-design/compare/v1.2.0...v1.2.1) (2022-05-23)


### CI/CD

* **pepita-release:** v11 ([7d5edcb](https://gitlab.pepita.io/getrix/gx-design/commit/7d5edcb4c6300205d97e90def33d6857ac8ec2f5))

## [1.2.0](https://gitlab.pepita.io/getrix/gx-design/compare/v1.1.0...v1.2.0) (2022-03-23)


### Features

* **forms:** layout forms section ([a406c16](https://gitlab.pepita.io/getrix/gx-design/commit/a406c1672de1d28fad24fddd158311b65bcdc990))
* **hidden:** hidden classes ([9ae66f1](https://gitlab.pepita.io/getrix/gx-design/commit/9ae66f19e8d42d9f787029d961be0ae9176e2dde))
* **input-number:** input number ([55cff2b](https://gitlab.pepita.io/getrix/gx-design/commit/55cff2bc758f276f7a580e621c6095be0767a4d3))
* **layout:** grid ([8bac2b9](https://gitlab.pepita.io/getrix/gx-design/commit/8bac2b9c7847e5c66d8f8d2dcb0f2d4e0145331c))
* **layout:** grid ([d598e1d](https://gitlab.pepita.io/getrix/gx-design/commit/d598e1d56fb954e7e596ae8aac61f3d821f2cd95))
* **table:** table component ([4a77d74](https://gitlab.pepita.io/getrix/gx-design/commit/4a77d7457fdeb194eb8182c25d4a53d58f0260b9))


### Bug Fixes

* **favicon:** favicon path changes ([ef54f9c](https://gitlab.pepita.io/getrix/gx-design/commit/ef54f9cbd0164dbacb5ac1ebfd1a5256d8e1dff1))
* **layout:** modificati grid e hidden ([cd71254](https://gitlab.pepita.io/getrix/gx-design/commit/cd712540c63de327de6c6ed8c7d185021eb2360b))

## [1.1.0](https://gitlab.pepita.io/getrix/gx-design/compare/v1.0.0...v1.1.0) (2022-03-09)


### Features

* **changelog:** add changelog page ([59ce15a](https://gitlab.pepita.io/getrix/gx-design/commit/59ce15af0c980b8f0a9ccf35bd7d1eb924a18252))
* **switch:** done ([df02ffa](https://gitlab.pepita.io/getrix/gx-design/commit/df02ffad2cd2ec09648d5bf816d081156357a80f))
* **switch:** done ([d3a9bb8](https://gitlab.pepita.io/getrix/gx-design/commit/d3a9bb876313e16270ea2ee720b6447ebb3afa3b))
* **switch:** switch ui and logics ([f19e15a](https://gitlab.pepita.io/getrix/gx-design/commit/f19e15abdbd72339f7ffddee6f3087fbc609b856))


### Bug Fixes

* **misc:** cleanup old addon + new api staticDirs ([90f35d6](https://gitlab.pepita.io/getrix/gx-design/commit/90f35d6b44f539e10c837b729aa88604c410cd2d))

## [1.0.0](https://gitlab.pepita.io/getrix/gx-design/compare/v0.1.7...v1.0.0) (2022-03-07)


### Features

* **input-tooltipo:** add tooltip badge to label's input ([db7000f](https://gitlab.pepita.io/getrix/gx-design/commit/db7000f91dea07d9724f9557d2d915a2906daa36))
* **popover:** popover large variant ([bf6afff](https://gitlab.pepita.io/getrix/gx-design/commit/bf6afff630fd418cb19da2ccccd395e451b29bfa))
* **popover:** popover rewrite ([0221901](https://gitlab.pepita.io/getrix/gx-design/commit/02219017ddfbf9f73ab23af13a2d6453b6b18203))
* **popover:** popover rewrite ([31dd891](https://gitlab.pepita.io/getrix/gx-design/commit/31dd891baa6431bbf68bd0621d516f50baca6687))
* **readme:** readme update ([22ceda9](https://gitlab.pepita.io/getrix/gx-design/commit/22ceda993c74ff0ec38cded284f4c291eb0c1d17))
* **release:** v1.0.0 ([5781ab1](https://gitlab.pepita.io/getrix/gx-design/commit/5781ab1da075457379e513a3bd87ce7511f3473d))
* **shadows:** foundation ([7e3ff4f](https://gitlab.pepita.io/getrix/gx-design/commit/7e3ff4febe3c34c7a45f5563e091c1217cd32572))
* **tooltip:** tooltip rewrite ([ff8eacf](https://gitlab.pepita.io/getrix/gx-design/commit/ff8eacf90d26b97dfe298b3bfed683d507bec40b))
* **tooltip:** tooltip rewrite ([d68b0ab](https://gitlab.pepita.io/getrix/gx-design/commit/d68b0ab09bdadcec346c2341c0fc31a13b0ae088))
* **tooltip:** tooltip rewrite ([23d5e64](https://gitlab.pepita.io/getrix/gx-design/commit/23d5e64e06aba1f271fc42218e85cb430c12f15a))
* **tooltip:** tooltip start ([922ae73](https://gitlab.pepita.io/getrix/gx-design/commit/922ae73e95743feb49801e50dc7750d1563aa59a))
* **tootlip:** tooltip rewrite ([644908d](https://gitlab.pepita.io/getrix/gx-design/commit/644908da49da01ce64a0e7a3c737c868ceadb8bc))


### Bug Fixes

* **intro:** removed intro page ([da16704](https://gitlab.pepita.io/getrix/gx-design/commit/da167041f917b2119c2fb2108128ea4620b12d48))
* **readme:** readme fix ([1281fd8](https://gitlab.pepita.io/getrix/gx-design/commit/1281fd83d657a67c34797de667b3f08d488a17f6))
* **source:** transform source ([de6bde1](https://gitlab.pepita.io/getrix/gx-design/commit/de6bde11072540add90332212536b4c5796a6850))

### [0.1.7](https://gitlab.pepita.io/getrix/gx-design/compare/v0.1.6...v0.1.7) (2022-02-16)


### Features

* **foundation:** add intro pages ([39053b3](https://gitlab.pepita.io/getrix/gx-design/commit/39053b399b160b660c6cb204288c28d8a4545469))

### [0.1.6](https://gitlab.pepita.io/getrix/gx-design/compare/v0.1.5...v0.1.6) (2022-02-11)


### Bug Fixes

* **ci:** realease bot only on tags ([93469fa](https://gitlab.pepita.io/getrix/gx-design/commit/93469fa12cfcf5377ccbf05d1634cad89aeb3e65))

### [0.1.5](https://gitlab.pepita.io/getrix/gx-design/compare/v0.1.4...v0.1.5) (2022-02-11)


### Features

* **logo:** aggiunto logo storybook ([fa0d0d2](https://gitlab.pepita.io/getrix/gx-design/commit/fa0d0d2e7beaf749b8778de89c7bb337173a385d))
* **version:** versione e logo ([35f8816](https://gitlab.pepita.io/getrix/gx-design/commit/35f88165a581f4ee8d6b65986c93ad6761cb318b))

### [0.1.4](https://gitlab.pepita.io/getrix/gx-design/compare/v0.1.3...v0.1.4) (2022-02-09)


### Bug Fixes

* **tag:** text as prop ([0b775be](https://gitlab.pepita.io/getrix/gx-design/commit/0b775beed18e3fe0acb7167463c4a8ddc150d139))

### [0.1.3](https://gitlab.pepita.io/getrix/gx-design/compare/v0.1.2...v0.1.3) (2022-02-08)


### Features

* **immoPro:** add immopro switch to components ([d753cd9](https://gitlab.pepita.io/getrix/gx-design/commit/d753cd9694fd967e06d7910e94ce06efd51f6e38))
* **tag-input:** add tag input component ([668b223](https://gitlab.pepita.io/getrix/gx-design/commit/668b223310331a93326c458dc8b38b1ac1b52c2b))
* **tag-input:** tag input component ([8ad166e](https://gitlab.pepita.io/getrix/gx-design/commit/8ad166efb3ec158e0ac4fec3e3fe2b4b7c27772b))


### Bug Fixes

* **artifact:** artifact expire time ([c46cb1c](https://gitlab.pepita.io/getrix/gx-design/commit/c46cb1cfab336c438ce25682b779c2174e5426f9))
* **radio:** css naming ([e6c5f3a](https://gitlab.pepita.io/getrix/gx-design/commit/e6c5f3ae5d8428c77f96e2427be7de84138d5d7c))
* **radio:** error story ([3f57a80](https://gitlab.pepita.io/getrix/gx-design/commit/3f57a80d3a0dae505098a60f7d594b1869bc901f))
* **radio:** prop order ([b5fd321](https://gitlab.pepita.io/getrix/gx-design/commit/b5fd3217a09bce3216333e7a30b1a1268181151a))
* **radio:** radio input component and stories fix ([3b5fd8d](https://gitlab.pepita.io/getrix/gx-design/commit/3b5fd8da1d08a91ba4380d60a2e26ed0737b82f6))
* **radio:** removed notes ([eb46284](https://gitlab.pepita.io/getrix/gx-design/commit/eb4628449b93ff8e69932e69f9a1a8bade4325c2))
* **radio:** starting to fix radio component ([db5e5b2](https://gitlab.pepita.io/getrix/gx-design/commit/db5e5b276eed269151b6a18eb2aa9f8e7851aaeb))
* **readme:** readme fix ([82283a9](https://gitlab.pepita.io/getrix/gx-design/commit/82283a9fb330a6707273c547677fda0b45b60f4f))
* **readme:** readme fix ([02dfb5c](https://gitlab.pepita.io/getrix/gx-design/commit/02dfb5cafc2065f57b250704c84ba0091738fc5b))
* **readme:** readme fix ([ad60d37](https://gitlab.pepita.io/getrix/gx-design/commit/ad60d37f8b09e8d0025fa17835b3f568a854525a))
* **readme:** removed nvm ([f060cbc](https://gitlab.pepita.io/getrix/gx-design/commit/f060cbc0b634acd6bc77ef303fc811e1ff62414b))
* **render:** fixed story rendering ([83d814e](https://gitlab.pepita.io/getrix/gx-design/commit/83d814e77580386e6a0d623165e9cf328dedbfda))
* **storybook:** show correct source code as html ([fe560eb](https://gitlab.pepita.io/getrix/gx-design/commit/fe560ebce8b08606beb6e3715a7738802f2a6ee0))
* **tag-input:** new variant transparent ([036fcad](https://gitlab.pepita.io/getrix/gx-design/commit/036fcada6df3bfda4025279fce16554d0da70fce))

### [0.1.2](https://gitlab.pepita.io/getrix/gx-design/compare/v0.1.1...v0.1.2) (2022-01-26)


### Bug Fixes

* **release:** release notification channel ([a1a0852](https://gitlab.pepita.io/getrix/gx-design/commit/a1a0852a094d7fe242e2931c27c44164bd2c71f0))
* **release:** release notification channel ([7643c51](https://gitlab.pepita.io/getrix/gx-design/commit/7643c5129257cd1cfb22474db277f84f37080b72))
* **source-code:** source code as HTML ([a23c5a2](https://gitlab.pepita.io/getrix/gx-design/commit/a23c5a221ab55d99df38259158575f7481ff5b1e))

### 0.1.1 (2022-01-25)


### Features

* **mr:** template ([d00f0fb](https://gitlab.pepita.io/getrix/gx-design/commit/d00f0fbb9572cc46f582f6133724b76a7a181fb8))
* **readme:** readme created ([e4bab11](https://gitlab.pepita.io/getrix/gx-design/commit/e4bab11a88855cd9ade636dd6509121ce4264124))
