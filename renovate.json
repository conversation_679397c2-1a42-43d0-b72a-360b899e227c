{"$schema": "https://docs.renovatebot.com/renovate-schema.json", "assignees": [], "lockFileMaintenance": {"enabled": false}, "enabled": false, "vulnerabilityAlerts": {"enabled": true}, "git-submodules": {"enabled": true}, "extends": ["config:recommended", ":semanticCommits", ":preserveSemverRanges", ":separateMajorReleases", ":combinePatchMinorReleases", ":ignoreUnstable", ":prImmediately", ":semanticPrefixFixDepsChoreOthers", ":updateNotScheduled", ":automergeDisabled", ":ignoreModulesAndTests", "helpers:disableTypesNodeMajor", "group:monorepos", "group:definitelyTyped", "group:illuminate", "group:postcss", "group:linters", "group:test", "group:recommended"]}